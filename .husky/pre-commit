#!/bin/sh

# TMP_LOG=".lint-md-error.log"

# RED='\033[0;31m'
# GREEN='\033[0;32m'
# YELLOW='\033[0;33m'
# CYAN='\033[0;36m'
# RESET='\033[0m'

# npx lint-staged --quiet 2>&1 | tee "$TMP_LOG"
# LINT_EXIT_CODE=${PIPESTATUS[0]:-${?}}

# if [ "$LINT_EXIT_CODE" -ne 0 ]; then
#   printf "\n${RED}❌ Markdown 文档校验未通过${RESET}\n"
#   printf "${YELLOW}以下是出错的文件及对应规则，请及时修复：${RESET}\n"
#   printf "${CYAN}---------------------------------------------${RESET}\n"
#   grep -E '^[^:]+\.md:' "$TMP_LOG"
#   printf "${CYAN}---------------------------------------------${RESET}\n"
#   printf "${YELLOW}📌 提示：${RESET}\n"
#   printf "  👉 可尝试执行：${CYAN}pnpm lint:fix${RESET} 自动修复\n"
#   printf "  👉 若仍有问题，请手动修复后再提交\n"
#   printf "  👉 [MD0XX]相关错误规则说明请查看：${CYAN}.markdownlint.jsonc${RESET}\n\n"
#   rm "$TMP_LOG"
#   exit 1
# else
#   rm "$TMP_LOG"
#   printf "${GREEN}✅ Markdown 文档校验通过，继续提交...${RESET}\n"
# fi
