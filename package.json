{"scripts": {"docs:dev": "vuepress dev docs", "docs:build": "git pull && vuepress build docs", "docs:build:ci": "vuepress build docs", "lint:md": "markdownlint-cli2", "lint:fix": "markdownlint-cli2 --fix", "prepare": "husky"}, "devDependencies": {"@vuepress/bundler-vite": "2.0.0-rc.19", "@vuepress/plugin-watermark": "2.0.0-rc.69", "@vuepress/theme-default": "2.0.0-rc.69", "husky": "^9.1.7", "lint-staged": "^15.4.3", "markdown-it-for-inline": "^2.0.1", "markdownlint-cli2": "^0.17.2", "sass-embedded": "^1.83.1", "vue": "^3.5.13", "vuepress": "2.0.0-rc.19"}, "dependencies": {"scss": "^0.2.4"}}