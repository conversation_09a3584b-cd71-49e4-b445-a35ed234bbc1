// markdownlint 官方规则
{
  // 所有规则的默认状态
  "default": true,

  // 要继承的配置文件路径
  "extends": null,

  // MD001/heading-increment : 标题层级应逐级递增 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md001.md
  "MD001": false,

  // MD003/heading-style : 标题样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md003.md
  "MD003": {
    // 标题样式
    "style": "consistent"
  },

  // MD004/ul-style : 无序列表样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md004.md
  "MD004": {
    // 列表样式
    "style": "consistent"
  },

  // MD005/list-indent : 同级列表项缩进不一致 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md005.md
  "MD005": true,

  // MD007/ul-indent : 无序列表缩进 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md007.md
  "MD007": {
    // 缩进空格数
    "indent": 2,
    // 是否缩进列表第一级
    "start_indented": false,
    // 第一级缩进空格数（当 start_indented 启用时）
    "start_indent": 2
  },

  // MD009/no-trailing-spaces : 行尾空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md009.md
  "MD009": {
    // 换行允许的空格数
    "br_spaces": 2,
    // 允许列表项中的空行包含空格
    "list_item_empty_lines": false,
    // 严格模式检查不必要的换行
    "strict": false
  },

  // MD010/no-hard-tabs : 硬制表符 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md010.md
  "MD010": {
    // 检查代码块
    "code_blocks": true,
    // 要忽略的围栏代码语言
    "ignore_code_languages": [],
    // 每个硬制表符对应的空格数
    "spaces_per_tab": 1
  },

  // MD011/no-reversed-links : 反向链接语法 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md011.md
  "MD011": true,

  // MD012/no-multiple-blanks : 连续空行 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md012.md
  "MD012": {
    // 允许的最大连续空行数
    "maximum": 1
  },

  // MD013/line-length : 行长度限制 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md013.md
  "MD013": {
    // 最大字符数
    "line_length": 800,
    // 标题行最大字符数
    "heading_line_length": 120,
    // 代码块行最大字符数
    "code_block_line_length": 800,
    // 检查代码块
    "code_blocks": true,
    // 检查表格
    "tables": true,
    // 检查标题
    "headings": true,
    // 严格长度检查
    "strict": false,
    // 宽松长度检查
    "stern": false
  },

  // MD014/commands-show-output : 命令前美元符号未显示输出 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md014.md
  "MD014": true,

  // MD018/no-missing-space-atx : ATX 标题井号后缺少空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md018.md
  "MD018": true,

  // MD019/no-multiple-space-atx : ATX 标题井号后多个空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md019.md
  "MD019": true,

  // MD020/no-missing-space-closed-atx : 闭合 ATX 标题井号内缺少空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md020.md
  "MD020": true,

  // MD021/no-multiple-space-closed-atx : 闭合 ATX 标题井号内多个空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md021.md
  "MD021": true,

  // MD022/blanks-around-headings : 标题周围需有空行 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md022.md
  "MD022": {
    // 标题上方空行数
    "lines_above": 1,
    // 标题下方空行数
    "lines_below": 1
  },

  // MD023/heading-start-left : 标题必须从行首开始 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md023.md
  "MD023": true,

  // MD024/no-duplicate-heading : 重复标题内容 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md024.md
  "MD024": false,

  // MD025/single-title/single-h1 : 单文件多个顶级标题 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md025.md
  "MD025": false,

  // MD026/no-trailing-punctuation : 标题结尾标点符号 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md026.md
  "MD026": {
    // 禁止的标点符号
    "punctuation": ".,;:!。，；：！"
  },

  // MD027/no-multiple-space-blockquote : 块引用符号后多个空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md027.md
  "MD027": true,

  // MD028/no-blanks-blockquote : 块引用内空行 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md028.md
  "MD028": true,

  // MD029/ol-prefix : 有序列表前缀格式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md029.md
  "MD029": {
    // 列表样式
    "style": "one_or_ordered"
  },

  // MD030/list-marker-space : 列表标记后空格数 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md030.md
  "MD030": {
    // 单行无序列表空格数
    "ul_single": 1,
    // 单行有序列表空格数
    "ol_single": 1,
    // 多行无序列表空格数
    "ul_multi": 1,
    // 多行有序列表空格数
    "ol_multi": 1
  },

  // MD031/blanks-around-fences : 围栏代码块周围需有空行 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md031.md
  "MD031": {
    // 包含列表项
    "list_items": true
  },

  // MD032/blanks-around-lists : 列表周围需有空行 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md032.md
  "MD032": true,

  // MD033/no-inline-html : 禁用行内 HTML : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md033.md
  "MD033": {
    // 允许的 HTML 元素
    "allowed_elements": ["br", "img", "span", "p", "a", "star"]
  },

  // MD034/no-bare-urls : 禁止裸露的 URL : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md034.md
  "MD034": true,

  // MD035/hr-style : 水平线样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md035.md
  "MD035": {
    // 水平线样式
    "style": "consistent"
  },

  // MD036/no-emphasis-as-heading : 禁止用强调代替标题 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md036.md
  "MD036": false,

  // MD037/no-space-in-emphasis : 强调标记内空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md037.md
  "MD037": true,

  // MD038/no-space-in-code : 代码跨度元素内空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md038.md
  "MD038": true,

  // MD039/no-space-in-links : 链接文本内空格 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md039.md
  "MD039": true,

  // MD040/fenced-code-language : 围栏代码块需指定语言 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md040.md
  "MD040": {
    // 允许的语言列表
    "allowed_languages": [],
    // 仅允许语言标识
    "language_only": false
  },

  // MD041/first-line-heading/first-line-h1 : 文件首行应为顶级标题 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md041.md
  "MD041": false,

  // MD042/no-empty-links : 禁止空链接 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md042.md
  "MD042": true,

  // MD043/required-headings : 强制标题结构 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md043.md
  "MD043": false,

  // MD044/proper-names : 专有名词大小写 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md044.md
  "MD044": {
    // 专有名词列表
    "names": [],
    // 检查代码块
    "code_blocks": true,
    // 检查 HTML 元素
    "html_elements": true
  },

  // MD045/no-alt-text : 图片需有替代文本 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md045.md
  "MD045": false,

  // MD046/code-block-style : 代码块样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md046.md
  "MD046": {
    // 代码块样式
    "style": "consistent"
  },

  // MD047/single-trailing-newline : 文件末尾需有单个换行符 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md047.md
  "MD047": true,

  // MD048/code-fence-style : 代码围栏样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md048.md
  "MD048": {
    // 代码围栏样式
    "style": "consistent"
  },

  // MD049/emphasis-style : 强调样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md049.md
  "MD049": {
    // 强调样式
    "style": "consistent"
  },

  // MD050/strong-style : 加粗样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md050.md
  "MD050": {
    // 加粗样式
    "style": "consistent"
  },

  // MD051/link-fragments : 链接锚点有效性 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md051.md
  "MD051": false,

  // MD052/reference-links-images : 引用式链接/图片需定义 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md052.md
  "MD052": {
    // 包含快捷语法
    "shortcut_syntax": false
  },

  // MD053/link-image-reference-definitions : 链接/图片引用需被使用 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md053.md
  "MD053": {
    // 忽略的引用定义
    "ignored_definitions": ["//"]
  },

  // MD054/link-image-style : 链接/图片样式 : https://github.com/DavidAnson/markdownlint/blob/v0.32.1/doc/md054.md
  "MD054": {
    // 允许自动链接
    "autolink": true,
    // 允许行内链接/图片
    "inline": true,
    // 允许完整引用链接/图片
    "full": true,
    // 允许折叠引用链接/图片
    "collapsed": true,
    // 允许快捷引用链接/图片
    "shortcut": true,
    // 允许行内 URL 链接
    "url_inline": true
  },

  // 表管风格: https://github.com/DavidAnson/markdownlint/blob/v0.37.4/doc/md055.md
  "MD055": false,
  // 表格列数: https://github.com/DavidAnson/markdownlint/blob/v0.37.4/doc/md056.md
  "MD056": false,
  // 表格周围应有空行: https://github.com/DavidAnson/markdownlint/blob/main/doc/md058.md
  "MD058": false
}
