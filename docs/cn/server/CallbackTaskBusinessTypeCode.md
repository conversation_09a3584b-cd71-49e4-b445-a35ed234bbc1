---
title: 回调任务业务类型码
order: 2
---


**回调任务业务类型码**

任务执行业务回调参考码及参考json

任务业务类型  | taskBusinessType | 参考回调json
--- |------------------| ---
实例状态| 999              | {"endTime": 1734950319974,"padCode": "AC32010230013","taskBusinessType": 999,"taskContent": "","taskId": 14691,"taskResult": "Success","taskStatus": 3}
云机状态| 1000             | {"endTime": null,"padCode": "AC32010230003","taskBusinessType": 1000,"taskContent": "","taskId": 14676,"taskResult": "Success","taskStatus": 3}
实例重启任务| 1000             | { "endTime": null, "padCode": "AC32010230013", "taskBusinessType": 1000, "taskContent": "", "taskId": 10609, "taskResult": "Success", "taskStatus": 3}
实例重置任务| 1001             | {"endTime": null,"padCode": "AC32010230014","taskBusinessType": 1001,"taskContent": "","taskId": 10610,"taskResult": "Success","taskStatus": 3 }
异步执行ADB任务| 1002             | { "cmd": "cd /root;ls", "cmdResult": "/system/bin/sh: cd: /root: No such file or directory", "endTime": 1734942133000, "padCode": "AC32010230014", "taskContent": null, "taskId": 10614, "taskResult": "/system/bin/sh: cd: /root: No such file or directory", "taskStatus": 3}
应用安装任务| 1003             | {"endTime": 1734939747000,"padCode": "AC32010230014","taskBusinessType": 1003,"taskContent": "","taskId": 10613,"taskResult": "Success","taskStatus": 3}
应用卸载任务| 1004             | {"endTime": 1734940052000,"padCode": "AC32010230014","taskBusinessType": 1004,"taskContent": "","taskId": null,"taskResult": "Success","taskStatus": 3}
应用启停任务| 1007/1005        | {"packageName": "com.quark.browser","padCode": "AC32010230014","taskId": 10618,"taskStatus": 3}
文件上传实例任务| 1009             | {"errorCode": null,"fileId": "cfec132ab3c4e1aff5515c4467d9bbe460","padCode": "AC32010230014","result": true,"taskBusinessType": 1009,"taskId": 10659,"taskResult": "Success","taskStatus": 3}
查询实例应用列表| 1011             | {"apps": [{"appName": "夸克","packageName": "com.quark.browser","versionCode": 650,"versionName": "7.3.0.650"}],"padCode": "AC32010230014","taskBusinessType": 1011,"taskId": 10665,"taskStatus": 3}
实例升级镜像任务| 1012             | {"endTime": 1734939473335,"padCode": "AC32010230013","taskBusinessType": 1012,"taskContent": "","taskId": 10612,"taskResult": "Success","taskStatus": 3}
实例黑名单任务| 1015             | {"padCode": "AC32010250001","taskBusinessType": 1015,"taskId": 17594,"taskStatus": 3}
备份实例| 1024             | {"endTime": null,"padCode": "AC32010230014","taskBusinessType": 1024,"taskContent": "","taskId": 10607,"taskResult": "Success","taskStatus": 3}
还原备份数据| 1025             | {"endTime": 1734946555557,"padCode": "AC32010230014","taskBusinessType": 1025,"taskContent": "","taskId": 10620,"taskResult": "Success","taskStatus": 3}
一键新机| 1124             | {"endTime": 1734947364915,"padCode": "AC32010230012","taskBusinessType": 1124,"taskContent": {"ro.product.system.name": "X7"// ... 这里包含其他键值对，这里只给出了一个示例},"taskId": 14655,"taskResult": "Success","taskStatus": 3}
网存实例开机| 1201             | {"endTime":null,"padCode":"ACN250427352B7WU","taskBusinessType":1201,"taskContent":"","taskId":10001661,"taskResult":"","taskStatus":3}
网存实例关机| 1202             | {"endTime":null,"padCode":"ACN250427352B7WU","taskBusinessType":1202,"taskContent":"","taskId":10001662,"taskResult":"","taskStatus":3}
网存实例删除| 1203             | {"endTime":null,"padCode":"ACN250427352B7WU","taskBusinessType":1203,"taskContent":"","taskId":10001662,"taskResult":"","taskStatus":3}
网存存储备份| 1204             | {"endTime":null,"padCode":"ACN250427352B7WU","taskBusinessType":1204,"taskContent":"","taskId":10001664,"taskResult":"","taskStatus":3}
网存存储删除| 1205             | {"endTime":null,"padCode":"ACN250427352B7WU","taskBusinessType":1205,"taskContent":"","taskId":10001665,"taskResult":"","taskStatus":3}
文件上传任务| 2000             |  {"fileUniqueId": "cf4768c0388b3783eaa86d422b7d385b2f","originFileUrl": "<http://192.168.230.80:18100/file/f48bddbc736d6a04268efa2098a53ebczidongjingling.apk","taskBusinessType>": 2000,"taskId": 489,"taskStatus": 3}
镜像上传任务| 4001             |  {"imageId":"img-2505244083465456","taskBusinessType":4001,"taskStatus":-1,"failMsg":"URL地址错误"}
