---
title: 实例属性列表
order: 1
---

## Modem Properties 属性列表
>
> Android 系统中的可配置属性：
>
> - 非持久化保存：可通过“修改实例属性（updatePadProperties）”接口的 modemPropertiesList 参数设置（非持久化保存，立即生效，重启实例后失效）
> - 持久化保存：可通过“修改实例属性（updatePadProperties）”接口的 modemPersistPropertiesList 参数来设置（持久化保存，重启实例后生效）

### 持久化调用

请求示例

```json
"modemPersistPropertiesList":[{
  "propertiesName":"IMEI",
  "propertiesValue":"897654321"
},
  {
    "propertiesName":"ICCID",
    "propertiesValue":"00998877"
  }]
```

### 非持久化调用

请求示例

```json
"modemPropertiesList":[{
  "propertiesName":"IMEI",
  "propertiesValue":"897654321"
},
  {
    "propertiesName":"ICCID",
    "propertiesValue":"00998877"
  }]
```

### 属性列表

| 属性（key） | 属性值（value） | 属性说明
| :------------ | :------------ |  :------------ |
| IMEI | 897654321 | |
| ICCID | 00998877 | |
| IMSI | 4600112345 | |
| MCCMNC | 461,01 | |
| OpName | 中国移动 | |
| PhoneNum | 861380013800 | | |

## System Properties系统属性
>
> Android 系统中的可配置属性：
>
> - 非持久化保存：可通过“修改实例属性（updatePadProperties）”接口的 systemPropertiesList 参数设置（非持久化保存，立即生效，重启实例后失效）
> - 持久化保存：可通过“修改实例属性（updatePadProperties）”接口的 systemPersistPropertiesList 参数来设置（持久化保存，重启实例后生效）

### 持久化调用

请求示例

```json
"systemPersistPropertiesList":[{
  "propertiesName":"ro.product.manufacturer",
  "propertiesValue":"XIAOMI"
},
  {
    "propertiesName":"ro.product.brand",
    "propertiesValue":"XIAOMI"
  }]
```

### 非持久化调用

```json
请求示例
"systemPropertiesList":[{
  "propertiesName":"ro.product.manufacturer",
  "propertiesValue":"XIAOMI"
},
  {
    "propertiesName":"ro.product.brand",
    "propertiesValue":"XIAOMI"
  }]
```

### 属性列表

#### 通用属性

| 属性（key） | 属性值（value） | 属性说明 |
| :------------ | :------------ | :------------ |
|ro.product.manufacturer | 例：HW | 厂商 |
|ro.product.brand | 例：HW | 品牌 |
|ro.product.model | 例：LYA_AL00 | 型号
|ro.build.id | 0 | Build 标签
|ro.build.display.id | 0 | Build 版本号
|ro.product.name | 例：LYA_AL00 | 产品名称
|ro.product.device | 例：HWLYA | 设备信息
|ro.product.board | 例：LYA | 主板名
|ro.build.tags | 例：dev-keys/release-keys | 开发 key/release key
|ro.build.fingerprint | 0 | 系统指纹
|ro.build.date.utc | 0 | 固件编译时间戳
|ro.build.user | 0 | 固件编译用户
|ro.build.host | 0 | 固件编译主机
|ro.build.description | 0 | 编译描述信息
|ro.build.version.incremental | 0 | 内部版本号
|ro.build.version.codename | 0 | codename

#### 其他属性

##### 开放指定前缀的属性
>
> "ro.build.",
"ro.product.",
"ro.odm.",
"ro.vendor.",
"ro.system_ext.",
"ro.system.",
"ro.com.",
"ro.config."

但并非开放全部：其中有部分是不适宜开放，在下面的黑名单中维护
另外，部分不以以上为前缀的属也给予开放，在下面的白名单中维护

##### 不可修改的属性黑名单
>
> "ro.build.type",
"ro.build.vername",
"ro.build.version.release",
"ro.build.version.sdk",
"ro.build.version.name",
"ro.product.cpu.abi",
"ro.product.cpu.abilist",
"ro.product.cpu.abilist32",
"ro.product.cpu.abilist64",
"ro.odm.build.type",
"ro.odm.build.version.release",
"ro.odm.build.version.sdk",
"ro.odm.product.cpu.abilist",
"ro.odm.product.cpu.abilist32",
"ro.odm.product.cpu.abilist64",
"ro.vendor.build.type",
"ro.vendor.build.version.release",
"ro.vendor.build.version.sdk",
"ro.vendor.product.cpu.abilist",
"ro.vendor.product.cpu.abilist32",
"ro.vendor.product.cpu.abilist64",
"ro.system.build.type",
"ro.system.build.version.release",
"ro.system.build.version.sdk"

##### 可修改的属性白名单
>
> "ro.board.platform",
"ro.bootimage.build.fingerprint",
"ro.baseband",
"ro.boot.wificountrycode",
"ro.bootimage.build.date",
"ro.bootimage.build.date.utc",
"ro.gfx.driver.0",
"ro.revision",
"ro.ril.svdo",
"ro.ril.svlte1x",
"ro.serialno",

#### 模拟SIM

| 属性（key） | 属性值（value） | 属性说明 |
| :------------ | :------------ | :------------ |
| aic.sim.state | 例：5 | 0;1：无 SIM 卡;2 ：SIM_STATE_NETWORK_LOCKED;3：SIM 卡 PIN 码锁定;4：SIM 卡 PUK 码锁定;5：SIM 卡正常 |
| aic.operator.shortname | 例：CMCC | 运营商简称
| aic.operator.numeric | 例：46001 | 网络运营商 id (即 MCCMNC)
| aic.spn | 例：China Mobile | SIM 卡运营商名称
| aic.iccid | 例：89860002191807255576 | SIM 卡卡号
| aic.imsi | 例：460074008004488 | 前缀为 sim 卡运营商号：MCC(3 位)+MNC(2 位或 3 位)
| aic.phonenum | 例：18629398873 | 电话号码
| aic.net.country | 例：ChINA | 网络所属国家
| aic.sim.country | 例：ChINA | SIM 卡所属国家
| aic.signal.strength | 例：{\"cdmaDbm\"=0,\"cdmaEcio\"=1,\"evdoDbm\"=2,\"evdoEcio\"=3,\"evdoSnr\"=4,\"rssi\"=-51,\"asulevel\"=30,\"ber\"=0,\"ta\"=0,\"rscp\"=-51,\"ecNo\"=10,\"rsrp\"=1,\"rsrq\"=43,\"rssnr\"=300,\"cqi\"=15,\"csiRsrp\"=-44,\"csiRsrq\"=-3,\"csiSinr\"=23,\"csiCqiTableIndex\"=0,\"ssRsrp\"=-44,\"ssRsrq\"=-3,\"ssSinr\"=40,\"parametersUseForLevel\"=22} | 信号强度
| aic.deviceid | 例：370483496 | 电子序列号
| aic.cellinfo | 例：{\"lac\"=4049,\"cid\"=1463,\"sid\"=149,\"arfcn\"=arfcn,\"bsic\"=133,\"alphal\"=\"\",\"alphas\"=\"CMCC\",\"psc\"=11,\"ci\"=11,\"psc\"=11,\"pci\"=22,\"tac\"=33, \"earfcn\"=44,\"bandwidth\"=144} | 不同模式下基站信息生效字段：GSM：int lac, int cid, int arfcn, int bsic, String mccStr,String mncStr, String alphal, String alphasCDMA：int lac, int cid, int psc, int uarfcn, String mccStr, String mncStr, String alphal, String alphasLTE：int mcc, int mnc, int ci, int pci, int tac,String mccStr, String mncStr, String alphal, String alphasNR:int csiRsrp, int csiRsrq, int csiSinr, int ssRsrp, int ssRsrq, int ssSinr #其中 mccStr,mncStr,mcc,mnc 将从 modem/aic.operator.numeric 获取，此处不用传
| aic.net.type | 例：13 | 数据网络类型: gsm/lte/cdma(电信), 默认为 lte ## 0-20;NETWORK_TYPE_CDMA=4 ;NETWORK_TYPE_LTE=13 ;NETWORK_TYPE_GSM=16 ;NETWORK_TYPE_NR=20 #5G
| aic.radio.type | 例：13 | 语音网络类型: gsm/lte/cdma(电信), 默认为 lte(VoLTE) ;NETWORK_TYPE_CDMA=4 ;NETWORK_TYPE_LTE=13 ;NETWORK_TYPE_GSM=16 ;NETWORK_TYPE_NR=20 #5G
| aic.gid1 | 例：FF | GroupLevel1 具体的含义取决于运营商的定义，可能代表不同的服务或特定的功能。
| aic.alphatag | 例：abcdefg | SIM 卡上存储的一个字符串，通常用于表示移动网络运营商的名称或品牌
| aic.nai | 例：abcdefg | NAI 是一个用于标识设备在移动网络中的身份的字符串

##### 不同模式下信号强度生效参数

CDMA:int cdmaDbm, int cdmaEcio, int evdoDbm, int evdoEcio, int evdoSnr
GSM: int rssi, int ber, int ta
WCDMA:int rssi, int ber, int rscp, int ecno
TDSCDMA：int rssi, int ber, int rscp
LTE:int rssi, int rsrp, int rsrq, int rssnr, int cqi, int timingAdvance
NR: int csiRsrp, int csiRsrq, int csiSinr, int ssRsrp, int ssRsrq, int ssSinr

##### 信号强度数值范围参考

 //cdma
public int cdmaDbm; // This value is the RSSI value
public int cdmaEcio; // This value is the Ec/Io
public int evdoDbm; // This value is the EVDO RSSI value
public int evdoEcio; // This value is the EVDO Ec/Io
public int evdoSnr; // Valid values are 0-8. 8 is the highest signal to noise ratio
//public int level;

//gsm
public int rssi; // in dBm [-113, -51] or UNAVAILABLE
public int ber; // bitErrorRate; // bit error rate (0-7, 99) TS 27.007 8.5 or UNAVAILABLE
public int ta; // timingAdvance; // bit error rate (0-7, 99) TS 27.007 8.5 or UNAVAILABLE

//wcdma
public int rscp; // in dBm [-120, -24]
public int ecno; // range -24, 1, CellInfo.UNAVAILABLE if unknown

//lte
//public int rssi; // in dBm [-113,-51], UNKNOWN
public int rsrp; // in dBm [-140,-43], UNKNOWN
public int rsrq; // in dB [-20,-3], UNKNOWN
public int rssnr; // in 10*dB [-200, +300], UNKNOWN
public int cqi; // [0, 15], UNKNOWN
//public int ta; // [0, 1282], UNKNOWN

//Nr
public int csiRsrp; // [-140, -44], UNKNOWN
public int csiRsrq; // [-20, -3], UNKNOWN
public int csiSinr; // [-23, 23], UNKNOWN

public int csiCqiTableIndex;
public List mCsiCqiReport;
public int ssRsrp; // [-140, -44], UNKNOWN
public int ssRsrq; // [-20, -3], UNKNOWN
public int ssSinr; // [-23, 40], UNKNOWN

public int mParametersUseForLevel;

## Setting Properties属性

请求示例

```json
"settingPropertiesList":[{
  "propertiesName":"ssaid/com.demo1",
  "propertiesValue":"2345243531"
},
  {
    "propertiesName":"ssaid/com.demo2",
    "propertiesValue":"123456789"
  },
  {
    "propertiesName":"language",
    "propertiesValue":"zh-CN"
  }]
```

### 属性列表

| 属性（key） | 属性值（value） | 属性说明
| :------------ | :------------ |  :------------ |
| ssaid/com.cheersucloud.cimi.sample | 897654321 | Android id |
| bt/mac | 1A:75:FF:88:2A:06 | 蓝牙 MAC |
| language | zh-CN | 系统语言 |
| timezone | Asia/Shanghai | 系统时区 |
| systemvolume | 10 | 固定媒体音量，可取范围 0-15 |

## OAID Properties

请求示例

```json
"oaidPropertiesList":[{
  "propertiesName":"UDID",
  "propertiesValue":"111111111"
},
  {
    "propertiesName":"OAID",
    "propertiesValue":"123456789"
  },
  {
    "propertiesName":"language",
    "propertiesValue":"zh-CN"
  }]
```

### 属性列表

| 属性（key） | 属性值（value） | 属性说明 |
| :------------ | :------------ |  :------------ |
| UDID | 11111111 | 是 iOS 设备的唯一标识符，每个 iOS 设备都有一个独一无二的 UDID。但是从 2018 年起，苹果公司禁止开发者访问 UDID，而改用 Vendor ID (Vender Identifier) |
| OAID | 22222222 | 是 Android 设备的匿名标识符，由中国移动互联网产业联盟（CCIA）开发和推广，目的是为了取代设备 ID（IMEI）和 Android ID（Android系统的唯一标识符），解决移动广告跨应用跨平台精准投放和用户隐私保护的问题。 |
| VAID | 33333333 | 是 Android 设备的厂商广告标识符，由设备厂商提供。VAID 可以用于广告跨应用跨平台精准投放，与 OAID 不同的是，VAID 不是匿名标识符。 |
| AAID | 44444444 | 是 Google Play 服务框架提供的唯一标识符，用于广告跨应用跨平台精准投放，同时也是为了保护用户隐私而设计的。用户可以随时重置AAID，禁止应用程序访问其 AAID。 |
