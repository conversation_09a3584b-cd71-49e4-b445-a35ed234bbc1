---
title: 错误码
order: 5
---


### **系统错误码说明**

接口可能返回的错误码及对应解决方案：

| code   | msg                           | 解决方案 |
|--------|--------------------------------|---------|
| 100000 | 请求参数不正确                 | 请检查参数是否符合要求 |
| 100001 | 没有访问权限                   | 联系管理员开通权限 |
| 100002 | 无法获取 HttpServlet 请求对象  | 确认请求方式及参数是否正确 |
| 100003 | 请求头缺少 Authorization       | 检查请求头是否包含 Authorization |
| 100004 | 无效的密钥                     | 请检查密钥是否正确 |
| 100005 | 验证签名失败                   | 确认签名方式正确 |
| 100006 | 请求头缺少 token               | 确认 token 是否正确传递 |
| 100007 | 无效的 token                   | 检查 token 是否过期 |
| 100008 | token 验证失败                 | 确认 token 有效性 |
| 100009 | 缺少客户信息                   | 检查参数，参考调用示例 |
| 100010 | 处理失败                       | 重试，若多次失败请联系管理员 |
| 100011 | 相同请求正在进行               | 稍后重试 |
| 100012 | 不支持的 HTTP 请求方式         | 确认接口支持的请求类型 |
| 100013 | 参数类型或格式错误             | 请检查参数格式 |
| 110014 | 操作过于频繁                   | 稍后重试 |
| 120006 | token 绑定的 uuid 不匹配       | 请检查参数 |
| 120007 | 接口不支持该功能               | 联系管理员 |
| 120008 | token 不属于当前用户           | 确认 token 归属 |
| 110028 | 实例不存在                     | 请检查实例参数 |
| 110031 | 实例未就绪                     | 等待实例状态就绪 |
