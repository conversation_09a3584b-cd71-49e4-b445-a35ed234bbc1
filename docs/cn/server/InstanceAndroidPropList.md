---
title: 安卓改机属性列表
order: 1
---



**实例为云真机类型时，云真机可设置的ro.开头属性**

```javascript
{
  "ro.sys.cloud.android_id" : "xxxxxxxxxxxx",
}
```

**实例为虚拟机类型时，可设置属性列表中ro.开头属性**

**属性列表**

```javascript
{
  // 机型、品牌、机型、指纹信息设置
  "ro.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.build.description" : "raven-user 13 TQ1A.230105.002 9325679 release-keys",
  "ro.product.brand" : "google",
  "ro.product.model" : "raven",
  "ro.product.manufacturer" : "google",
  "ro.product.device" : "raven",
  "ro.product.name" : "raven",
  "ro.build.version.incremental" : "9325679",
  "ro.build.flavor" : "raven-user",
  "ro.product.board" : "raven",
  "ro.build.product" : "raven",
  "ro.hardware" : "raven",
  "ro.odm.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.product.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.system.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.system_ext.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.vendor.build.fingerprint" : "google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys",
  "ro.product.odm.device" : "raven",
  "ro.product.odm.model" : "raven",
  "ro.product.odm.name" : "raven",
  "ro.product.product.device" : "raven",
  "ro.product.product.model" : "raven",
  "ro.product.product.name" : "raven",
  "ro.product.system.device" : "raven",
  "ro.product.system.model" : "raven",
  "ro.product.system.name" : "raven",
  "ro.product.system_ext.device" : "raven",
  "ro.product.system_ext.model" : "raven",
  "ro.product.system_ext.name" : "raven",
  "ro.product.vendor.device" : "raven",
  "ro.product.vendor.model" : "raven",
  "ro.product.vendor.name" : "raven",

  // 安卓ID
  "ro.sys.cloud.android_id" : "xxxxxxxxxxxx",

  // 媒体DRM deviceUniqueId
  "persist.sys.cloud.drm.id" : "400079ef55a4475558eb60a05c4a4335121238f13fdd48c10026e2847a6fc7a6",

  // 媒体DRM provisioningUniqueId (需要较新版本的ROM才支持)
  "persist.sys.cloud.drm.puid" : "3240032ec137ebb38b11bec65af4eeb13bf765ccd3f0f20318a54f3887cbd675",

  // GPU 信息
  "persist.sys.cloud.gpu.gl_vendor" : "my_gl_vendor",
  "persist.sys.cloud.gpu.gl_renderer" : "my_gl_renderer",
  "persist.sys.cloud.gpu.gl_version" : "\"OpenGL ES 3.2\"",
  
  // SIM卡信息
  // mcc mnc
  "persist.sys.cloud.mobileinfo" : "525,01",

  // type,mcc,mnc,tac(16进制),cellid(16进制),narfcn(16进制),pci(16进制)
  // type：
  // 表示网络类型，例如 LTE、5G、UMTS 或 GSM。
  // 这通常用来判断当前连接使用的通信技术。
  // 当前需要固定为9，代表5G

  // mcc (Mobile Country Code)：
  // 移动国家代码，标识网络所在国家。例如，460代表中国，525代表新加坡。

  // mnc (Mobile Network Code)：
  // 移动网络代码，用于标识特定运营商。例如，新加坡的MNC代码01可能代表Singtel。

  // tac (Tracking Area Code)： 16进制
  // 跟踪区域代码，用于分区移动网络，使得手机设备可以进行位置更新。
  // 在LTE和5G网络中广泛使用，帮助运营商确定设备的物理位置。

  // cellid (Cell Identity)： 16进制
  // 小区标识，标识单个基站的ID。
  // CellID加上TAC可以精确定位一个设备所连接的基站。

  // narfcn (NR Absolute Radio Frequency Channel Number)： 16进制
  // 这是5G网络中的绝对频率信道号，指定了设备与基站之间通信的具体频率。

  // physicalcellid (PCI)： 16进制
  // 物理小区ID，标识在特定频率上的物理小区。对于LTE和NR（5G），PCI用于区分相邻基站的信号。
  "persist.sys.cloud.cellinfo" : "9,525,01,2CA,E867D07,6A4,78",
  // IMEI号
  "persist.sys.cloud.imeinum" : "759344050201724",
  // ICCID号
  "persist.sys.cloud.iccidnum" : "68681042080146961320",
  // IMSI号
  "persist.sys.cloud.imsinum" : "525010862935902",
  // 手机号，需要以国家代码开头，比如新加坡是65
  "persist.sys.cloud.phonenum" : "6590523545",

  // gps 信息
  "persist.sys.cloud.gps.lat" : "1.357703",
  "persist.sys.cloud.gps.lon" : "103.817543",
  "persist.sys.cloud.gps.speed" : "0.1",
  "persist.sys.cloud.gps.altitude" : "15",
  "persist.sys.cloud.gps.bearing" : "73",

  // 电池容量 (单位毫安)
  "persist.sys.cloud.battery.capacity" : "5000",

  // 电池信息 (开机初始值，开机后会仿真模拟正常充放电)
  "persist.sys.cloud.battery.level" : "80",

  // 语言
  "persist.sys.language" : "en",
  // 国家
  "persist.sys.country" : "HK",
  // 时区  
  "persist.sys.timezone" : "Asia/Hong_Kong",

  // wifi 模拟信息
  // wifi 名称
  "persist.sys.cloud.wifi.ssid" : "testwifi",
  // wifi mac
  "persist.sys.cloud.wifi.mac" : "00:02:00:00:00:00",
  // wifi ip
  "persist.sys.cloud.wifi.ip" : "**************",
  // wifi 网关
  "persist.sys.cloud.wifi.gateway" : "*************",
  // wifi dns1
  "persist.sys.cloud.wifi.dns1" : "*************",
  // wifi 默认名称
  "persist.sys.cloud.wifi.ssid" : "wifiName",

  // 应用安装来源
  "persist.sys.cloud.pm.install_source" : "com.android.vending",
        
  //开机时间，单位为秒
  "persist.sys.cloud.boottime.offset"  :  "3"   

  // 设置网络代理属性，开机生效，无需额外设置
  // 代理类型 socks5、http-relay
  "ro.sys.cloud.proxy.type" : "socks5",
  // 代理配置 代理IP，代理端口，代理用户，代理密码，true(开启代理)  
  // 注意事项：
  // 1. 代理用户和代理密码中不能存在|字符
  // 2. 当没有用户和密码的时候，留空既可,类似这样 *************|54212|||true
  "ro.sys.cloud.proxy.data" : "*************|54212|username|password|true"
  // 代理模式
  // proxy 为使用系统iptables能力来实现代理功能
  // vpn   为使用安卓系统VPN能力来实现代理功能，支持DNS防泄漏
  "ro.sys.cloud.proxy.mode" : "proxy",

  // 设置dns数量，可以设置为1或者2
  "ro.boot.redroid_net_ndns" : "2",

  // 设置第1个dns的地址
  "ro.boot.redroid_net_dns1" : "*******",

  // 设置第2个dns的地址
  "ro.boot.redroid_net_dns2" : "*******",
  

  "ro.sys.cloud.proxy.byPassPackageNameNum"  :  "1",   //绕过代理包名列表数量
  "ro.sys.cloud.proxy.byPassPackageName1" :  "包名1,包名2",   //Android属性有字符长度限制，字符长度最好不超过80个字符，超过则重新再创建一个列表

  "ro.sys.cloud.proxy.byPassIpNameNum"  :  "1",   //绕过代理IP列表数量
  "ro.sys.cloud.proxy.byPassIpName1" :  "ip1,ip2",   //Android属性有字符长度限制，字符长度最好不超过80个字符，超过则重新再创建一个列表

  "ro.sys.cloud.proxy.byByPassDomainNum"  :  "1",   //绕过代理域名列表数量
  "ro.sys.cloud.proxy.byByPassDomain1" :  "域名1,域名2",   //Android属性有字符长度限制，字符长度最好不超过80个字符，超过则重新再创建一个列表

  "ro.sys.cloud.rand_pics" : "3", // 系统初次启动，随机生成3张图片放置到相册中

  "ro.sys.cloud.boot_id" : "1b9d92ee76f34825a8bdff42335f46ec"  // /proc/sys/kernel/random/boot_id
}
```
