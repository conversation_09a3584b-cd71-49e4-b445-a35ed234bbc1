---
title: 接口文档
order: 2
---

### SDK Token签发

签发临时 STS Token，用于对接入云手机服务的用户进行鉴权。

#### **获取SDK临时Token**

**接口地址**

> /openapi/open/token/stsToken

**请求方式**

> GET

**请求数据类型**

> application/json

**请求示例**

```java
package com.xiaosuan.api.utils;

import com.alibaba.fastjson.TypeReference;
import com.xiaosuan.armcloud.sdk.configure.ArmCloudConfig;
import com.xiaosuan.armcloud.sdk.constant.ArmCloudApiEnum;
import com.xiaosuan.armcloud.sdk.http.DefaultHttpExecutor;
import com.xiaosuan.armcloud.sdk.model.Result;
import com.xiaosuan.armcloud.sdk.service.ArmCloudApiService;
import com.xiaosuan.armcloud.sdk.service.impl.ArmCloudApiServiceImpl;

import java.util.HashMap;

public class test {
    /**
     * 获取sts_token
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {

        ArmCloudConfig armCloudConfig = new ArmCloudConfig();
        armCloudConfig.setOpenUrl("https://xxx");
        armCloudConfig.setService("armcloud-paas");
        armCloudConfig.setHost("xxx");
        armCloudConfig.setAk("xxxxxx");
        armCloudConfig.setSk("xxxxxx");
        ArmCloudApiService armcloudApiService = new ArmCloudApiServiceImpl(armCloudConfig, new DefaultHttpExecutor());
        Result<Object> result = armcloudApiService.execute(ArmCloudApiEnum.STS_TOKEN,new HashMap<>(), new TypeReference<Result<Object>>() {});
        System.out.println(result);

    }
}

```

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | ---|
|code | 200 | Integer | 状态码|
|msg | success | String | 响应消息|
|ts | ************* | Long | 时间戳|
|data |  | Object |  |
|├─token | xxxx-xxxx-xxxxx-xxxx | String | sdk通信token|

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": {
  "token": "xxxx-xxxx-xxxxx-xxxx"
 }
}
```

#### **获取SDK临时Token(根据padCode)**

签发临时 STS Token，用于对接入云手机服务的用户进行鉴权(该token只能用于请求的padCode)。

**接口地址**

> /openapi/open/token/stsTokenByPadCode

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型   | 是否必填 | 参数描述   |
|--- |-----|--------| --- |--------|
|padCode | ACXXXXX | String | 是 | 实例code |

**请求示例**

```javascript
{"padCode":"AC32010230001"}
```

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | ---|
|code | 200 | Integer | 状态码|
|msg | success | String | 响应消息|
|ts | ************* | Long | 时间戳|
|data |  | Object |  |
|├─token | xxxx-xxxx-xxxx-xxxx | String | sdk通信token|

**响应示例**

```javascript
{"code":200,"msg":"success","ts":1735209109185,"data":{"token":"xxx-xxx-xxx-xxxx"}}
```

#### **清除SDK授权Token**

**接口地址**

> /openapi/open/token/clearStsToken

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型   | 是否必填 | 参数描述|
|--- |-----|--------| --- | --- |
|token | 123 | String | 是 | |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | ---|
|code | 200 | Integer | 状态码|
|msg | success | String | 响应消息|
|ts | ************* | Long | 时间戳|
|data |  | Object |  |

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
120008 | token不属于当前用户| 参考接口说明，检查请求参数和传值

### 板卡管理

#### **板卡列表**

根据查询条件分页获取ARM列表。

**接口地址**

> /openapi/open/device/list

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值          | 参数类型 | 是否必填 | 参数描述
--- |--------------| --- |--| ---
page | 1            | Integer | 是 | 页码
rows | 10           | Integer | 是 | 条数
padAllocationStatus | 2           | Integer |否 | 实例分配状态：-2删除失败 -1分配失败 0-未分配；1-分配中 2-已分配 3-删除中
deviceStatus | 1           | Integer | 否 | 物理机状态 0-离线；1-在线
armServerCode | XS-MARS3500SA-0019           | String | 否 | 服务器code
deviceCode | ***************           | String | 否 | 板卡code
deviceIp | **********           | String | 否 | deviceIp
armServerStatus | 1           | Integer | 否 |  服务器状态 0-离线；1-在线
idc | 1           | String | 否 |  机房Id
deviceIpList |  | String[] | 否    |
├─ | ********** | String | 否    | 板卡IP

**响应参数**

| 参数名               | 示例值 | 参数类型 | 参数描述                                            |
|-------------------| -- | --- |-------------------------------------------------|
| code              | 200 | Integer |                                                 |
| msg               | success | String ||
| ts                | 1713773577581 | Long ||
| data              |  | Object ||
| ├─page            | 1 | Integer | 当前页                                             |
| ├─rows            | 10 | Integer | 每页的数量                                           |
| ├─size            | 1 | Integer | 当前页的数量                                          |
| ├─total           | 1 | Integer | 总记录数                                            |
| ├─totalPage       | 1 | Integer | 总页数                                             |
| ├─pageData        |  | object[] | 列表                                              |
| ├─├─id            | 11236 | Integer | 板卡id                                            |
| ├─├─deviceLevel      | m2-3 | String | 云机实例级别                              |
| ├─├─deviceCode     | AC32010250020 | String | 物理机编号 |
| ├─├─deviceOutCode   | 03-58614134b4357321 | String | 外部物理机编码                                            |
| ├─├─deviceStatus       | **********/24 | Integer | 物理机状态 0-离线；1-在线                                            |
| ├─├─deviceIp      | ********** | Integer | 物理机IP                                            |
| ├─├─idc         | 1 | Integer | 外部机房编码                                           |
| ├─├─armServerCode | ACS32010260000 | String | ARM服务器编码                                           |
| ├─├─createBy    | admin | String | 创建者                                            |
| ├─├─createTime          | 2025-01-02 10:56:13 | String | 创建时间                                         |
| ├─├─padAllocationStatus          | 2 | Integer | 实例分配状态：-2删除失败 -1分配失败 0-未分配；1-分配中 2-已分配 3-删除中    |                                     |
| ├─├─clusterCode          | 001 | String | 集群code    |
| ├─├─macAddress          | 52:74:01:b8:58:40 | String | MAC地址    |
| ├─├─initStatus          | 1 | String | 初始化状态 0-初始化失败 1-初始化成功 2-初始化中    |
| ├─├─cbsInfo          | 2.0.8 | String | 板卡CBS信息    |
| ├─├─nodeId          | 1 | String | 刀片ID    |
| ├─├─position          | 3 | String | 卡槽位置    |
| ├─├─gateway          | *********** | String | 板卡网关    |

**请求示例**

```javascript
{
    "padAllocationStatus": 2,
        "deviceStatus": 1,
        "armServerCode": "ACS32010260000",
        "deviceCode": "AC32010250020",
        "deviceIpList": [
        "**********"
    ],
        "deviceIp": "**********",
        "armServerStatus": 1
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1737626655161,
        "data": {
        "page": 1,
            "rows": 10,
            "size": 1,
            "total": 1,
            "totalPage": 1,
            "pageData": [
            {
                "page": 1,
                "rows": 10,
                "id": 11236,
                "deviceLevel": "m2-3",
                "deviceCode": "AC32010250020",
                "deviceOutCode": "03-58614134b4357321",
                "deviceStatus": true,
                "deviceIp": "**********",
                "idc": "1",
                "armServerCode": "ACS32010260000",
                "createBy": "admin",
                "createTime": "2025-01-02 10:56:13",
                "padAllocationStatus": 2,
                "deleteFlag": false,
                "clusterCode": "001",
                "macAddress": "52:74:01:b8:58:40",
                "initStatus": 1,
                "cbsInfo": "2.0.8",
                "nodeId": "1",
                "position": "3",
                "gateway": null
            }
        ]
    }
}
```

#### **板卡重启**

断电重启板卡, 将影响板卡上创建的所有实例

该接口一般用在重启实例也无法恢复的情况

**接口地址**

> /openapi/open/device/powerReset

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名  | 示例值        | 参数类型 | 是否必填 | 参数描述 |
| ------- | ------------- | -------- | -------- | -------- |
|deviceIps |  | String[] | 是 |  |
|├─ | ************ | String | 是 | 物理设备IP |
|type | 2 | String[] | 是 | 重启类型 2：断电重启 |

**响应参数**

| 参数名 | 示例值 | 参数类型 | 参数描述|
| --- | --- | --- | ---|
| code | 200 | Integer |  状态码|
| msg | success | String |  响应消息|
| ts | ************* | Long |  时间戳|
| data |  | Object[] |  |
| ├─taskId | 1|Integer |  任务ID|
| ├─deviceIp |************ |String |  物理设备IP|
| ├─errorMsg |“” |String |  失败的原因|
| ├─deviceOutCode |AC22030010000 |String |  云机编号|

**请求示例**

```javascript
{
 "deviceIps": [
  "************"
 ],
 "type":2
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1713773577581,
    "data": [
        {
            "taskId": 1,
            "deviceIp": "************",
            "errorMsg": null,
            "deviceOutCode": "AC22030010000"
        }
    ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110030 | 执行断电重启命令失败,参数请求不合规 | 参数请求不合规
110032 | 物理IP不存在 | 请检查物理设备IP是否正确
110033 | 执行断电重启命令失败 | 联系相关人员

#### **重置板卡**

根据查询条件分页获取ARM列表。

**接口地址**

> /openapi/open/device/resetDevice

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值          | 参数类型 | 是否必填 | 参数描述
--- |--------------| --- |--| ---
remark | 确认删除           | String | 是 |  重置备注信息
deviceIps |  | String[] | 是    |物理机Ip列表(最少1个，最大128个)
├─ | ********** | String | 是    | 板卡IP

**响应参数**

| 参数名               | 示例值 | 参数类型 | 参数描述                                            |
|-------------------| -- | --- |-------------------------------------------------|
| code              | 200 | Integer |           响应码                                      |
| msg               | success | String ||
| ts                | 1713773577581 | Long ||
| data                | 1个板卡删除实例！ | String |消息内容|

**请求示例**

```javascript
{
    "deviceIps": [
        "**********"
    ],
        "remark": "1"
}
```

**响应示例**

```javascript
{
    "msg": "success",
        "code": 200,
        "data": "1个板卡删除实例！",
        "ts": 1739954448827
}

```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110060 | 板卡不存在| 确认下板卡IP是否正确

### 网存实例管理

#### **创建网存实例**

用于在指定集群中创建网存实例（默认类型为虚拟机）

**接口地址**

> /openapi/open/pad/net/storage/res/create

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值                      | 参数类型    | 是否必填 | 参数描述
--- |--------------------------|---------|------| ---  
clusterCode | 001                      | String  | 是    | 集群代码，标识实例所属集群
specificationCode | m2-3                     | String  | 是    | 规格代码，定义实例的性能配置
imageId | img-25021828327          | String  | 是    | 镜像ID，指定系统镜像
screenLayoutCode | realdevice_1440x3120x560 | String  | 是    | 屏幕布局代码
number | 2                        | Integer | 是    | 需要创建的实例数量
dns | *******                  | String  | 否    | DNS服务器地址
storageSize | 16                       | Integer | 是    | 存储大小（GB）
randomADITemplates | true                     | boolean | 否    | 随机ADI模板（该值为true为真机,并随机填充一个adi模板）
realPhoneTemplateId | 36                       | Integer | 否    | 真实设备模板ID
groupId             | 0          | Integer | 否    | 分组ID，默认为公共池。

**响应参数**

参数名 | 示例值                                 | 参数类型 | 参数描述
--- |-------------------------------------| --- |--------------
code | 200                                 | Integer | 状态码（200表示成功）
msg | success                             | String | 接口请求状态信息
ts | 1742536074329                       | Long | 时间戳
data | 实例创建成功                              | String | 业务返回信息
| ├─padCode | ACN250424UPHQOTG                    |Integer | 实例编号         |
| ├─screenLayoutCode | realdevice_1440x3120x560            |String | 屏幕布局编号       |
| ├─netStorageResId | “ZSC250424G4S6RH1-ACN250424UPHQOTG” |String | 网存code       |
| ├─deviceLevel | “m2-4”                              |String | 实例规格         |
| ├─clusterCode | “006”                               |String | 集群编号         |

**请求示例**

```json
{
  "clusterCode": "001",
  "specificationCode": "m2-3",
  "imageId": "img-25021828327",
  "screenLayoutCode": "realdevice_1440x3120x560",
  "isolateCpu": true,
  "isolateMemory": true,
  "number": 2,
  "isolateStorage": true,
  "dns": "*******",
  "storageSize": 16,
  "realPhoneTemplateId": 36
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN250424UPHQOTG",
      "cloudVendorType": 3,
      "memory": 8192,
      "groupId": 0,
      "storage": 45,
      "padIp": null,
      "streamType": 1,
      "padSn": 1,
      "armServerCode": null,
      "screenLayoutCode": "realdevice_1440x3120x560",
      "netStorageResSize": 16,
      "imageId": "img-25041475147",
      "dns": "*******",
      "dataSize": 17179869184,
      "dataSizeAvailable": null,
      "cpu": -1,
      "clusterCode": "006",
      "netStorageResId": "ZSC250424G4S6RH1-ACN250424UPHQOTG",
      "realPhoneTemplateId": null,
      "dataSizeUsed": null,
      "netStorageResFlag": 1,
      "deviceLevel": "m2-4",
      "online": 0,
      "socModel": "MACS2080",
      "streamStatus": 0,
      "status": 1
    }
  ],
  "ts": 1745465574610
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | -- | ---
220003 | 暂不支持当前存储规格,请参考文档设置| 当前支持固定存储值(4, 16, 32, 64, 128, 256)
110099 | ADI模板不存在,请检查参数| 确认ADI模板的值存在且未被禁用
110044 | 实例规格不存在 | 目前支持m2-1~10
220009 | 当前网存容量不足,请联系管理员 | 联系管理员增加网存容量或者删除掉一些不用的实例释放空间
110041 | 镜像不存在 | 确认镜像ID是否正确
110045 | 屏幕布局不存在 | 确认屏幕布局代码是否正确

#### **网存实例开机**

用于在指定集群中对网存实例进行开机操作。如果传入国家code无法匹配,将使用默认code:SG

**接口地址**

> /openapi/open/pad/net/storage/on

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- |------| ---  
clusterCode | 001 | String | 否    | 集群代码，标识实例所属集群
androidProp | {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"} | Object  | 否    | 参考 [安卓改机属性列表](./InstanceAndroidPropList.html)
countryCode | SG                                                  | String  | 否    | 国家编码(具体查看:<https://chahuo.com/country-code-lookup.html>)
padCodes | ["ACN250321HRKNE3F"] | String[] | 是    | 需要开机的实例编码列表,最多允许同时开机200个实例

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1742536327373 | Long | 时间戳
data | [ {...} ] | Object[] | 实例状态信息列表
├─ padCode | ACN250321HRKNE3F | String | 实例编码
├─ vmStatus | 0 | Integer | 实例状态（0 表示已启动）
├─ taskId | 13023 | Integer | 后台任务ID

**请求示例**

```json
{
  "clusterCode": "001",
  "padCodes": ["ACN250321HRKNE3F"], 
   "countryCode":"US",
   "androidProp": "{\"persist.sys.cloud.wifi.mac\": \"D2:48:83:70:66:0B\"}"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN250321HRKNE3F",
      "vmStatus": 0,
      "taskId": 13023
    }
  ],
  "ts": 1742536327373
}

```

**错误码**

错误码 | 错误说明|操作建议
--- | -- | ---
110095 | 没有可以开机的实例,请检查实例状态 | 确认实例状态是否为关机状态
220011 | 频繁操作网存实例,请稍后再试 | 稍后再试
110101 | 未找到与实例规格匹配的板卡，请前往板卡列表设置规格。 | 确认实例的规格跟板卡规格一致,并且板卡有算力未被占用

#### **网存实例关机**

用于在指定集群中对网存实例执行关机操作。

**接口地址**

> /openapi/open/pad/net/storage/off

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述                                                                                                                   
--- | --- | --- |------|------------------------------------------------------------------------------------------------------------------------  
clusterCode | 001 | String | 否    | 集群代码，标识实例所属集群                                                                                                          
padCodes | ["ACN250321HRKNE3F"] | String[] | 是    | 需要关机的实例编码列表                                                                                                            
forceDel | false              | Boolean  | 否    | <font color=red size=5>**请谨慎使用**</font> 是否强制删除（会直接关机并删除实例，<font color=red size=5>不保留数据</font>） 默认为false CBS版本2.3.5以上支持 |


**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1742536327373 | Long | 时间戳
data | [ {...} ] | Object[] | 关机结果信息列表
├─ padCode | ACN250321HRKNE3F | String | 实例编码
├─ vmStatus | 0 | Integer | 实例状态（0 表示已关机）
├─ taskId | 13023 | Integer | 后台任务ID

**请求示例**

```json
{
  "clusterCode": "001",
  "padCodes": ["ACN250321HRKNE3F"]
}
```

**响应示例**

```json
{
"msg": "success",
"code": 200,
"data": [
{
"padCode": "ACN250321HRKNE3F",
"vmStatus": 0,
"taskId": 13023
}
],
"ts": 1742536327373
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | -- | ---
110094 | 没有可以关机的实例,请检查实例状态 | 确认实例状态(关机状态跟开机中状态无法关机)

#### **删除网存实例**

用于在指定集群中删除指定的网存实例。

**接口地址**

> /openapi/open/pad/net/storage/delete

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---  
clusterCode | "" | String | 否 | 集群代码，标识实例所属集群（可为空）
padCodes | ["ACN250321GYWUP8J"] | String[] | 是 | 需要删除的实例编码列表

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1742536423100 | Long | 时间戳
data | [ {...} ] | Object[] | 删除结果信息列表
├─ padCode | ACN250321GYWUP8J | String | 实例编码
├─ vmStatus | 0 | Integer | 实例状态（0 表示已删除或已关机）
├─ taskId | 13025 | Integer | 后台任务ID

**请求示例**

```json
{
  "clusterCode": "",
  "padCodes": ["ACN250321GYWUP8J"]
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN250321GYWUP8J",
      "vmStatus": 0,
      "taskId": 13025
    }
  ],
  "ts": 1742536423100
}

```

**错误码**

错误码 | 错误说明|操作建议
--- | -- | ---
110096 | 当前实例状态无法删除,请检查实例状态 | 确认实例状态(只有关机状态的实例可以允许被删除)

#### **查询网存集群详情**

用于查询指定集群的存储资源信息，包括已使用存储容量、总存储容量以及剩余可用存储容量等。

**接口地址**

> /openapi/open/pad/net/detail/storageCapacity/available

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---  
clusterCode | "002" | String | 是 | 集群编号，用于查询指定集群的存储资源信息

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1742476224830 | Long | 时间戳
data | {} | Object | 返回的数据对象
data.netStorageResList | null | Object | 网存资源列表
data.storageCapacityUsedTotal | 0 | Integer | 已使用存储容量
data.storageCapacityTotal | 0 | Integer | 总存储容量
data.storageCapacityAvailable | 0 | Integer | 剩余可用存储容量

**请求示例**

```json
{
  "clusterCode": "002"
}
```

**响应示例**

```json
{
  "code": 200,
  "data": [
    {
      "clusterCode": "001",
      "netStorageResList": [
        {
          "clusterCode": "001",
          "createTime": "2025-04-11 11:51:00",
          "dcCode": "001",
          "netStorageResId": 5,
          "storageCapacity": 800,
          "storageCapacityUsed": 0,
          "updateBy": "0",
          "updateTime": "2025-04-23 19:10:00"
        }
      ],
      "storageCapacityAvailable": 800,
      "storageCapacityTotal": 800,
      "storageCapacityUsedTotal": 0
    }
  ],
  "msg": "success",
  "ts": 1745406673542
}
```

#### **设置网存集群板卡规格**

用于设置指定设备的板卡规格等级。

**接口地址**

> /openapi/open/device/net/setDeviceLevel

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---  
deviceCodes | `["ACD250320R4I7OLM", "ACD250320UEH4OV3"]` | Array | 是 | 设备编码列表，包含需要设置规格的板卡的编码
deviceLevel | `"m2-8"` | String | 是 | 设置的设备规格等级

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1742526263389 | Long | 时间戳
data | "" | String | 业务返回信息

**请求示例**

```json
{
  "deviceCodes": ["ACD250320R4I7OLM", "ACD250320UEH4OV3"],
  "deviceLevel": "m2-8"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": "",
  "ts": 1742526263389
}
```

错误码 | 错误说明|操作建议
--- | -- | ---
220002 | 当前规格不存在，请检查参数 | 确认规格代码是否正确
220007 | 操作的板卡不存在，请检查参数 | 确认板卡编码是否正确
220008 | 当前板卡存在运行中的算力单元，无法修改 | 板卡上有运行的实例,无法修改规格,需要先将板卡上的实例关机

#### **网存存储备份**

用于发起网存存储备份任务，系统将为每个传入的网存资源编号生成一个对应的备份任务。

**接口地址**

> /openapi/open/pad/net/storage/pad/backup

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | -- | --- | --- | ---  
remark | 谷歌验证已经登录 | String | 是 | 备份标记信息
padCodes | `["ACN250403SFPIB1N"]` | List&lt;String&gt; | 是 | 实例padCode，最少 1 个，最多 200 个

**响应参数**
网存存储资源编号列表
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1743664828338 | Long | 时间戳
data | `[ { "padCode": "ZSC2504034L6B26X-ACN250403SFPIB1N", "vmStatus": 0, "taskId": 10350 } ]` | List&lt;GeneratePadTaskVO&gt;  | 备份存储code列表

**请求示例**

```json
{
  "padCodes": [
    "ACN250403SFPIB1N"
  ],
  "remark": "谷歌验证已经登录"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ZSC2504034L6B26X-ACN250403SFPIB1N",
      "vmStatus": 0,
      "taskId": 10350
    }
  ],
  "ts": 1743664828338
}
```

错误码 | 错误说明|操作建议
--- | -- | ---
220009 | 无法操作不属于自己的存储单元,请检查参数 | 确认存储code填写正确
220010 | 处于开机中的网存无法备份,请检查数据 | 存储只有关机状态才允许备份
220001 | 当前批次实例中存在未关机的实例，请检查实例状态 |  

#### **指定网存ID的网存实例开机(还原)**

用于为指定网存资源编号（netStorageResUnitCode）绑定的实例开机。如果传入国家code无法匹配,将使用默认code:SG
在进行指定存储开机时，以下情形需要进行拦截：

1. **跨设备类型**：
    - 虚拟机 —— 云真机
    - 云真机 —— 虚拟机

2. **跨真机品牌、型号(实例未开机除外)**：
    - A品牌云真机 —— B品牌云真机（例如：Samsung —— Xiaomi）
    - A品牌1型号云真机 —— A品牌2型号云真机（例如：Samsung Galaxy A53 —— Samsung Galaxy A71）

3. **跨镜像版本**：
    - 安卓13 —— 安卓14
    - 安卓13 —— 安卓10
    - 安卓14 —— 安卓13
    - 安卓14 —— 安卓10
    - 安卓10 —— 安卓13
    - 安卓10 —— 安卓14

**接口地址**

> /openapi/open/pad/net/storage/specifiedCode/on

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---  
clusterCode | "001" | String | 否 | 集群编码
padCode | "ACN250403FKAX2HX" | String | 是 | 实例编号
netStorageResUnitCode | "ZSC250403MMGUFGN-ACN250403ZIMD9KJ" | String | 否 | 网络存储资源编号（可选）
androidProp | {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"} | Object  | 否    | 参考 [安卓改机属性列表](./InstanceAndroidPropList.html)
countryCode | SG                                                  | String  | 否    | 国家编码(具体查看:<https://chahuo.com/country-code-lookup.html>)

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---  
code | 200 | Integer | 状态码（200表示成功）
msg | success | String | 接口请求状态信息
ts | 1743664443188 | Long | 时间戳
data | `{ "padCode": "ACN250403FKAX2HX", "vmStatus": 0, "taskId": 10341 }` | GeneratePadTaskVO | 任务详情

**请求示例**

```json
{
  "clusterCode": "001",
  "padCode": "ACN250403FKAX2HX",
  "netStorageResUnitCode": "ZSC250403MMGUFGN-ACN250403ZIMD9KJ", 
   "countryCode":"US",
   "androidProp": "{\"persist.sys.cloud.wifi.mac\": \"D2:48:83:70:66:0B\"}"
}
```

**响应示例**

```json
{
  "clusterCode": "001",
  "padCode": "ACN250403FKAX2HX",
  "netStorageResUnitCode": "ZSC250403MMGUFGN-ACN250403ZIMD9KJ"
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | -- | ---
110095 | 没有可以开机的实例,请检查实例状态 | 确认实例状态是否为关机状态
220011 | 频繁操作网存实例,请稍后再试 | 稍后再试
110101 | 未找到与实例规格匹配的板卡，请前往板卡列表设置规格。 | 确认实例的规格跟板卡规格一致,并且板卡有算力未被占用

#### **网存存储删除**

用于删除指定网存存储资源。

---

**接口地址**

> POST /openapi/open/pad/net/storage/pad/delete

---

**请求数据类型**

> application/json

---

**请求参数**

| 参数名                   | 示例值                                          | 参数类型     | 是否必填 | 参数描述                     |
|------------------------|----------------------------------------------|------------|---------|----------------------------|
| netStorageResUnitCodes | `["ZSC250407XLYILP4-ACN250407XRY4CW9"]`       | List&lt;String&gt; | 是      | 需要删除的网存存储资源编号列表，最多支持 200 个 |

---

**响应参数**

| 参数名    | 示例值   | 参数类型  | 参数描述                   |
|---------|--------|----------|----------------------------|
| code    | 200    | Integer  | 状态码（200表示成功）        |
| msg     | success| String   | 接口请求状态信息            |
| ts      | 1744026334273 | Long | 时间戳                     |
| data    | `[{"padCode": "ZSC250407XLYILP4-ACN250407XRY4CW9", "vmStatus": 0, "taskId": 10159}]` | List&lt;GeneratePadTaskVO&gt; | 每个存储资源对应的删除任务结果列表 |

---

**请求示例**

```json
{
  "netStorageResUnitCodes": [
    "ZSC250407XLYILP4-ACN250407XRY4CW9"
  ]
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ZSC250407XLYILP4-ACN250407XRY4CW9",
      "vmStatus": 0,
      "taskId": 10159
    }
  ],
  "ts": 1744026334273
}

```

**错误码**
[错误码说明](./ErrorMsgCode.md#系统错误码说明)

#### **获取网存实例使用详情**

用于查询网存实例使用详情，按设备等级分组。

---

**接口地址**

> POST /openapi/open/pad/netPad/group/deviceLevel

---

**请求数据类型**

> application/json

---

**请求参数**

| 参数名        | 示例值   | 参数类型 | 是否必填 | 参数描述                     |
|-------------|--------|--------|---------|----------------------------|
| clusterCode | "001"  | String | 是      | 集群编码                    |

---

**响应参数**

| 参数名        | 示例值   | 参数类型  | 参数描述                   |
|-------------|--------|----------|----------------------------|
| totalNumber | 16     | Long     | 总算力数量                 |
| onNumber    | 2      | Long     | 已开机数量                 |
| deviceLevel | "m2-8" | String   | 规格                       |

---

**请求示例**

```json
{
  "clusterCode": "001"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "totalNumber": 16,
      "onNumber": 2,
      "deviceLevel": "m2-8"
    },
    {
      "totalNumber": 9,
      "onNumber": 1,
      "deviceLevel": "m2-3"
    }
  ],
  "ts": 1744024331175
}

```

#### **网存存储详情查询**

用于查询网存存储资源的详细信息，支持分页查询。

---

**接口地址**

> POST /openapi/open/netStorage/resUnit/net/storage/pad/resUnit

---

**请求数据类型**

> application/json

---

**请求参数**

| 参数名    | 示例值  | 参数类型 | 是否必填 | 参数描述                 |
|---------|--------|--------|---------|------------------------|
| page    | 3      | Integer | 是      | 当前页码                |
| rows    | 10     | Integer | 是      | 每页显示条数            |

---

**响应参数**

| 参数名         | 示例值  | 参数类型         | 参数描述                   |
|--------------|--------|------------------|----------------------------|
| total        | 27     | Integer          | 总记录数                   |
| size         | 7      | Integer          | 当前页记录数               |
| totalPage    | 3      | Integer          | 总页数                     |
| page         | 3      | Integer          | 当前页码                   |
| pageData     | `[...]`| List&lt;NetStorageResUnitVO&gt; | 当前页的数据列表          |

**NetStorageResUnitVO字段说明**

| 字段名                   | 示例值                    | 参数类型 | 参数描述                           |
|------------------------|--------------------------|----------|-----------------------------------|
| netStorageResUnitId     | 87                       | Long     | 网络存储详情 ID                     |
| shutdownFlag            | 0                        | Integer  | 是否有实例开关机（0: 关机，1: 开机） |
| netStorageResUnitCode   | "ZSC25040761D2GP8-ACN250407XRY4CW9" | String   | 网络存储详情 Code                  |
| clusterCode             | "001"                    | String   | 集群 Code                          |
| padCode                 | "ACN250407XRY4CW9"       | String   | 实例 Code                          |
| netStorageResUnitSize   | 16                       | Long     | 网络存储大小 (单位:GB)             |

---

**请求示例**

```json
{
  "page": 3,
  "rows": 10
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": {
    "total": 27,
    "size": 7,
    "totalPage": 3,
    "page": 3,
    "pageData": [
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407XRY4CW9",
        "netStorageResUnitCode": "ZSC25040761D2GP8-ACN250407XRY4CW9",
        "clusterCode": "001",
        "netStorageResUnitId": 87,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407X17VWDY",
        "netStorageResUnitCode": "ZSC250407TIBRYY6-ACN250407X17VWDY",
        "clusterCode": "001",
        "netStorageResUnitId": 88,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407X17VWDY",
        "netStorageResUnitCode": "ZSC2504075ET1IJG-ACN250407X17VWDY",
        "clusterCode": "001",
        "netStorageResUnitId": 89,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407X17VWDY",
        "netStorageResUnitCode": "ZSC250407B4843FH-ACN250407X17VWDY",
        "clusterCode": "001",
        "netStorageResUnitId": 90,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407OFXS319",
        "netStorageResUnitCode": "ZSC250407RK1JROT-ACN250407OFXS319",
        "clusterCode": "001",
        "netStorageResUnitId": 91,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407XRY4CW9",
        "netStorageResUnitCode": "ZSC250407XLYILP4-ACN250407XRY4CW9",
        "clusterCode": "001",
        "netStorageResUnitId": 92,
        "netStorageResUnitSize": 16
      },
      {
        "shutdownFlag": 0,
        "padCode": "ACN250407XRY4CW9",
        "netStorageResUnitCode": "ZSC2504076D4LZPS-ACN250407XRY4CW9",
        "clusterCode": "001",
        "netStorageResUnitId": 93,
        "netStorageResUnitSize": 16
      }
    ],
    "rows": 10
  },
  "ts": 1744023373581
}
```

### 实例管理

#### **修改实例WIFI属性**

修改指定实例的WIFI列表属性（此接口与一建新机设置WIFI二选一，否则会出现覆盖问题）

**接口地址**

> /openapi/open/pad/setWifiList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | -- | ---
padCodes |  | String[] | 是 | 实例编号
wifiJsonList |  | String[] | 是 | wifi属性列表
├─SSID | 110101 | String | 是 | wifi名称  (不支持中文)
├─BSSID | 02:31:00:00:00:01 | String | 是 | 接入点mac地址
├─MAC | 02:00:10:00:00:00 | String | 是 | wifi网卡mac地址
├─IP | 02:00:10:00:00:00 | String | 是 | wifi网络IP
├─MAC | 02:00:10:00:00:00 | String | 是 | wifi网卡mac地址
├─gateway | ************* | String | 是 | wifi网关
├─DNS1 | ******* | String | 是 | DNS1
├─DNS2 | ******* | String | 是 | DNS2
├─hessid | 0 | Integer | 否 | 网络标识符
├─anqpDomainId | 0 | Integer | 否 | ANQP（Access Network Query Protocol）域ID
├─capabilities | "" | String | 否 | WPA/WPA2等信息
├─level | 0 | Integer | 否 | 信号强度（RSSI）
├─linkSpeed | 500 | Integer | 否 | 当前Wi-Fi连接速率
├─txLinkSpeed | 600 | Integer | 否 | 上传链路速度
├─rxLinkSpeed | 700 | Integer | 否 | 下载链路速度
├─frequency | 2134 | Integer | 否 | Wi-Fi信道频率
├─distance | -1 | Integer | 否 | 估算的AP距离
├─distanceSd | -1 | Integer | 否 | 估算距离的标准差
├─channelWidth | 0 | Integer | 否 | 信道带宽
├─centerFreq0 | 0 | Integer | 否 | 中心频率0
├─centerFreq1 | -1 | Integer | 否 | 中心频率1
├─is80211McRTTResponder | false | Boolean | 否 | 是否支持 802.11mc（Wi-Fi RTT，测距技术

**响应参数**

参数名 | 示例值 | 参数类型    | 参数描述
--- | -- |---------| ---
code | 200 | Integer | 状态码
msg | success | String  | 响应消息
ts | ************* | Long    | 时间戳
data |  | Object  |
├─ taskId | 1 | Integer | 任务ID
├─ padCode | AC2025030770R | String | 实例编号
├─ vmStatus | 1 | Integer  | 实例在线状态：0-离线，1-在线

**请求示例**

```javascript
{
    "padCodes":["AC2025030770R92X"],
        "wifiJsonList":[
        {
            "SSID": "110101",
            "BSSID": "02:31:00:00:00:01",
            "MAC": "02:00:10:00:00:00",
            "IP": "*************5",
            "gateway": "*************",
            "DNS1": "*******",
            "DNS2": "*******",
            "hessid": 0,
            "anqpDomainId": 0,
            "capabilities": "",
            "level": 0,
            "linkSpeed": 500,
            "txLinkSpeed": 600,
            "rxLinkSpeed": 700,
            "frequency": 2413,
            "distance": -1,
            "distanceSd": -1,
            "channelWidth": 0,
            "centerFreq0": -1,
            "centerFreq1": -1,
            "is80211McRTTResponder": true
        }
    ]
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1741676855566,
        "data": [
        {
            "taskId": 14904,
            "padCode": "AC2025030770R92X",
            "vmStatus": 0
        }
    ]
}

```

#### **实例详情**

查询指定实例的属性信息，包括系统属性信息和设置信息。

**接口地址**

> /openapi/open/pad/padDetails

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | -- | ---
page | 1 | Integer | 否 | 页码
rows | 10 | Integer | 否 | 每页记录数
padCodes |  | String[] | 否 |
├─ | AC21020010391 | String | 否 | 实例编号
padIps |  | String[] | 否 |
├─ | *********** | String | 否 | 实例ip
vmStatus | 1 | String | 否 | 实例在线状态：0-离线，1-在线
controlStatus | 1 | String | 否 | 受控状态（推流状态）：1-非受控，2-受控
faultStatus | 14 | String | 否 | 实例运行状态 14-异常 其他-正常
deviceStatus | 0 | String | 否 | 物理机在线状态：0-离线，1-在线
groupId | 2 | Integer | 否 | 分组ID
idcCode | HNCS-C-01 | String | 否 | 机房编号

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object |
├─ page | 1 | Integer | 当前页
├─ rows | 10 | Integer | 每页的数量
├─ size | 1 | Integer | 当前页的数量
├─ total | 1 | Integer | 总记录数
├─ totalPage | 1 | Integer | 总页数
├─ pageData |  | object[] | 列表
├─ ├─ padCode | VP21020010391 | String | 实例编号
├─ ├─ imageId | andorid:v10 | String | 镜像ID
├─ ├─ deviceLevel | q2-6 | String | 实例规格（q2-6六开，q2-4四开）
├─ ├─ padStatus | 12 | Integer | 实例状态 （10-运行中 11-重启中 12-重置中 13-升级中 14-异常 15-未就绪）
├─ ├─ deviceStatus | 1 | Integer | 物理机状态（0-离线，1-在线）
├─ ├─ online | 1 | Integer | 实例在线状态（0-离线，1-在线）
├─ ├─ streamStatus | 0 | Integer | 实例推流状态（ 0-空闲 1-推流中）
├─ ├─ dataSize | 234493726720 | Long | 存储总容量(字节)
├─ ├─ dataSizeUsed | 179189956608 | Long | 存储已使用容量(字节)
├─ ├─ dcInfo |  | Object | 机房信息
├─ ├─ ├─ dcCode | dc01 | String | 机房编码
├─ ├─ ├─ dcName | 长沙-01 | String | 机房名称
├─ ├─ ├─ area | 长沙 | String | 地区
├─ ├─ ├─ ossEndpoint | <https://xxx.armcloud.net> | String | OSS公网接口地址
├─ ├─ ├─ ossEndpointInternal | <https://xxx.armcloud.net> | String | OSS内网接口地址
├─ ├─ ├─ ossFileEndpoint | <https://xxx.armcloud.net> | String | 访问文件地址
├─ ├─ ├─ ossScreenshotEndpoint | <https://xxx.armcloud.net> | String | 访问截图地址

**请求示例**

```javascript
{
 "page": 1,
 "rows": 10,
 "padCodes": ["AC21020010391"],
 "padIps":["***********"],
 "vmStatus":"1",
 "controlStatus":"1",
 "faultStatus":"14",
 "deviceStatus":"0"
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": {
  "page": 1,
  "rows": 10,
  "size": 1,
  "total": 1,
  "totalPage": 1,
  "pageData": [
   "padCode": "AC21020010391",
   "imageId": "andorid:v10",
   "deviceLevel": "q2-6",
   "dcInfo": {
    "dcCode": "dc01",
    "dcName": "长沙-01",
    "area": "长沙",
    "ossEndpoint": "https://xxx.armcloud.net",
    "ossEndpointInternal": "https://xxx.armcloud.net",
    "ossFileEndpoint": "https://xxx.armcloud.net",
    "ossScreenshotEndpoint": "https://xxx.armcloud.net",
   },
   "padStatus": 12,
   "deviceStatus": 1,
   "online": 1,
   "streamStatus": 0,
   "dataSize": 234493726720,
   "dataSizeUsed": 179189956608
  ]
 }
}

```

**代码示例**

```java
// java 调用示例
PadDetailsRequest requestParam = new PadDetailsRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);
Result<Page<PadDetailsResponse>> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_DETAILS, requestParam,new TypeReference<Result<Page<PadDetailsResponse>>>() {});
```

#### **实例重启**

对指定实例执行重启操作，用以解决系统无响应、卡死等问题。

**接口地址**

> /openapi/open/pad/restart

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名  | 示例值        | 参数类型    | 是否必填 | 参数描述      |
| ------- | ------------- |---------|------|-----------|
|padCodes |  | String[] | 是    |           |
|├─ | AC21020010001 | String  | 是    | 实例编号      |
|groupIds |  | Integer[] | 否    |           |
|├─ | 1 | Integer | 否    | 实例组ID     |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | ---|
|code | 200 | Integer | 状态码|
|msg | success | String | 响应消息|
|ts | ************* | Long | 时间戳|
|data | | Object[] | |
|├─taskId | 1 | Integer | 任务ID|
|├─padCode | AC21020010001 | String | 实例编号|
|├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）|

**请求示例**

```javascript
{
 "padCodes": [
  "AC22030022693"
 ],
 "groupIds": [1]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data":[
    {
    "taskId": 1,
    "padCode": "AC21020010001",
    "vmStatus": 1
    }
   ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
10001 | 重启失败| 联系管理员
110004 | 执行重启命令失败| 稍后再次重启
110028 | 实例不存在| 请检查实例是否存在

**代码示例**

```java
// java 调用示例
public class SDKExample {
    private final ArmCloudApiService armCloudApiService;

    public SDKExample() {
        ArmCloudConfig armCloudConfig = new ArmCloudConfig();
        armCloudConfig.setOpenUrl("https://openapi-hk.armcloud.net");
        armCloudConfig.setService("armcloud-paas");
        armCloudConfig.setHost("openapi.armcloud.net");
        armCloudConfig.setAk("your access_key_id");
        armCloudConfig.setSk("your secret_access_key");
        armCloudApiService = new ArmCloudApiServiceImpl(armCloudConfig, new DefaultHttpExecutor());
    }

    @Test
    public void test() throws Exception {
        RestartRequest requestParam = new RestartRequest();
        List<String> padCodes = new ArrayList<>();
        padCodes.add("AC22010041147");
        requestParam.setPadCodes(padCodes);
        Result<PadTaskResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_RESTART, requestParam, new TypeReference<Result<PadTaskResponse>>() {});
    }
}
```

#### **实例重置**
>
> 注意：实例重置清除系统所有数据。请谨慎使用,每次重置之后会将公网IP恢复到默认值

对实例执行重置操作，将会清理云机所有数据

**接口地址**

> /openapi/open/pad/reset

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值         | 参数类型  | 是否必填 | 参数描述   |
| -------- | -------------- | --------- | -------- | ---------- |
| padCodes |                | String[]  | 是       |            |
| ├─       | AC21020010001  | String    | 是       | 实例编号   |
| groupIds |                | Integer[] | 否       |            |
| ├─       | 1              | Integer   | 否       | 实例组ID   |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String | 响应消息 |
|ts | ************* | Long | 时间戳 |
|data |  | Object[] | |
|├─taskId | 1 | Integer |  任务ID|
|├─padCode | AC21020010001 | String | 实例编号 |
|├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
 "padCodes": [
  "AC21020010001"
 ],
 "groupIds": [1]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717559681604,
 "data": [
  {
   "taskId": 88,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 89,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

|错误码 | 错误说明 | 操作建议|
|--- | --- | --- |
|10002 | 重置失败 | 联系管理员|
|110005 | 执行重置命令失败 | 稍后再次重置|

**代码示例**

```java
// java 调用示例
 ResetRequest requestParam = new ResetRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);
Result<PadTaskResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_RESET, requestParam, new TypeReference<Result<PadTaskResponse>>() {});
```

#### **查询实例属性**

查询指定实例的属性信息，包括系统属性信息和设置信息。

**接口地址**

> /openapi/open/pad/padProperties

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
|--- | --- | --- | --- | --- |
|padCode | AC21020010001 | String | 是 | 实例编号 |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述 |
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String| 响应消息 |
|ts | ************* | Long |  时间戳 |
|data |  | Object |  |
|├─padCode | AC21020010001 | String | 实例编号 |
|├─modemPropertiesList |  | Object[] | Modem-属性列表 |
|├─├─propertiesName | IMEI | String | 属性名称 |
|├─├─propertiesValue | 412327621057784 | String | 属性值 |
|├─systemPropertiesList|  | Object[] | 系统-属性列表 |
|├─├─propertiesName | ro.build.id | String | 属性名称 |
|├─├─propertiesValue | QQ3A.200805.001 | String | 属性值 |
|├─settingPropertiesList |  | Object[] | setting-属性列表 |
|├─├─propertiesName | ro.build.tags | String | 属性名称 |
|├─├─propertiesValue | release-keys | String | 属性值 |
|├─oaidPropertiesList |  | Object[] | oaid-属性列表 |
|├─├─propertiesName | oaid | String | 属性名称 |
|├─├─propertiesValue | 001 | String | 属性值 |

**请求示例**

```javascript
{
 "padCode": "AC21020010001"
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": {
  "padCode": "AC21020010001",
  "modemPropertiesList": [
   {
    "propertiesName": "IMEI",
    "propertiesValue": "412327621057784"
   }
  ],
  "systemPropertiesList": [
   {
    "propertiesName": "ro.build.id",
    "propertiesValue": "QQ3A.200805.001"
   }
  ],
  "settingPropertiesList": [
   {
    "propertiesName": "ro.build.tags",
    "propertiesValue": "release-keys"
   }
  ],
  "oaidPropertiesList": [
   {
    "propertiesName": "oaid",
    "propertiesValue": "001"
   }
  ]
 }
}
```

**错误码**

|错误码 | 错误说明 | 操作建议 |
| ----- | -------- | ------ |
|110028 | 实例不存在 | 请检查实例是否正确 |

**代码示例**

```java
// java 调用示例
PadPropertiesRequest requestParam = new PadPropertiesRequest();
requestParam.setPadCode("AC22010041147");
Result<PadPropertiesResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_GET_PROPERTIES, requestParam,new TypeReference<Result<PadPropertiesResponse>>() {});
```

#### **批量查询实例属性**

批量查询指定实例的属性信息，包括系统属性信息和设置信息。

**接口地址**

> /openapi/open/pad/batchPadProperties

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值           | 参数类型 | 是否必填 | 参数描述 |
|--- |---------------| --- | --- | --- |
|padCodes |               | String[] | 是 | |
|├─ | AC21020010001 | String | 是 | 实例编号|
|├─ | AC21020010002 | String | 是 | 实例编号|

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述 |
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String| 响应消息 |
|ts | ************* | Long |  时间戳 |
|data |  | Object[] | |
|├─padCode | AC21020010001 | String | 实例编号 |
|├─modemPropertiesList |  | Object[] | Modem-属性列表 |
|├─├─propertiesName | IMEI | String | 属性名称 |
|├─├─propertiesValue | 412327621057784 | String | 属性值 |
|├─systemPropertiesList|  | Object[] | 系统-属性列表 |
|├─├─propertiesName | ro.build.id | String | 属性名称 |
|├─├─propertiesValue | QQ3A.200805.001 | String | 属性值 |
|├─settingPropertiesList |  | Object[] | setting-属性列表 |
|├─├─propertiesName | ro.build.tags | String | 属性名称 |
|├─├─propertiesValue | release-keys | String | 属性值 |
|├─oaidPropertiesList |  | Object[] | oaid-属性列表 |
|├─├─propertiesName | oaid | String | 属性名称 |
|├─├─propertiesValue | 001 | String | 属性值 |

**请求示例**

```javascript
{
    "padCodes": [
        "AC21020010001",
        "AC21020010002"
    ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": [
        {
            "padCode": "AC21020010001",
            "modemPropertiesList": [
                {
                    "propertiesName": "IMEI",
                    "propertiesValue": "412327621057784"
                }
            ],
            "systemPropertiesList": [
                {
                    "propertiesName": "ro.build.id",
                    "propertiesValue": "QQ3A.200805.001"
                }
            ],
            "settingPropertiesList": [
                {
                    "propertiesName": "ro.build.tags",
                    "propertiesValue": "release-keys"
                }
            ],
            "oaidPropertiesList": [
                {
                    "propertiesName": "oaid",
                    "propertiesValue": "001"
                }
            ]
        },
        {
            "padCode": "AC21020010002",
            "modemPropertiesList": [
                {
                    "propertiesName": "IMEI",
                    "propertiesValue": "412327621057784"
                }
            ],
            "systemPropertiesList": [
                {
                    "propertiesName": "ro.build.id",
                    "propertiesValue": "QQ3A.200805.001"
                }
            ],
            "settingPropertiesList": [
                {
                    "propertiesName": "ro.build.tags",
                    "propertiesValue": "release-keys"
                }
            ],
            "oaidPropertiesList": [
                {
                    "propertiesName": "oaid",
                    "propertiesValue": "001"
                }
            ]
        }
    ]
}
```

**错误码**

|错误码 | 错误说明 | 操作建议 |
| ----- | -------- | ------ |
|110028 | 实例不存在 | 请检查实例是否正确 |

**代码示例**

```java
// java 调用示例
PadPropertiesRequest requestParam = new PadPropertiesRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC21020010001");
padCodes.add("AC21020010002");
requestParam.setPadCodes(padCodes);
Result<List<PadPropertiesResponse>> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_POST_PROPERTIES, requestParam,new TypeReference<Result<PadPropertiesResponse>>() {});
```

#### **修改实例属性**

动态修改实例的属性信息，包括系统属性和设置

实例需要处于开机状态，该接口为即时生效

参考 [实例属性列表](./InstanceList.html#modem-properties-属性列表)

**接口地址**

> /openapi/open/pad/updatePadProperties

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述|
|--- | --- | --- | --- | ---|
|padCodes |  | String[] | 是 | |
|├─ | AC21020010001 | String | 是 | 实例编号|
|modemPersistPropertiesList |  | Object[] | 否 | Modem-持久化-属性列表|
|├─propertiesName | IMEI | String | 否 | 属性名称|
|├─propertiesValue | 412327621057784 | String | 否 | 属性值|
|modemPropertiesList |  | Object[] | 否 | Modem-非持久化-属性列表|
|├─propertiesName | IMEI | String | 否 | 属性名称|
|├─propertiesValue | 412327621057784 | String | 否 | 属性值|
|systemPersistPropertiesList |  | Object[] | 否 | 系统-持久化-属性列表|
|├─propertiesName | ro.build.id | String | 否 | 属性名称|
|├─propertiesValue | QQ3A.200805.001 | String | 否 | 属性值|
|systemPropertiesList |  | Object[] | 否 | 系统-非持久化-属性列表|
|├─propertiesName | ro.build.id | String | 否 | 属性名称|
|├─propertiesValue | QQ3A.200805.001 | String | 否 | 属性值|
|settingPropertiesList |  | Object[] | 否 | setting-属性列表|
|├─propertiesName | ro.build.tags | String | 否 | 属性名称|
|├─propertiesValue | release-keys | String | 否 | 属性值|
|oaidPropertiesList |  | Object[] | 否 | oaid-属性列表|
|├─propertiesName | oaid | String | 否 | 属性名称|
|├─propertiesValue | 001 | String | 否 | 属性值|

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String | 响应消息 |
|ts | ************* | Long | 时间戳 |
|data |  | Object[] | |
|├─taskId | 1 | Integer |  任务ID|
|├─padCode | AC21020010001 | String | 实例编号 |
|├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
 "padCodes": [
  "AC21020010001"
 ],
 "modemPersistPropertiesList": [
   {
    "propertiesName": "IMEI",
    "propertiesValue": "412327621057784"
   }
  ],
 "modemPropertiesList": [
   {
    "propertiesName": "IMEI",
    "propertiesValue": "412327621057784"
   }
  ],
  "systemPersistPropertiesList": [
   {
    "propertiesName": "ro.build.id",
    "propertiesValue": "QQ3A.200805.001"
   }
  ],
  "systemPropertiesList": [
   {
    "propertiesName": "ro.build.id",
    "propertiesValue": "QQ3A.200805.001"
   }
  ],
  "settingPropertiesList": [
   {
    "propertiesName": "ro.build.tags",
    "propertiesValue": "release-keys"
   }
  ],
  "oaidPropertiesList": [
   {
    "propertiesName": "oaid",
    "propertiesValue": "001"
   }
  ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570916196,
 "data": [
  {
   "taskId": 36,
   "padCode": "AC22030010001",
   "vmStatus": 1
  }
 ]
}
```

**错误码**

|错误码 | 错误说明 | 操作建议 |
| --- | --- | --- |
|110011 | 执行修改属性命令失败 | 请稍后重试 |
|110028 | 实例不存在 | 请检查实例是否正确 |
|110027 | 实例编号集合存在重复项 | 请检查实例是否存在重复的 |

**代码示例**

```java
// java 调用示例
UpdatePadPropertiesRequest requestParam = new UpdatePadPropertiesRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);

List<PadPropertiesSub> subs = new ArrayList<>();
PadPropertiesSub sub = new PadPropertiesSub();
sub.setPropertiesName("oaid");
sub.setPropertiesValue("123456789");
subs.add(sub);
requestParam.setSystemPropertiesList(subs);
Result<List<GeneratePadResponse>> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_UPDATE_PROPERTIES, requestParam,new TypeReference<Result<List<GeneratePadResponse>>>() {});
```

#### **修改实例安卓改机属性**

静态设置安卓改机属性，需要重启实例才能够生效，一般用于修改设备信息

该接口与[修改实例属性](./OpenAPI.md#修改实例属性)接口的区别在于生效时机，该接口生效时间为每次开机初始化

<span style="color: red;">设置实例属性后，属性数据会持久化存储，重启或重置实例无需再调用该接口</span>

参考 [安卓改机属性列表](./InstanceAndroidPropList.html)

**使用该接口需要对安卓系统有一定的理解**，部分属性可能会影响实例正常启动，如果改机后，导致实例异常，可以通过调用实例重置接口恢复正常

**接口地址**

> /openapi/open/pad/updatePadAndroidProp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名                   | 示例值        | 参数类型 | 是否必填 | 参数描述                        |
| ------------------------ | ------------- | -------- | -------- | ------------------------------- |
| padCode                  | AC32010210001 | String   | 否       | 实例id                          |
| restart                  | false         | Boolean  | 否       | 设置完成后自动重启（默认false） |
| props                    | {}            | Object   | 是       | 系统属性（此字段为key-value定义） |
| ├─ro.product.vendor.name | OP52D1L1      | String   | 是       | 属性设置                        |

**响应参数**

| 参数名    | 示例值        | 参数类型 | 参数描述 |
| --------- | ------------- | -------- | -------- |
| code      | 200           | Integer  | 状态码   |
| msg       | success       | String   | 响应消息 |
| ts        | ************* | Long     | 时间戳   |
| data      |               | Object   |          |
| ├─taskId  | 24            | Long     | 任务id   |
| ├─padCode | AC32010210001 | String   | 实例id   |

**请求示例**

```javascript
{
 "padCode": "AC32010210001",
 "props": {
  "ro.product.vendor.name": "OP52D1L1"
 },
 "restart": false
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": {
  "taskId": 11,
  "padCode": "AC32010210001"
 }
}
```

#### **根据国家Code修改SIM卡信息**

静态设置安卓改机属性，需要重启实例才能够生效，一般用于修改设备信息

该接口与[修改实例安卓改机属性](./OpenAPI.md#修改实例安卓改机属性)接口具有相同功能.区别在于该接口会随机生成SIM卡信息,并且每次都会重启

<span style="color: red;">设置实例属性后，属性数据会持久化存储，重启或重置实例无需再调用该接口</span>

**接口地址**

> /openapi/open/pad/replacePadAndroidPropByCountry

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名                      | 示例值        | 参数类型 | 是否必填 | 参数描述                  |
|--------------------------| ------------- | -------- |------|-----------------------|
| padCode                  | AC32010210001 | String   | 是    | 实例id                  |
| countryCode              | US | String   | 否    | 国家code                |
| props                    | {}            | Object   | 否    | 系统属性（此字段为key-value定义） |
| ├─ro.product.vendor.name | OP52D1L1      | String   | 否    | 属性设置                  |

**响应参数**

| 参数名    | 示例值        | 参数类型 | 参数描述 |
| --------- | ------------- | -------- | -------- |
| code      | 200           | Integer  | 状态码   |
| msg       | success       | String   | 响应消息 |
| ts        | ************* | Long     | 时间戳   |
| data      |               | Object   |          |
| ├─taskId  | 24            | Long     | 任务id   |
| ├─padCode | AC32010210001 | String   | 实例id   |

**请求示例**

```javascript
{
    "padCode": "AC32010250001",
        "props": {
        "persist.sys.cloud.phonenum": "1234578998"
    },
    "countryCode": "US"
}
```

**响应示例1**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1738910485368,
        "data": {
        "taskId": 14017,
            "padCode": "AC32010250001",
            "vmStatus": 0
    }
}
```

**响应示例2**
```javascript
{
   "code": 500,
   "msg": "目前不支持国家编码: XX",
   "ts": 1753521350163,
   "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ----
110065 | 参数请求不合规,请参考接口文档 | 检查参数,参考接口文档
120005 | 实例不存在 | 请检查实例编号是否正确

#### **停止推流**

停止指定实例推流，断开实例连接。

**接口地址**

> /rtc/open/room/dissolveRoom

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名  | 示例值        | 参数类型 | 是否必填 | 参数描述 |
| ------- | ------------- | -------- | -------- | -------- |
|padCodes |  | String[] | 是 |  |
|├─ | AC11010000031 | String | 是 | 实例编号 |
|├─ | AC22020020700 | String | 是 | 实例编号 |

**响应参数**

| 参数名 | 示例值 | 参数类型 | 参数描述|
| --- | --- | --- | ---|
| code | 200 | Integer |  状态码|
| msg | success | String |  响应消息||
| ts | ************* | Long |  时间戳|
| data |  | Object[] |  |
| ├─successList |  | Object[] |  成功集合|
| ├─├─padCode | AC11010000031 | String |  实例编号|
| ├─failList |  | Object[] |  失败集合|
| ├─├─padCode | AC22020020700|String |  实例编号|
| ├─├─errorCode |120005 |Integer |  错误码|
| ├─├─errorMsg |实例不存在 |String |  失败的原因|

**请求示例**

```javascript
{
    "padCodes": ["AC11010000031","AC22020020700"]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
     "data": {
     "successList": [
              {
                  "padCode": "AC11010000031"
              }
          ],
          "failList": [
      {
                  "padCode": "AC22020020700",
      "errorCode": 120005,
      "errorMsg": "实例不存在"
              }
    ]
     }
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ----
120005 | 实例不存在 | 请检查实例编号是否正确
120004 | 中止推流错误，指令服务异常 | 请稍后重试

#### **批量申请RTC连接Token**

批量申请当前账号下多个实例的RTC Token连接信息，连接信息根据Pad进行分组返回。

> 当Pad无法连接时不会返回RTC Toekn信息，`msg`字段会显示无法连接原因。
>
> 目前此接口仅支持生产 ArmcloudRTC Token。如出现`此接口暂不支持此功能`等异常消息时请联系相关人员重新配置推流信息
>
> 生成的token暂不支持刷新延长有效期。当过期后需重新申请新token
>
> 加密数据需使用AES GCM模式解密，密钥为padCode

**接口地址**

> /rtc/open/room/batchApplyToken

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名         | 示例值        | 参数类型 | 是否必填 | 参数描述                                                     |
| -------------- | ------------- | -------- | -------- | ------------------------------------------------------------ |
| userId         | 202           | String   | 是       | 业务方自定义用户ID，此字段用来生成房间信息。实现一个用户跟GameServer处于一个房间。调用方需做用户级唯一性，如使用同一个userId生成不同的Toekn连接同一个GamerServer会导致上一个已建立的连接断开 |
| expire         | 3600          | Integer  | 否       | token有效期（单位：秒。默认86400）                           |
| pads           |               | Object[] | 是       | 实例列表                                                     |
| ├─padCode      | AC11010000031 | String   | 是       | 实例编号                                                     |
| ├─videoStream  |               | Object   | 否       | 推流配置                                                     |
| ├─├─resolution | 07            | String   | 否       | 分辨率                                                       |
| ├─├─frameRate  | 30            | String   | 否       | 帧率                                                         |
| ├─├─bitrate    | 2000          | String   | 否       | 码率                                                         |

**响应参数**

| 参数名 | 示例值 | 参数类型 | 参数描述|
| --- | --- | --- | ---|
| code | 200 | Integer |  状态码|
| msg | success | String |  响应消息|
| ts | ************* | Long |  时间戳|
| data |  | Object[] |  |
| ├─roomToken | 001j7Tb2jAyAzR6UtLv3cgclCFhw6Q== | String | token |
| ├─roomCode | AC22030010181202 | String | 房间号 |
| ├─appId | j7Tb2GcE9rN5oF6xP3A4qwer | String | 应用ID |
| ├─padCode | AC22030010181 | String | 实例ID |
| ├─signalServer | LnBbVX4AD1CA4uyoN1kXp:P8H01PaGZDHEFD | String | 信令地址(需使用AES GCM模式解密) |
| ├─stuns | pL25iYgaA12RNmdCYR/:SUJD21Bz4S6HE88GzVN | String | stuns地址(需使用AES GCM模式解密) |
| ├─turns | doF22kA7Z6OiVP1br29:rA1R4d6Vyk9e | String | turns地址(需使用AES GCM模式解密) |
| ├─msg | connect pad fail | String | 错误信息 |

**请求示例**

```javascript
{
 "userId": "202",
 "expire": 3600,
 "pads": [
  {
   "padCode": "AC22010010842",
   "videoStream ": {
    "resolution": "1",
    "frameRate": "30",
    "bitrate": "2000"
   }
  },
  {
   "padCode": "AC22030010181",
   "videoStream ": {
    "resolution": "1",
    "frameRate": "30",
    "bitrate": "2000"
   }
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1721305008916,
 "data": [
  {
   "roomToken": null,
   "roomCode": "AC22010010842202",
   "appId": "j7Tb2GcE9rN5oF6xP3A4qwer",
   "streamType": 2,
   "videoCodec": "",
   "reportSdkLog": false,
   "padCode": "AC22010010842",
   "msg": "connect pad fail",
   "signalServer": null,
   "stuns": null,
   "turns": null
  },
  {
   "roomToken": "001j7Tb2GcE9rN15oF6xP3A4qwerNwDyCHIRtQrGxZABAAC1ZuzKkAEAABAAQUMyMjAzMDAxMDE4MA12D1TIwMgMAMjAyAQAEALVm7MqQAQAAIABpLvj5zX3dnyN/8UvRsLJnHWA4zR6UtLv3cgclCFhw6Q==",
   "roomCode": "AC22030010181202",
   "appId": "j7Tb2GcE9rN5oF6xP3A4qwer",
   "streamType": 2,
   "videoCodec": "",
   "reportSdkLog": false,
   "padCode": "AC22030010181",
   "msg": null,
   "signalServer": "LnBbVX4AD1CA4uyoN1kXp:P8H01PaGZDHEFDsnU6nRCbOFzvL2smbG9HxKh+XP5WHC",
   "stuns": "pL25iYgaA12RNmdCYR/:SUJD21Bz4S6HE88GzVN8rANlfL9925iaHW+ilJAaWldPpoBKqwoEq0Ggon0HhDc4a6v0pg=",
   "turns": "doF22kA7Z6OiVP1br29:rA1R4d6Vyk9efIFX6qPPMyKs7OhmxFA7xBr65P8NA/Rxb31Js6VOaO3Zrtd3h9uM/mNYUy5mJOQ4j8TJ8DjfBFaEHVNOAcF5tzgbg8iksGhNONfv8hHw=="
  }
 ]
}
```

**错误码**

| 错误码 | 错误说明             | 操作建议                 |
| ------ | -------------------- | ------------------------ |
| 120005 | 实例不存在           | 请检查实例编号是否正确   |
| 120007 | 此接口暂不支持此功能 | 联系相关人员更改推流配置 |

#### **申请RTC共享房间Token**

用于实现多个实例在一个房间中，实例接收房间广播消息进行处理与实现获取公共流

> 房间号生成规则：terimer + userId + paas用户标识
> 例如：terimer = pc，userId=123，paas用户标识=qwer
>
> 房间号为：pc123qwer

**接口地址**

> /rtc/open/room/share/applyToken

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名            | 示例值        | 参数类型 | 是否必填 | 参数描述                      |
| ----------------- | ------------- | -------- | -------- | ----------------------------- |
| userId            | 202           | String   | 是       | 调用方业务用户ID              |
| terminal          | pc            | String   | 是       | 终端                          |
| expire            | 3600          | Integer  | 否       | token有效期 单位秒。默认1小时 |
| pushPublicStream  | true          | Boolean  | 否       | 是否推送公共流（默认false）   |
| pads              |               | Array    | 是       | 需要加入共享房间的实例列表    |
| ├─padCode         | AC22010010842 | String   | 是       | 实例id                        |
| ├─├─videoStream   |               | Object   | 否       | 推流配置                      |
| ├─├─├─videoStream | 1             | String   | 否       |                               |
| ├─├─├─frameRate   | 30            | String   | 否       |                               |
| ├─├─├─bitrate     | 2000          | String   | 否       |                               |

**响应参数**

| 参数名      | 示例值                                 | 参数类型 | 参数描述  |
| ----------- | -------------------------------------- | -------- | --------- |
| code        | 200                                    | Integer  | 状态码    |
| msg         | success                                | String   | 响应消息  |
| ts          | *************                          | Long     | 时间戳    |
| data        |                                        | Object   |           |
| ├─roomToken | 00165b7AS149e52467a4016f050b8cQQBDjDJKuTAb | String   | 房间Token |
| ├─roomCode  | android_12345                          | String   | 房间号    |
| ├─appId     | 65b749e52467a4016f050b8c               | String   | 应用id    |

**请求示例**

```javascript
{
 "userId": "202",
 "terminal": "pc",
 "expire": 3600,
 "pushPublicStream": true,
 "pads": [
  {
   "padCode": "AC22010010842",
   "videoStream": {
    "resolution": "1",
    "frameRate": "30",
    "bitrate": "2000"
   }
  },
  {
   "padCode": "AC22030010181",
   "videoStream": {
    "resolution": "1",
    "frameRate": "30",
    "bitrate": "2000"
   }
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": null,
 "data": {
  "roomToken": "00165b7AS149e52467a4016f050b8cQQBDjDJKuTAbnZVwU52UOAENSTTExMDEwMDAwMDExAwAxMjMFAAAAXBTnZQEAXBTnZQIAXBTnZQMAXBTnZQQAAAAAACAADCbuyT9crLX9MNUCWyFhsFXwb4nuFPxfgE7MqHjv4yQ=",
  "roomCode": "android_12345",
  "appId": "65b749e52467a4016f050b8c"
 }
}
```

#### **修改实例时区**

**接口地址**

> /openapi/open/pad/updateTimeZone

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值        | 参数类型 | 是否必填 | 参数描述    |
| -------- | ------------- | -------- | -------- | ----------- |
| timeZone | Asia/Shanghai | String   | 是       | UTC标准时间 |
| padCodes |               | Array    | 是       | 实例列表    |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- | -------- | -------------------------------- |
| code       | 200           | Integer  | 状态码                           |
| msg        | success       | String   | 响应消息                         |
| ts         | ************* | Long     | 时间戳                           |
| data       |               | Object   |                                  |
| ├─taskId   | 24            | Long     | 任务ID                           |
| ├─padCode  | AC22030010001 | String   | 房间号                           |
| ├─vmStatus | 实例状态      | Integer  | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
 "padCodes": [
  "AC32010140003"
 ],
 "timeZone": "Asia/Shanghai"
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": [
  {
   "taskId": 24,
   "padCode": "AC32010140003",
   "vmStatus": 1
  }
 ]
}
```

#### **修改实例语言**

**接口地址**

> /openapi/open/pad/updateLanguage

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| -------- | ------ | -------- | -------- | -------- |
| language | zh     | String   | 是       | 语言     |
| country  | CN     | String   | 否       | 国家     |
| padCodes |        | Array    | 是       | 实例列表 |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- | -------- | -------------------------------- |
| code       | 200           | Integer  | 状态码                           |
| msg        | success       | String   | 响应消息                         |
| ts         | ************* | Long     | 时间戳                           |
| data       |               | Object   |                                  |
| ├─taskId   | 24            | Long     | 任务ID                           |
| ├─padCode  | AC22030010001 | String   | 房间号                           |
| ├─vmStatus | 实例状态      | Integer  | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
 "padCodes": [
  "AC32010140026"
 ],
 "language": "zh",
 "country": ""
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": [
  {
   "taskId": 24,
   "padCode": "AC32010140026",
   "vmStatus": 1
  }
 ]
}
```

#### **修改实例SIM卡信息**

**接口地址**

> /openapi/open/pad/updateSIM

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名           | 示例值                  | 参数类型   | 是否必填 | 参数描述                       |
| ---------------- |----------------------|--------|------|----------------------------|
| imei             | 868034031518269      | String | 否    | IMEI号                      |
| operatorShortnam | CMSS                 | String | 否    | 运营商简称                      |
| imsi             | 460074008004488      | String | 否    | 前缀为sim卡运营商号：MCC(3位)+MNC(2位或3位) |
| phonenum         | 18511112222          | String | 否    | 电话号码                       |
| mcc       | 502                  | String | 否    | 网络所属国家                     |
| mnc       | 146                  | String | 否    | 移动设备网络代码                   |
| padCodes         |                      | Array  | 是    | 实例列表                       |
| type         | 1                    | String | 是    | 网络类型                       |
| tac         | 871420                    | String | 是    | 追踪区域码                      |
| cellid         | 870091003                    | String | 是    | 基站 ID                      |
| narfcn         | 99240                    | String | 是    | 用于 5G 网络中标识信道的频点编号         |
| physicalcellid         | 6C                    | String | 是    | 用来标识 5G 或 LTE 网络中的特定小区     |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- | -------- | -------------------------------- |
| code       | 200           | Integer  | 状态码                           |
| msg        | success       | String   | 响应消息                         |
| ts         | ************* | Long     | 时间戳                           |
| data       |               | Object   |                                  |
| ├─taskId   | 24            | Long     | 任务ID                           |
| ├─padCode  | AC22030010001 | String   | 房间号                           |
| ├─vmStatus | 实例状态      | Integer  | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
    "padCodes": ["AC32010230011"],
        "imei": "868034031518269",
        "imeisv": "00",
        "meid": "A0000082C65F6C",
        "operatorLongname": "CHINA MOBILE",
        "operatorShortnam": "CMSS",
        "operatorNumeric": "455555",
        "spn": "China",
        "iccid": "89860002191807255576",
        "imsi": "460074008004488",
        "phonenum": "861234566",
        "netCountry": "US",
        "simCountry": "US",
        "type": "9",
        "mcc": "502",
        "mnc": "146",
        "tac": "871420",
        "cellid": "870091003",
        "narfcn": "99240",
        "physicalcellid": "6C"
}

```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": [
  {
   "taskId": 24,
   "padCode": "AC32010140033",
   "vmStatus": 1
  }
 ]
}
```

#### **设置实例经纬度**

**接口地址**

> /openapi/open/pad/gpsInjectInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名    | 示例值     | 参数类型 | 是否必填 | 参数描述 |
| --------- | ---------- | -------- | -------- | -------- |
| longitude | 116.397455 | Float    | 是       | 纬度     |
| latitude  | 39.909187  | Float    | 是       | 经度     |
| altitude  | 8          | Float    | 否       | 海拔 (需要更新到最新镜像)    |
| padCodes  |            | Array    | 是       | 实例列表 |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- |--------| ------------------------------- |
| code       | 200           | Integer | 状态码                           |
| msg        | success       | String | 响应消息                         |
| ts         | ************* | Long   | 时间戳                           |
| data       |               | Boolean    |                                 |

**请求示例**

```javascript
{
 "padCodes": [
  "AC32010030001"
 ],
 "longitude": 116.397455,
 "latitude": 39.909187,
 "altitude": 8
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": true
}
```

#### **查询实例代理信息**

**接口地址**

> /openapi/open/network/proxy/info

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| -------- | ------ | -------- | -------- | -------- |
| padCodes |        | Array    | 是       | 实例列表 |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- | -------- | -------------------------------- |
| code       | 200           | Integer  | 状态码                           |
| msg        | success       | String   | 响应消息                         |
| ts         | ************* | Long     | 时间戳                           |
| data       |               | Object   |                                  |
| ├─taskId   | 24            | Long     | 任务ID                           |
| ├─padCode  | AC22030010001 | String   | 房间号                           |
| ├─vmStatus | 实例状态      | Integer  | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
  "padCodes": [
    "AC32010140012"
  ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1726742932284,
 "data": [
  {
   "taskId": 3612,
   "padCode": "AC32010140012",
   "vmStatus": 1
  }
 ]
}
```

<!-- #### **实例数据备份**

1. 备份当前实例数据至云空间，后续可从云空间还原数据到其他实例。数据备份方式为全量备份，备份数据的内容包括以下

- 已安装的APP
- 实例磁盘上的所有数据（图库文件、下载的文件等）
- 改机属性

2. 数据备份名称规则：[backupNamePrefix_]padCode_yyyyMMddHHmmss,
   
   例如backupNamePrefix为****test****，padCode为****AC32010100001****。备份名称全称为：****test_AC32010100001_20241211174508****

**接口地址**

> /openapi/open/pad/data/backup

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes | AC32010100001 | [] | 是 | 实例id列表
backupNamePrefix | test | String | 否 | 数据备份名称前缀

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |  | Object |
├─taskId | 12818 | Long | 任务ID
├─padCode | AC22030010124 | String | 实例编号

**请求示例**

```json
{
  "padCodes": [
    "AC32010100001"
  ],
  "backupNamePrefix": "test"
}
```

**响应示例**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1733212452565,
  "data": [
    {
      "taskId": 2447,
      "padCode": "AC32010100001",
      "backupName": "test_AC32010100001_20241203155412"
    }
  ]
}
``` -->

<!-- #### **备份文件删除**

**接口地址**

> /openapi/open/pad/data/del

**请求方式**

> POST（删除备份数据，支持批量删除，一批最多支持200个）

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | -- | --- | ---
padCode | AC32010100001 | String | 是 | 实例编号
backupName | 12345_AC32010230011_20241218143659 | String | 是 | 数据备份名称

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |  | Boolean |

**请求示例**

```json
{
  "dataDelDTOS": [
    {
      "backupName": "OpenApi_AC32010230012_20241220161011",
      "padCode": "AC32010230012"
    },
    {
      "backupName": "12345_AC32010230011_20241218143659",
      "padCode": "AC32010230011"
    }
  ]
}
```

**响应示例(全部成功)**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1733212452565,
  "data": true
}
```

**响应示例(部分成功)**

```json
{
  "code": 200,
  "msg": "删除成功,存在无效实例编码与数据备份名称",
  "ts": 1734934603646,
  "data": [
    {
      "padCode": "AC32010230011",
      "backupName": "OpenApi_AC32010230012_20241220161011"
    }
  ]
}
``` -->

<!-- #### **实例数据恢复**
> 注意：因数据是全量替换，有可能会当前实例导致数据丢失。请谨慎使用！

把备份到云空间的数据恢复到指定实例，恢复数据的内容包括以下

- 已安装的APP
- 实例磁盘上的所有数据（图库文件、下载的文件等）
- 改机属性

**接口地址**

> /openapi/open/pad/data/restore

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes | [] | Array | 是 | 实例id列表
backupName | test_AC32010100001_20241211174508 | String | 是 | 数据备份名称

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |  | Object |
├─taskId | 12818 | Long | 任务ID
├─padCode | AC22030010124 | String | 实例编号

**请求示例**

```json
{
  "padCodes": [
    "AC32010030001"
  ],
  "backupName": "test_AC32010100001_20241211174508"
}
```

**响应示例**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1717570615524,
  "data": [
    {
      "taskId": 23,
      "padCode": "AC22030010124"
    }
  ]
}
``` -->

#### **一键新机** <star />
>
> 注意：一键新机会清除系统所有数据。请谨慎使用！

一键新机功能，将当前实例数据全部清空,并重新设置安卓属性

- 虚拟机直接设置安卓属性,然后清空所有数据
- 云真机直接清空所有数据(等同于重置),并且添加SIM信息,如果有传入模板id,会替换adi模板.如果没有传入模板,并且replacementRealAdiFlag为true.会随机挑选一个模板Id
- 注意:如果不传国家信息,或者传入的国家信息服务找不到,会默认设置上新加坡的SIM信息
- 如果传入的国家不支持,会返回错误500,提示:目前不支持国家编码XX

**接口地址**

> /openapi/open/pad/replacePad

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值                                                 | 参数类型    | 是否必填 | 参数描述
--- |-----------------------------------------------------|---------|------| ---
padCodes | []                                                  | Array   | 是    | 实例id列表
countryCode | SG                                                  | String  | 否    | 国家编码(具体查看:<https://chahuo.com/country-code-lookup.html>)
realPhoneTemplateId | 65                                                  | Long    | 否    | 模板id(从/openapi/open/realPhone/template/list接口获取)
androidProp | {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"} | Object  | 否    | 参考 [安卓改机属性列表](./InstanceAndroidPropList.html)
replacementRealAdiFlag | false                                               | Boolean | 否    | 真机是否随机adi模板(false 不随机 true 随机)
certificate | 参考[手机根证书](./PhoneCertificate.html) | String  | 否    | 手机根证书

**响应参数**

参数名 | 示例值           | 参数类型    | 参数描述
--- |---------------|---------| ---
code | 200           | Integer | 状态码
msg | success       | String  | 响应消息
ts | 1721739857317 | Long    | 时间戳
data |               | Object  |
├─taskId | 12818         | Long    | 任务ID
├─padCode | AC22030010124 | String  | 实例编号
├─vmStatus | 2             | Intger  | 实例状态

**请求示例**

```json
{
 "padCodes": ["AC32010250031"],
 "countryCode": "SG",
 "realPhoneTemplateId": 210,
 "androidProp": {
  "persist.sys.cloud.battery.level": "67",
  "persist.sys.cloud.gps.lat": "1.3657",
  "persist.sys.cloud.gps.lon": "103.6464",
  "persist.sys.cloud.imsinum": "525050095718767"
 },
 "certificate" : "手机根证书"
}
```

**响应示例1**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1732270378320,
  "data": {
    "taskId": 8405,
    "padCode": "AC32010250031",
    "vmStatus": 2
  }
}
```
**响应示例2**
```json
{
   "code": 500,
   "msg": "目前不支持国家编码: xx",
   "ts": 1753521350163,
   "data": null
}
```

#### **查询一键新机支持国家列表**

**接口地址**

> /openapi/open/info/country

**请求方式**

> GET

**请求数据类型**

> application/json

**响应参数**

参数名 | 示例值           | 参数类型    | 参数描述
--- |---------------|---------| ---
code | 200           | Integer | 状态码
msg | success       | String  | 响应消息
ts | 1721739857317 | Long    | 时间戳
data |               | Object[]  |
├─code | AD         | String    | 国家编码
├─name | Andorra | String  | 国家名称(英文)

**响应示例**

```json
{
  "code": 200,
  "msg": "success",
  "ts": *************,
  "data": [{
    "code": "AD",
    "name": "Andorra"
  }]
}
```

#### **修改通讯录**

> fileUniqueId和info必填一个

**接口地址**

> /openapi/open/pad/updateContacts

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型   | 是否必填 | 参数描述
--- | -- |--------| --- | ---
padCodes | [] | Array  | 是 | 实例id列表
fileUniqueId | cfca25a2c62b00e065b417491b0cf07ffc | String | 否 | 通讯录文件ID
info |  | Object[] | 否 | 通讯录信息
├─firstName | tom        | String | 否 | 姓名
├─phone | 13111111111        | String | 否| 手机号
├─email | <<EMAIL>>        | String | 否| 邮箱

**响应参数**

参数名 | 示例值           | 参数类型    | 参数描述
--- |---------------|---------| ---
code | 200           | Integer | 状态码
msg | success       | String  | 响应消息
ts | 1721739857317 | Long    | 时间戳
data |               | Object  |
├─taskId | 12818         | Long    | 任务ID
├─padCode | AC22030010124 | String  | 实例编号

**请求示例**

```json
{
  "fileUniqueId": "cfca25a2c62b00e065b417491b0cf07ffc",
  "info": [{
    "firstName": "tom",
    "phone": "13111111111",
    "email": "<EMAIL>"
  }],
  "padCodes": [
    "AC32010180326"
  ]
}
```

**响应示例**

```json
{
  "code": 200,
  "msg": "success",
  "ts": *************,
  "data": {
    "taskId": 11,
    "padCode": "AC32010210001",
    "vmStatus": 0
  }
}
```

#### **实例设置代理**

**接口地址**

> /openapi/open/network/proxy/set

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值      | 参数类型 | 是否必填 | 参数描述 |
| -------- | ----------- | -------- |------| -------- |
| account  | 2222        | String   | 否    | 账号     |
| password | 2222        | String   | 否    | 密码     |
| ip       | *********** | String   | 否    | IP       |
| port     | 2222        | Integer  | 否    | 端口     |
| enable   | true        | Boolean  | 是    | 启用     |
| padCodes |             | Array    | 是    | 实例列表 |
| proxyType |   vpn          | String    | 否    | 支持参数：proxy、vpn |
| proxyName |   socks5          | String    | 否    | 支持参数：socks5、http-relay(包含http、https)|
| bypassPackageList       |  | Array   | 否    | 包名 设置该包名不走代理  |
| bypassIpList       |  | Array   | 否    | ip  设置该ip不走代理 |
| bypassDomainList       |  | Array   | 否    | 域名  设置该域名不走代理  |
| sUoT   | true        | Boolean  | 否    | 是否开启udp连接 默认为false    |

**响应参数**

| 参数名     | 示例值        | 参数类型 | 参数描述                         |
| ---------- | ------------- | -------- | -------------------------------- |
| code       | 200           | Integer  | 状态码                           |
| msg        | success       | String   | 响应消息                         |
| ts         | ************* | Long     | 时间戳                           |
| data       |               | Object   |                                  |
| ├─taskId   | 24            | Long     | 任务ID                           |
| ├─padCode  | AC22030010001 | String   | 房间号                           |
| ├─vmStatus | 实例状态      | Integer  | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
 "padCodes": [
  "AC32010140023"
 ],
 "account": "2222",
 "password": "2222",
 "ip": "***********",
 "port": 2222,
 "enable": true,
    "bypassPackageList":[],
    "bypassIpList":[],
    "bypassDomainList":[],
    "sUoT":true
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": [
  {
   "taskId": 24,
   "padCode": "AC32010140023",
   "vmStatus": 1
  }
 ]
}
```



#### **实时查询已安装的应用列表**

**接口地址**

> /openapi/open/pad/listInstalledApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值      | 参数类型 | 是否必填 | 参数描述                   |
| -------- | ----------- | -------- |------|------------------------|
| padCodes |  AC32010250001     | String[]    | 是    | 实例编号                   |
| appName |             | String    | 否    | 应用名称                |  

**响应参数**

| 参数名     | 示例值 | 参数类型 | 参数描述                    |
| ---------- |---|------|-------------------------|
| code       | 200 | Integer | 状态码                     |
| msg        | success | String | 响应消息                    |
| ts         | ************* | Long | 时间戳                     |
| data       |   | Object |                         |
| ├─padCode   | AC32010250001 | String | 实例编号 |
| ├─apps |   | Object[] | 应用集合
| ├─├─appName | 测试app | String |  应用名称
| ├─├─packageName | com.xxx.xxx | String |  包名
| ├─├─versionCode | 150600 | String |  版本号
| ├─├─versionName | 15.6.0 | String |  版本名称
| ├─├─appState | 0 | Integer |  0 已完成  1安装中   2下载中

**请求示例**

```javascript
{
 "padCodes": ["AC32010250001"],
 "appName": ""
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1740022125515,
 "data": [{
  "padCode": "AC32010250001",
  "apps": [{
   "appName": "测试app",
   "packageName": "com.xxx.xxx",
   "versionCode": "150600",
   "versionName": "15.6.0"
  }]
 }]
}
```

#### **设置保活应用**

目前只支持安卓14

**接口地址**

> openapi/open/pad/setKeepAliveApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值      | 参数类型     | 是否必填 | 参数描述   |
| -------- | ----------- |----------|------|--------|
| padCodes |  AC32010250001     | String[] | 否    | 实例编号   |
| applyAllInstances |  false     | Boolean  | 是    | 是否应用所有实例模式 |
| appInfos |             | Object[] | 否    |        |  
| ├─serverName | com.zixun.cmp/com.zixun.cmp.service.TaskService | String   | 是    | com.xxx.xxx（包名）/com.xxx.xxx.service.DomeService  (需要启动的服务完整路径) |

**响应参数**

| 参数名     | 示例值           | 参数类型   | 参数描述                    |
| ---------- |---------------|--------|-------------------------|
| code       | 200           | Integer | 状态码                     |
| msg        | success       | String | 响应消息                    |
| ts         | ************* | Long   | 时间戳                     |
| data       |               | null |                         |

**请求示例**

```javascript
{
 "padCodes": [
  "AC002",
  "AC001"
 ],
 "appInfos": [{
   "serverName": "com.zixun.cmp/com.zixun.cmp.service.TaskService"
  }
 ],
 "applyAllInstances": false
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1736326542985,
        "data": [{
        "taskId": 10074,
        "padCode": "AC32010250011",
        "errorMsg": null
    }]
}
```

#### **修改真机ADI模板**

修改实例云真机ADI模版, 传入云真机模版ID

必要条件：

1. 实例创建时，需要创建为云真机类型
2. 实例创建时的规格，需要和目标的ADI模版规格一致
3. 实例创建时的安卓版本，需要和目标的ADI安卓版本一致

**接口地址**

> /openapi/open/pad/replaceRealAdiTemplate

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名  | 示例值           | 参数类型     | 是否必填 | 参数描述    |
| ------- |---------------|----------|------|---------|
|padCodes |               | String[] | 是    |         |
|├─ | AC21020010001 | String   | 是    | 实例编号    |
|wipeData | false         | Boolean  | 是    | 是否清除数据  |
|realPhoneTemplateId | 186             | Long     | 是    | 云真机模板id |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | ---|
|code | 200 | Integer | 状态码|
|msg | success | String | 响应消息|
|ts | ************* | Long | 时间戳|
|data | | Object[] | |
|├─taskId | 1 | Integer | 任务ID|
|├─padCode | AC21020010001 | String | 实例编号|
|├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）|

**请求示例**

```json
{
 "padCodes": ["AC32010250011"],
 "wipeData": true,
 "realPhoneTemplateId": 186
}
```

**响应示例**

```json
{
 "code": 200,
 "msg": "success",
 "ts": 1736326542985,
 "data": [{
  "taskId": 10074,
  "padCode": "AC32010250011",
  "errorMsg": null
 }]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110028 | 实例不存在| 联系管理员
110064 | 当前实例中有不满足升级真机条件,请检查实例| 检查实例是否是真机
110099 | ADI模板不存在,请检查参数| 检查ADI模板信息

#### **异步执行ADB命令**

在一个或多个云手机实例中异步执行命令

**接口地址**

> /openapi/open/pad/asyncCmd

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述|
|--- | --- | --- | --- | ---|
|padCodes |  | String[] | 是 |
|├─ | AC22020020793 | String | 是 | 实例编号|
|scriptContent | cd /root;ls | String | 是 | ADB命令，多条命令使用分号隔开|

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String |  响应消息
ts | ************* | Long  | 时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22020020793 | String |  实例编号
├─vmStatus | 1 | Integer |  实例在线状态（0：离线；1：在线）

**请求示例**

```json
{
    "padCodes": [
        "AC22020020793"
    ],
    "scriptContent": "cd /root;ls"
}
```

**响应示例**

```json
{
 "code": 200,
 "msg": "success",
 "ts": 1717570297639,
 "data": [
  {
   "taskId": 14,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 15,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110003 | 执行ADB命令失败| 联系管理员
110012 | 命令执行超时 | 请稍后重试


#### **开关Root权限**

在一个或多个云手机实例中开关root权限。
开个单个应用root,需要指定包名,否则会抛出异常.(云真机产品，在调用此接口时，不建议设置全局root权限，会有被风控检测到的可能。)

**接口地址**

> /openapi/open/pad/switchRoot

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值           | 参数类型     | 是否必填 | 参数描述                     |
|--- |---------------|----------|------|--------------------------|
|padCodes |               | String[] | 是    |
|├─ | AC22020020793 | String   | 是    | 实例编号                     |
|globalRoot | false         | Boolean  | 否    | 是否开启全局root权限，默认不开启       |
|packageName | com.zixun.cmp        | String   | 否    | 应用包名(非全局root必传)多个包名通过,连接 |
|rootStatus | root开启状态         | Integer  | 是    | root状态，0：关闭 1：开启         |

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String |  响应消息
ts | ************* | Long  | 时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22020020793 | String |  实例编号
├─vmStatus | 1 | Integer |  实例在线状态（0：离线；1：在线）

**请求示例**

```json
{
  "padCodes": [
    "AC32010250002"
  ],
  "globalRoot": false,
  "packageName": "com.android.ftpeasys",
  "rootStatus": 0
}


```

**响应示例**

```json
{
 "code": 200,
 "msg": "success",
 "ts": 1717570297639,
 "data": [
  {
   "taskId": 14,
   "padCode": "AC32010250002",
   "vmStatus": 1
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110003 | 执行ADB命令失败| 联系管理员
110089 | 开启单个root包名不能为空 | 开启单个应用root时，包名不能为空

#### **本地截图**

实例截图。

**接口URL**

> openapi/open/pad/screenshot

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名    | 示例值   | 参数类型 | 是否必填 | 参数描述                                                                     |
| ---- | ---- | ---- | ---- |--------------------------------------------------------------------------|
|padCodes |  | String[] | 是 |                                                                          |
|├─ | AC21020010231 | String | 是 | 实例编号                                                                     |
| rotation  | 0 | Integer  | 是 | 截图画面横竖屏旋：0：截图方向不做处理，默认；1：截图画面旋转为竖屏时：a：手机竖屏的截图，不做处理。b：手机横屏的截图，截图顺时针旋转90度。 |
| broadcast | false    | Boolean  | 否 | 事件是否广播(默认false)                                                          |
| definition | false    | Integer  | 否 | 清晰度 取值范围0-100                                                            |
| resolutionHeight | false    | Integer  | 否 | 分辨率 - 高 大于1                                                              |
| resolutionWidth | false    | Integer  | 否 | 分辨率 - 宽 大于1                                                              |

**响应参数**

参数名 | 示例值 | 参数类型 |是否必填| 参数描述
--- | --- | --- | --- | ---
code | 200 | Integer | 是 | 状态码
msg | success | String | 是 | 响应消息
ts | ************* | Long | 是 | 时间戳
data |  | Object[] |  |
├─taskId | 1 | Integer | 否 | 任务ID
├─padCode | AC21020010231 | String | 否 | 实例编号
├─vmStatus | 1 | Integer | 是 | 实例在线状态（0：离线；1：在线）

**请求示例**

```json
{
 "padCodes": [
  "AC21020010231"
 ],
 "rotation": 0,
 "broadcast": false,
    "definition": 50,
    "resolutionHeight": 1920,
    "resolutionWidth": 1080
}
```

**响应示例**

```json
{
 "code": 200,
 "msg": "success",
 "ts": 1717570337023,
 "data": [
  {
   "taskId": 16,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 17,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110001 | 截图失败| 请重试
110004 | 执行重启命令失败| 稍后再次重启
110028 | 实例不存在| 请检查实例是否存在


#### **获取实例实时预览图片**

获取指定的实例，当前时间下的屏幕截图。 调用此接口，会给实例返回一个url和到期时间，在有效期内，通过访问这个url，可以获取实时的云机屏幕截图。
可以批量传多个实例编号，批量获取预览图url。

**接口地址**

> /openapi/open/pad/getLongGenerateUrl

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名    | 示例值           | 参数类型 | 是否必填 | 参数描述                                                     |
| --------- |---------------| -------- | -------- | ----- |
|padCodes |               | String[] | 是 | |
|├─ | AC11010000031 | String | 是 | 实例编号|
| format  | png           | String  | 否       | 图片格式，枚举值：png、jpg，默认png |
| height | 50 | String | 否 | 缩放高度（像素，不传则保持原始比例） |
| width | 50 | String | 否 | 缩放宽度（像素，不传则保持原始比例） |
| quality | 60 | String | 否 | 图片质量（百分比：0~100，不传则为50%） |

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─padCode | AC11010000031 | String | 实例编号
├─url | <http://xxx.armcloud.png> | String | 访问地址
├─expireAt | 1756024767163 | Long | URL到期时间（毫秒时间戳）
├─success | true| Boolean | 是否成功生成URL
├─reason | 实例状态异常 | String | 失败原因（成功时为空）

**请求示例**

```javascript
{
    "padCodes": [
        "AC11010000031",
        "AC11010000032"
    ], 
    "format": "png"
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": *************,
        "data": [
        {
            "padCode": "AC11010000031",
            "url": "http://xxx.armcloud.png",
            "expireAt": 1756024767163,
            "success": true,
            "reason": ""
        },
        {
            "padCode": "AC11010000032",
            "url": "",
            "expireAt": null,
            "success": false,
            "reason": "实例状态异常"
        }
    ]
}
```

#### **升级镜像**

批量实例镜像升级

**接口地址**

> /openapi/open/pad/upgradeImage

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22030010182 | String | 是 | 实例编号
imageId | mg-24061124017 | String | 是 | 镜像ID
wipeData | false | Boolean | 是 | 是否清除实例数据(data分区), true清除，false不清除

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─padCode | AC22030010182 | String | 实例编号
├─taskId | 1 | Integer | 任务ID
├─errorMsg | “” | String | 错误信息

**请求示例**

```javascript
{
    "padCodes": [
        "AC22030010182"
    ],
    "wipeData": false,
    "imageId": "mg-24061124017"
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1718594881432,
 "data": [
  {
   "taskId": 63,
   "padCode": "AC22030010182",
   "errorMsg": null
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110041 | 镜像不存在| 镜像id传参有误
110037 | 执行升级镜像指令失败| 实例状态有误，联系管理员
110038 | 执行升级镜像命令失败| 实例状态有误，联系管理员

#### **升级真机镜像**

批量实例真机镜像升级

**接口地址**

> /openapi/open/pad/virtualRealSwitch

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22030010182 | String | 是 | 实例编号
imageId | mg-24061124017 | String | 是 | 镜像ID
wipeData | false | Boolean | 是 | 是否清除实例数据(data分区), true清除，false不清除
realPhoneTemplateId | 178             | Integer | 否 | 真机模板ID upgradeImageConvertType=real时必填
upgradeImageConvertType | virtual | String | 是 | 转换镜像类型 virtual：虚拟机 real：云真机
screenLayoutId | 14 | Integer | 否 | 屏幕布局ID upgradeImageConvertType=virtual时必填

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─padCode | AC22030010182 | String | 实例编号
├─taskId | 1 | Integer | 任务ID
├─errorMsg | “” | String | 错误信息

**请求示例**

```javascript
{
    "padCodes": [
        "AC32010210023"
    ],
        "imageId": "img-24112653977",
        "wipeData": true,
        "realPhoneTemplateId": 178,
        "upgradeImageConvertType": "virtual",
        "screenLayoutId": 14
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1718594881432,
 "data": [
  {
   "taskId": 63,
   "padCode": "AC22030010182",
   "errorMsg": null
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110041 | 镜像不存在| 镜像id传参有误
110037 | 执行升级镜像指令失败| 实例状态有误，联系管理员
110038 | 执行升级镜像命令失败| 实例状态有误，联系管理员
110064 | 当前实例中有不满足升级真机条件,请检查实例| 当前实例中有不满足升级真机条件,请检查实例

#### **分页获取真机模板**

分页获取真机模板

**接口地址**

> /openapi/open/realPhone/template/list

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | -- | --- | --- | ---
pageIndex | 1 | Integer | 否 | 页码 默认1
pageSize | 10 | Integer | 否 | 每页显示数量 默认10 取值范围为1-100
androidImageVersion | 14 | String | 否 | 安卓镜像版本

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─id | 178 | Long | id
├─brand | google | String | 品牌
├─model | Pixel 7 Pro(12G) | String | 型号
├─fingerprintMd5 | f5da7b97678ac19309f0cf0203e072d7 | String | 安卓md5(ro.build.fingerprint)
├─fingerprint | google/cheetah/cheetah:13/TQ3A.230901.001/10750268:user/release-keys | String | 安卓ro.build.fingerprint
├─resourceSpecificationCode | m2-3 | String | 规格编号
├─screenLayoutCode | realdevice_1440x3120x560 | String | 屏幕布局编码
├─androidImageVersion | 13 | Integer | 安卓镜像版本
├─propertyJson | {\"test\": \"testa\"} | String | 实例属性

**请求示例**

```javascript
{
    "pageIndex": 1,
    "pageSize": 10
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1733748550410,
        "data": [
        {
            "id": 178,
            "brand": "google",
            "model": "Pixel 7 Pro(12G)",
            "fingerprintMd5": "f5da7b97678ac19309f0cf0203e072d7",
            "fingerprint": "google/cheetah/cheetah:13/TQ3A.230901.001/10750268:user/release-keys",
            "resourceSpecificationCode": "m2-3",
            "screenLayoutCode": "realdevice_1440x3120x560",
            "androidImageVersion": 13,
            "propertyJson": "{\"test\": \"testa\"}"
        }
    ]
}
```

#### **获取公共屏幕布局列表**

获取公共屏幕布局列表

**接口地址**

> /openapi/open/screenLayout/publicList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─id | 12 | Long | id
├─code | single-portrait-basic | String | 屏幕布局编码
├─screenWidth | 1920 | String | 屏幕宽度
├─screenHigh | 1080 | String | 屏幕高度
├─pixelDensity | 320 | String | 像素密度
├─screenRefreshRate | 60 | String | 屏幕刷新率

**请求示例**

```javascript

```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1733748912002,
        "data": [
        {
            "id": 12,
            "code": "single-portrait-basic",
            "screenWidth": "1080",
            "screenHigh": "1920",
            "pixelDensity": "320",
            "screenRefreshRate": "60"
        }
    ]
}

```

#### **批量获取实例机型信息**

根据实例编号批量获取对应的实例的机型信息。

**接口地址**

> /openapi/open/pad/modelInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22030010182 | String | 是 | 实例编号

**响应参数**

参数名 | 示例值 | 参数类型     | 参数描述
--- | -- |----------| ---
code | 200 | Integer  | 状态码
msg | success | String   | 响应消息
ts | ************* | Long     | 时间戳
data |  | Object[] |
├─padCode | AC22030010182 | String   | 实例编号
├─imei | 524803173613682 | String   | IMEI
├─serialno | 01NM5ON34M4O | String   | 序列号
├─wifimac | 04:3a:6c:e5:e9:8d:62:d6:4a | String   | Wi-Fi的mac地址
├─androidid | aa6bcedf1426546c | String   | Android实例唯一标识
├─model | Mi 10 Pro | String   | 型号
├─brand | Xiaomi | String   | 品牌
├─manufacturer | Xiaomi | String   | 厂商
├─isRoot | 1 | String   | 是否是ROOT权限
├─width | 720 | Integer  | 云手机的宽 最大不超过1080
├─height | 1280 | Integer  | 云手机的高 最大不超过1920
├─memoryLimit | 1024 | Integer  | 内存限额
├─bluetoothaddr | 3A:1F:4B:9C:2D:8E | String   | 蓝牙地址
├─phonenum | 1112341234 | String  | 手机号码
├─romVersion | android13 |  String | 安卓版本
├─dataSize | 2367381504 | Integer  | 内存大小(b)
├─dataSizeAvailable | 365830144 | Integer  | 剩余可用(b)
├─dataSizeUsed | 1024 | 2001551360  | 已使用(b)

**请求示例**

```javascript
{
    "padCodes": [
        "AC22030010182"
    ]
}
```

**响应示例**

```javascript
{
  "code": 200,
  "msg": "success",
  "ts": *************,
  "data": [
    {
      "padCode": "AC22030010182",
      "imei": "524803173613682",
      "serialno": "01NM5ON34M4O",
      "wifimac": "04:3a:6c:e5:e9:8d:62:d6:4a",
      "androidid": "aa6bcedf1426546c",
      "model": "Mi 10 Pro",
      "brand": "Xiaomi",
      "manufacturer": "Xiaomi",
      "isRoot": "1",
      "width": 720,
      "height": 1280,
      "memoryLimit": 1024
    }
  ]
}
```

#### **添加应用黑名单列表**

根据实例规格添加应用黑名单。

**接口地址**

> /openapi/open/appBlack/setUpBlackList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padGrade | q2-1 | String | 是 | 实例规格
blackApps |  | Object[] | 是 | 黑名单列表
├─appPkg | cn.v8box.app | String | 是 | 应用包名
├─appName | x8沙箱 | String | 是 | 应用名称

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721647657112 | Long | 时间戳
data | “” | String |

**请求示例**

```javascript
{
 "padGrade": "q2-1",
 "blackApps": [
  {
   "appPkg": "cn.v8box.app",
   "appName": "x8沙箱"
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1721647657112,
 "data": null
}
```

#### **设置实例黑名单**

根据实例规格设置实例黑名单。

**接口地址**

> /openapi/open/pad/triggeringBlacklist

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padGrade | q2-1 | String | 是 | 实例规格
padCodes |  | String[] | 否 |
├─ | AC22030010124 | String | 否 | 实例编号

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |  | Object[] |
├─taskId | 12818 | Integer | 任务ID
├─padCode | AC22030010124 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padGrade": "q2-4",
 "padCodes": [
  "AC22030010124"
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1721739857317,
 "data": [
  {
   "taskId": 12818,
   "padCode": "AC22030010124",
   "vmStatus": 1
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110051 | 该规格不存在应用黑名单配置| 需添加规格应用黑名单列表
110028 | 实例不存在| 传参有误
110052 | 执行设置应用黑名单指令失败| 请重试

#### **设置实例带宽**

根据实例编号设置实例带宽。

**接口地址**

> /openapi/open/pad/setSpeed

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22030010124 | String | 是 | 实例编号
upBandwidth | 10.00 | float | 是 | 上行带宽 Mbps (0：不限制；-1：限制上网)
downBandwidth | 10.00 | float | 是 | 下行带宽 Mbps (0：不限制；-1：限制上网)

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |  | Object[] |
├─taskId | 679 | Integer | 任务ID
├─padCode | AC32010140011 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padCodes": [
  "AC32010140011"
 ],
 "upBandwidth": 10.00,
 "downBandwidth": 10.00
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1721640654237,
 "data": [
  {
   "taskId": 679,
   "padCode": "AC32010140011",
   "vmStatus": 1
  }
 ]
}
```

#### **<span id="pad_openOnlineAdb">开启关闭ADB</span>**

根据实例编号打开或关闭实例adb

**接口地址**

> /openapi/open/pad/openOnlineAdb

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | -- | ---
padCodes |  | String[] | 是 | 实例列表(传入实例数量1-200个) |
├─ | AC32010250032 | String | 是 | 实例编号|
openStatus | 1 | Integer | 是 | 开启关闭ADB状态(1开启 0或者不传默认关闭)

**响应参数**

参数名 | 示例值           | 参数类型 | 参数描述
--- |---------------| --- | ---
code | 200           | Integer | 状态码
msg | success       | String | 响应消息
ts | 1721739857317 | Long | 时间戳
data |               | Object[] |
├─taskId | 16147 | Integer | 任务id
├─padCode | AC32010250032 | String | 实例编号
├─taskStatus | 3             | Integer | 任务状态（-1全失败，-2部分失败，-3取消，-4超时，-5异常，1，等待执行，2执行中，3完成）
├─taskResult | success       | String | 任务结果

**请求示例**

```javascript
{   
    "padCodes":[
        "AC32010250032"
    ],
    "status": 1
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1736920929306,
    "data": [
        {
        "taskId": 16147,
        "padCode": "AC32010250032",
        "taskStatus": 3,
        "taskResult": "success"
        }
    ]
}
```

#### **获取ADB连接信息**

根据实例编号获取adb连接信息
响应数据(key,adb)不全情况时，请调用[开启关闭ADB](#pad_openOnlineAdb)开启adb。

**接口地址**

> /openapi/open/pad/adb

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | -- | ---
padCode | AC32010250032 | String | 是 | 实例编号 |
enable | true | Boolean | 是 | ADB 状态 true:开启 false:关闭 |

**响应参数**

参数名 | 示例值           | 参数类型 | 参数描述
--- |---------------| --- | --
code | 200           | Integer | 状态码
msg | success       | String | 响应消息
ts | 1736922808949 | Long | 时间戳
data |               | Object[] |
├─padCode | AC32010250032 | String | 实例编号
├─command | ssh -oHostKeyAlgorithms=+ssh-rsa 10.255.3.2_001_1736922765389@************* -p 1824 -L 8572:adb-proxy:53728 -Nf | String | SSH 连接指令
├─expireTime | 2025-01-16 14:32:00 | String | adb 链接有效期
├─enable | true | Boolean  | ADB 状态 true:开启 false:关闭
├─key | 3CXr3FJZ6gbnGuJctDOpP9M6X6Rl786xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx== | String | 连接密钥
├─adb | adb connect localhost:8577            | String | adb连接信息

**请求示例**

```javascript
{   
    "padCode": "AC32010250032",
    "enable": true
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1736922808949,
    "data": {
        "padCode": "AC32010250032",
        "command": "ssh -oHostKeyAlgorithms=+ssh-rsa 10.255.3.2_001_1736922765389@************* -p 1824 -L 8572:adb-proxy:53728 -Nf",
        "expireTime": "2025-01-16 14:32:00",
        "enable": true,
        "key": "3CXr3FJZ6gbnGuJctDOpP9M6X6Rl786xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx==",
        "adb": "adb connect localhost:8577"
    }
}
```

#### **模拟触控**

通过传入xy坐标，模拟在实例中的触控事件（按下、抬起、触摸中）。

**接口地址**

> /openapi/open/pad/simulateTouch

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名        | 示例值                                                          | 参数类型     | 参数描述                                                  |
|-----------|--------------------------------------------------------------|----------|-------------------------------------------------------|
| padCodes  | 2200                                                         | String[] | 需要触发点击的实例编码                                           |
| width     | 120                                                          | Integer  | 容器宽度                                                  |
| height    | 120                                                          | Integer  | 容器高度                                                  |
| positions | [{"actionType":0,"x":100,"y":100,"nextPositionWaitTime":20}] | Object[] | 点击坐标组                                                 |
| ├─ actionType | 1                                                            | Integer  | 操作类型（0：按下；1：抬起；2：触摸中）                                 |
| ├─ x         | 100                                                          | float    | 点击的x坐标                                                |
| ├─ y         | 100                                                          | float    | 点击的y坐标                                                |
| ├─ nextPositionWaitTime          | 100                                                          | Integer  | 多组坐标时，触发下一组点击坐标的等待间隔时间ms毫秒值                           |
| ├─ swipe          | -1                                                           | float    | 滚动距离  -1 下划  1 上划                                     |
| ├─ touchType          | gestureSwipe                                                 | String   | gestureSwipe划动事件  gesture触控事件  keystroke按键事件(默认是按下抬起) |
| ├─ keyCode          | 1                                                            | Integer  | 用于标识用户按下或释放的具体按键                        |

**响应参数**

参数名 | 示例值     | 参数类型 | 参数描述
--- |---------| --- | --
code | 200     | Integer | 状态码
msg | success | String | 响应消息
ts | 1736922808949 | Long | 时间戳
data |         | Object[] |
├─padCode | AC32032 | String | 实例编号
├─taskId | 10004759 | Long |任务id
├─vmStatus | 0       | String | 实例在线状态

**请求示例**

```javascript
{
  "padCodes": [
    "实例编号"
  ],
  "width": 1080,
  "height": 1920,
  "positions": [
    {
      "actionType": 0,
      "x": 100,
      "y": 100,
      "nextPositionWaitTime": 20,
      "swipe":-1, 
      "touchType":"gestureSwipe",
      "keyCode":1
    },
    {
      "actionType": 2,
      "x": 110,
      "y": 110,
      "nextPositionWaitTime": 22
    },
    {
      "actionType": 2,
      "x": 120,
      "y": 120,
      "nextPositionWaitTime": 23
    },
    {
      "actionType": 1,
      "x": 120,
      "y": 120
    }
  ]
}
```

**响应示例**

```javascript
{
  "code": 200,
  "msg": "success",
  "ts": 1743676563784,
  "data": [
    {
      "taskId": 100059,
      "padCode": "ACP00001",
      "vmStatus": 0
    },
    {
      "taskId": 100060,
      "padCode": "ACP00001",
      "vmStatus": 0
    }
  ]
}
```

#### **实例操作任务详情**

查询指定实例操作任务的执行结果详细信息。

**接口地址**

> /task-center/open/task/padTaskDetail

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
taskIds |  | Integer[] | 是 |
├─taskId | 1 | Integer | 是 |任务ID

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object [] | 子任务列表详情
├─ taskId | 1 | Integer | 子任务ID
├─ padCode | VP22020020793 | String | 实例标识
├─ taskStatus | 2 | String TODO类型使用错误  | 任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）
├─ endTime | 1713429401000 | Long | 子任务结束时间戳
├─ taskContent | “” | String | 任务内容
├─ taskResult | “” | String | 任务结果
├─ errorMsg | “” | String | 错误信息

**请求示例**

```javascript
{
 "taskIds":[1,2]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1716283460673,
 "data": [
  {
   "taskId": 1,
   "padCode": "AC22030022441",
   "taskStatus": 2,
   "endTime": 1713429401000,
   "taskContent": null,
   "taskResult": null,
            "errorMsg":"Alarms  Android  Audiobooks  DCIM  Documents  Download  Movies  Music  Notifications  Pictures  Podcasts  Recordings  Ringtones  "
  },
  {
   "taskId": 2,
   "padCode": "AC22030022442",
   "taskStatus": 2,
   "endTime": 1713429401001,
   "taskContent": null,
   "taskResult": null,
            "errorMsg":"Alarms  Android  Audiobooks  DCIM  Documents  Download  Movies  Music  Notifications  Pictures  Podcasts  Recordings  Ringtones  "
  }
 ]
}
```

#### **获取实例执行脚本结果**

通过执行脚本任务ID来获取实例执行脚本结果。

**接口地址**

> /task-center/open/task/executeScriptInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
taskIds |  | Integer [] | 是 |
├─ | 1 | Integer | 否 | 任务ID

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22020020793 | String |  实例编号
├─taskStatus | 3| Integer |  任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）
├─endTime | 1756021166163 | Long |  任务执行结束时间
├─taskContent | Success | String |  任务内容
├─taskResult | Success | String |  任务结果

**请求示例**

```javascript
{
 "taskIds": [1]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":*************,
 "data":[
    {
    "taskId": 1,
    "padCode": "AC22020020793",
    "taskStatus":3,
    "taskResult":"Success",
    "endTime":"1756021166163",
    "taskContent":"Success"
    }
   ]
}
```

#### **获取实例截图结果**

通过截图任务 ID 来获取实例的截图结果。

**接口URL**

> /task-center/open/task/screenshotInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
taskIds |  | Integer [] | 是 |
├─ | 1 | Integer | 否 | 任务ID

**响应参数**

| 参数名  | 示例值              | 参数类型  | 参数描述                                                                 |
| ------- | ------------------- | --------- | ------------------------------------------------------------------------ |
| code    | 200                 | Integer   | 状态码                                                                  |
| msg     | success             | String    | 响应消息                                                                |
| ts      | *************       | Long      | 时间戳
| data    | -                   | Object[]  | 任务列表详情                                                                |
|├─ taskId | 1 | Integer | 任务ID |
|├─ taskStatus | 3 | Integer | 任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
|├─ padCode | AC22020020793 | String |  实例编号 |
|├─ taskContent | Success   | String |  任务内容 |
|├─ taskResult | Success   | String |  任务结果 |
|├─ endTime | 1756121167163 | String |  任务执行结束时间 |

**请求示例**

```json
{
 "taskIds": [1]
}
```

**响应示例**

```json
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data":[
    {
    "taskId": 1,
    "taskStatus": 3,
    "padCode": "AC22020020793",
    "taskContent": "Success",
    "taskResult": "Success",
    "endTime": 1756121167163
    }
   ]
}
```

#### **实例重启重置执行结果**

通过任务ID来获取实例重启重置执行结果。

**接口地址**

> /task-center/open/task/padExecuteTaskInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述|
|--- | --- | --- | --- | --- |
|taskIds |  | Integer [] | 是 | |
|├─ | 1 | Integer | 是 | 任务ID |

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | --- |
|code | 200 | Integer |  状态码 |
|msg | success | String |  响应消息 |
|ts | ************* | Long |  时间戳 |
|data |  | Object[] |  |
|├─taskId | 1 | Integer |  任务ID |
|├─padCode | AC21020010001 | String | 实例编号 |
|├─taskStatus | 3| Integer |  任务状态：（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
|├─ endTime | 1756021166163 | Long |  任务执行结束时间 |
|├─ taskContent | “” | String |  任务内容 |
|├─ taskResult | Success | String |  任务结果 |

**请求示例**

```javascript
{
    "taskIds": [1]
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": *************
    "data":[
        {
            "taskId": 1,
            "padCode": "AC22030022911",
            "taskStatus": 3,
            "endTime": 1756021166163,
            "taskContent": null,
            "taskResult": "Success"
        }
    ]
}
```

#### **实例列表信息**

根据查询条件分页获取实例列表信息。

**接口地址**

> /openapi/open/pad/infos

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值            | 参数类型      | 是否必填 | 参数描述
--- |----------------|-----------|------| ---
page | 1              | Integer   | 是    | 页码
rows | 10             | Integer   | 是    | 条数
armServerCode | ACS32010260000 | Integer   | 否    | 服务器编号
deviceCode | AC32010250020  | Integer   | 否    | 板卡编号
padType | real           | String    | 否    | 实例类型（virtual：虚拟机；real：真机）
idc | 1              | String    | 否    | 机房Id
padCodes |                | String[]  | 否    |
├─ | AC22010020062  | String    | 否    | 实例编号
groupIds |                | Integer[] | 否    |
├─ | 1              | Integer   | 否    | 实例分组ID

**响应参数**

| 参数名               | 示例值                            | 参数类型 | 参数描述                                            |
|-------------------|--------------------------------| --- |-------------------------------------------------|
| code              | 200                            | Integer |                                                 |
| msg               | success                        | String ||
| ts                | 1713773577581                  | Long ||
| data              |                                | Object ||
| ├─page            | 1                              | Integer | 当前页                                             |
| ├─rows            | 10                             | Integer | 每页的数量                                           |
| ├─size            | 1                              | Integer | 当前页的数量                                          |
| ├─total           | 1                              | Integer | 总记录数                                            |
| ├─totalPage       | 1                              | Integer | 总页数                                             |
| ├─pageData        |                                | object[] | 列表                                              |
| ├─├─padCode       | VP21020010391                  | String | 实例编号                                            |
| ├─├─padGrade      | q1-2                           | String | 实例开数（q1-6六开，q1-2二开）                             |
| ├─├─padStatus     | 10                             | String | 实例状态 （10-运行中 11-重启中 12-重置中 13-升级中 14-异常 15-未就绪） |
| ├─├─groupId       | 0                              | Integer | 分组ID                                            |
| ├─├─idcCode       | d3c1f580c41525e514330a85dfdecda8 | String | 机房编码                                            ||
| ├─├─idc           | 1                              | String | 机房id                                            ||
| ├─├─deviceIp      | ***********                    | String | 云机ip                                            |
| ├─├─padIp         | ***********                    | String | 实例ip                                            |
| ├─├─armServerCode | ACS32010260000                 | String | 服务器编号                                           |
| ├─├─deviceCode    | AC32010250020                  | String | 实例编号                                            |
| ├─├─armServerCode | ACS32010160000                 | String | 服务器编号                                           |
| ├─├─padType | real                           | String | 实例类型（virtual：虚拟机；real：真机）                                            |
| ├─├─deviceCode    | AC32010150070                  | String | 板卡编号                                            |
| ├─├─apps          |                                | String[] | 安装的应用列表                                         |
| ├─├─├─            | armcloud001                    | String | 安装的应用                                           |

**请求示例**

```javascript
{
    "page": 1,
        "rows": 10,
        "padCodes": [
        "AC21020010391"
    ],
        "armServerCode": "ACS32010260000",
        "deviceCode": "AC32010250020",
        "groupIds": [
        1
    ]
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1737625119669,
        "data": {
        "page": 1,
            "rows": 10,
            "size": 3,
            "total": 3,
            "totalPage": 1,
            "pageData": [
            {
                "padCode": "AC32010250021",
                "padGrade": "m2-3",
                "padStatus": 12,
                "groupId": 3,
                "idcCode": "1",
                "deviceIp": "**********",
                "padIp": "**********",
                "padType": "real",
                "adbOpenStatus": "0",
                "armServerCode": "ACS32010260000",
                "deviceCode": "AC32010250020",
                "apps": null,
                "imageId": "img-25012330968"
            },
            {
                "padCode": "AC32010250022",
                "padGrade": "m2-3",
                "padStatus": 10,
                "groupId": 3,
                "idcCode": "1",
                "deviceIp": "**********",
                "padIp": "**********",
                "padType": "real",
                "adbOpenStatus": "1",
                "armServerCode": "ACS32010260000",
                "deviceCode": "AC32010250020",
                "apps": null,
                "imageId": "img-25012330968"
            },
            {
                "padCode": "AC32010250023",
                "padGrade": "m2-3",
                "padStatus": 10,
                "groupId": 3,
                "idcCode": "1",
                "deviceIp": "**********",
                "padIp": "**********",
                "padType": "real",
                "adbOpenStatus": "0",
                "armServerCode": "ACS32010260000",
                "deviceCode": "AC32010250020",
                "apps": null,
                "imageId": "img-25012330968"
            }
        ]
    }
}
```

#### **实例分组列表**

获取用户当前所有实例分组列表信息（包括：分组 ID,分组名称，分组下实例数量）。
**接口地址**

> /openapi/open/group/infos

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCode | AC21020010391 | String | 否 | 实例编号
groupIds |  | Integer[] | 否 |
├─ | 1 | Integer | 否 | 分组ID

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─groupId | 1 | Integer | 分组ID
├─groupName | 分组一 | String | 分组名称
├─padCount | 1 | Integer | 分组下的实例数

**请求示例**

```javascript
{
 "padCode": "AC21020010391",
 "groupIds": [1]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": [
   {
    "groupId": 1,
    "groupName": "分组一",
    "padCount": 1
   }
 ]
}
```
#### **导入通话记录**

此接口允许将通话记录数据导入至云手机中。接口在导入过程中，会自动检测云手机通讯录中已保存的联系人，并将这些联系人对应的名称显示在通话记录中，便于用户快速识别联系人。

**接口地址**

> /openapi/open/pad/addPhoneRecord

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名           | 示例值                                                                                       | 参数类型     | 是否必填 | 参数描述                 |
|---------------|-------------------------------------------------------------------------------------------|----------|------|----------------------|
| padCodes      | ACP2505060777                                                                             | String[] | 是    | 需要编辑通话记录的实例编码        |
| callRecords   | [{"number":"18009781201","inputType":1,"duration":30,"timeString":"2025-05-06 14:00:09"}] | Object[] | 是    | 通话记录                 |
| ├─ number     | 13900000000                                                                               | String   | 是    | 电话号码                 |
| ├─ inputType  | 1                                                                                         | int      | 是    | 通话类型（1：拨出；2：接听；3：未接） |
| ├─ duration   | 60                                                                                        | int      | 是    | 通话时长;单位是秒,未接电话是0秒    |
| ├─ timeString | 2025-05-08 12:30:00                                                                       | String   | 否    | 通话时长                 |

**响应参数**

参数名 | 示例值     | 参数类型 | 参数描述
--- |---------| --- | --
code | 200     | Integer | 状态码
msg | success | String | 响应消息
ts | 1736922808949 | Long | 时间戳
data |         | Object[] |
├─padCode | AC32032 | String | 实例编号
├─taskId | 10004759 | Long |任务id
├─vmStatus | 0       | String | 实例在线状态

**请求示例**

```javascript
{
  "padCodes": [
     "实例编号"
  ],
  "callRecords": [
    {
      "number": "18009781201",
      "inputType": 1,
      "duration": 30,
      "timeString": "2025-05-06 14:00:09"
    },
    {
      "number": "18009781202",
      "inputType": 2,
      "duration": 60,
      "timeString": "2025-05-07 14:00:09"
    },
    {
      "number": "18009781203",
      "inputType": 3,
      "duration": 0
    }
  ]
}
```

**响应示例**

```javascript
{
  "code": 200,
  "msg": "success",
  "ts": 1743676563784,
  "data": [
    {
      "taskId": 100059,
      "padCode": "ACP00001",
      "vmStatus": 0
    },
    {
      "taskId": 100060,
      "padCode": "ACP00001",
      "vmStatus": 0
    }
  ]
}
```

#### **云机文本信息输入**

在云机中预先聚焦好输入框，调用该接口传入指定的文本信息内容后，文本展示在云机指定位置。

**接口地址**

> /openapi/open/pad/inputText

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名           | 示例值           | 参数类型     | 是否必填 | 参数描述          |
|---------------|---------------|----------|------|---------------|
| padCodes      | ACP2505060777 | String[] | 是    | 实例编码 |
| text   | hello123      | String | 是    | 输入文本          |
**响应参数**

参数名 | 示例值           | 参数类型 | 参数描述
--- |---------------| --- | --
code | 200           | Integer | 状态码
msg | success       | String | 响应消息
ts | 1736922808949 | Long | 时间戳
data |               | Object[] |
├─padCode | AC32032       | String | 实例编号
├─taskId | 10004759      | Long |任务id
├─vmStatus | 0             | String | 实例在线状态
├─taskStatus | 1             | String | 任务当前状态

**请求示例**

```javascript
{
   "padCodes": [
      "ACP250509FECQN33",
      "ACP250509T1VME44",
      "ACP25050917AYX11"
   ], 
   "text": "12345678"
}
```

**响应示例**

```javascript
{
     "msg": "success",
     "code": 200,
     "data": [ 
      {
         "padCode": "ACP250509FECQN33",
         "vmStatus": 0,
         "taskId": 10013014,
         "taskStatus": 1
      },
      {
         "padCode": "ACP250509T1VME44",
         "vmStatus": 0,
         "taskId": 10013015,
         "taskStatus": 1
      },
      {
         "padCode": "ACP25050917AYX11",
         "vmStatus": 0,
         "taskId": 10013016,
         "taskStatus": 1
      }
   ],
   "ts": 1746797852244
}
```

#### **重置GAID**

调用该接口传入指定实例编号或者实例分组，将重置云机中的 advertising ID（Reset advertising ID）

**接口地址**

> /openapi/open/pad/resetGAID

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名   | 示例值           | 参数类型      | 是否必填 | 参数描述             |
| -------- |---------------|-----------|------|------------------|
| padCodes |               | String[]  | 是    |                  |
| ├─       | AC21020010001 | String    | 是    | 实例编号             |
| groupIds |               | Integer[] | 否    |                  |
| ├─       | 1             | Integer   | 否    | 实例组ID            |
| resetGmsType | GAID          | String    | 是    | 重置gms类型, 可选：GAID |
| oprBy | zhangsan      | String    | 否    | 操作人              |
| taskSource | OPEN_PLATFORM      | String    | 是    | 任务来源，可选：OPEN_PLATFORM         |


**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述|
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String | 响应消息 |
|ts | ************* | Long | 时间戳 |
|data |  | Object[] | |
|├─taskId | 1 | Integer |  任务ID|
|├─padCode | AC21020010001 | String | 实例编号 |
|├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线） |

**请求示例**

```javascript
{
   "padCodes": [
      "ACPXXXXXXXXXXXXXXX"
   ],
  "taskSource": "OPEN_PLATFORM",
  "oprBy": "admin",
  "resetGmsType": "GAID"
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1717559681604,
    "data": [
         {
            "taskId": 88,
            "padCode": "AC22030010001",
            "vmStatus": 1
         },
         {
            "taskId": 89,
            "padCode": "AC22030010002",
            "vmStatus": 0
         }
      ]
}
```


#### **注入音频到实例麦克风**

将一个音频文件注入到实例的麦克风，代替麦克风收音的接口
备注：目前仅支持注入pcm格式的音频文件，请处理转好格式后再进行上传

**接口地址**

> /openapi/open/pad/injectAudioToMic

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名           | 示例值                              | 参数类型     | 是否必填 | 参数描述                    |
|---------------|----------------------------------|----------|------|-------------------------|
| padCodes      | ACP2505060777                    | String[] | 是    | 实例编码                    |
| url   | http://localhost/abc             | String   | 否    | 音频文件下载地址 （此字段和fileUniqueId 2选1传值）|
| fileUniqueId  | 8fc73d05371740008ea27a2707496a82 | String   | 否    | 文件id唯一标识（此字段和url 2选1传值） |
| enable        | true                             | Boolean        | 是    | 注入开关                    |
**响应参数**

参数名 | 示例值           | 参数类型 | 参数描述
--- |---------------| --- | --
code | 200           | Integer | 状态码
msg | success       | String | 响应消息
ts | 1736922808949 | Long | 时间戳
data |               | Object[] |
├─padCode | AC32032       | String | 实例编号
├─taskId | 10004759      | Long |任务id
├─vmStatus | 0             | String | 实例在线状态
├─taskStatus | 1             | String | 任务当前状态

**请求示例**

```javascript
{
  "padCodes": [
    "ACP250509FECQN33","ACP250509T1VME44","ACP25050917AYX11
  ],
  "url":"http://localhost/abc ",
  "fileUniqueId":"8a6d0df189ef4b0e83858fd9eeb7620c",
  "enable":true
}
```

**响应示例**

```javascript
{
     "msg": "success",
     "code": 200,
     "data": [ 
      {
         "padCode": "ACP250509FECQN33",
         "vmStatus": 0,
         "taskId": 10013014,
         "taskStatus": 1
      },
      {
         "padCode": "ACP250509T1VME44",
         "vmStatus": 0,
         "taskId": 10013015,
         "taskStatus": 1
      },
      {
         "padCode": "ACP25050917AYX11",
         "vmStatus": 0,
         "taskId": 10013016,
         "taskStatus": 1
      }
   ],
   "ts": 1746797852244
}
```


### 回调管理

配置说明:需要客户在客户开放平台配置回调地址，配置地址成功则默认开启接收回调信息

#### **查询支持的回调类型**

**接口地址**

> /openapi/open/config/selectList

**请求方式**

> GET

**请求数据类型**

> 无参数

**响应参数**

|参数名 | 示例值           | 参数类型    | 参数描述                   |
|--- |---------------|---------|------------------------|
|code | 200           | Integer |                        |
|msg | success       | String  ||
|ts | 1713773577581 | Long    ||
|data |               | Object  ||
|├─callbackName | "文件上传任务"        | String  | 回调类型名称                 |
|├─callbackType | "4"          | String  | 回调类型                   |
|├─id | 14         | Long    | 回调类型的ID(后续新增修改需要传入该参数) |

**请求示例**

```javascript
/openapi/open/config/selectList
```

**响应示例**

```javascript
{
    "code": 200,
        "data": [
        {
            "callbackName": "文件上传任务",
            "callbackType": "4",
            "id": 14
        },
        {
            "callbackName": "文件上传实例任务",
            "callbackType": "5",
            "id": 15
        },
        {
            "callbackName": "应用同步任务",
            "callbackType": "6",
            "id": 16
        }
    ],
    "msg": "success",
    "ts": 1734500966127
}
```

#### **查询配置的回调地址**

**接口地址**

> /openapi/open/config/selectCallback

**请求方式**

> GET

**请求数据类型**

> 无参数

**响应参数**

|参数名 | 示例值           | 参数类型    | 参数描述     |
|--- |---------------|---------|----------|
|code | 200           | Integer |          |
|msg | success       | String  ||
|ts | 1713773577581 | Long    ||
|data |        <http://www.baidu.com>       | String  | 配置的回调地址 |

**请求示例**

```javascript
/openapi/open/config/selectCallback
```

**响应示例**

```javascript
{
 "code" : 200,
 "data" : "http://www.baidu.com",
 "msg" : "success",
 "ts" : 1734501602763
}
```

#### **新增回调地址配置**

**接口地址**

> /openapi/open/config/insertCallback

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值              | 参数类型 | 是否必填 | 参数描述
--- |------------------| --- | --- | ---
callbackIdList | [1,2,3]          | Integer[] | 是 |回调类型ID集合(从查询支持的回调类型接口获取)
├─callbackUrl | ***********/test | String | 是 |接收任务回调配置URL

**响应参数**

|参数名 | 示例值           | 参数类型    | 参数描述       |
|--- |---------------|---------|------------|
|code | 200           | Integer |            |
|msg | success       | String  ||
|ts | 1713773577581 | Long    ||
|data | 2             | String  | 配置成功type数量 |

**请求示例**

```javascript
{"callbackIdList":[21,14],"callbackUrl":"localhost:8080/callback"}

```

**响应示例**

```javascript
{"code":200,"data":2,"msg":"success","ts":1734502541732}
```

#### **修改回调地址配置**

**接口地址**

> /openapi/open/config/updateCallback

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值              | 参数类型 | 是否必填 | 参数描述
--- |------------------| --- | --- | ---
callbackIdList | [1,2,3]          | Integer[] | 是 |回调类型ID集合(从查询支持的回调类型接口获取)
├─callbackUrl | ***********/test | String | 是 |接收任务回调配置URL

**响应参数**

|参数名 | 示例值           | 参数类型    | 参数描述     |
|--- |---------------|---------|----------|
|code | 200           | Integer |          |
|msg | success       | String  ||
|ts | 1713773577581 | Long    ||
|data | 2             | String  |  |

**请求示例**

```javascript
{
 "callbackIdList" : [21,14],
 "callbackUrl" : "localhost:8080/callback"
}
```

**响应示例**

```javascript
{
 "code" : 200,
 "data" : 2,
 "msg" : "success",
 "ts" : 1734502541732
}
```

#### **删除配置的回调地址**

**接口地址**

> /openapi/open/config/deleteCallback

**请求方式**

> POST

**请求数据类型**

> 无参数

**响应参数**

|参数名 | 示例值           | 参数类型    | 参数描述     |
|--- |---------------|---------|----------|
|code | 200           | Integer |          |
|msg | success       | String  ||
|ts | 1713773577581 | Long    ||
|data | 4             | Long    | |

**请求示例**

```javascript
/openapi/open/config/deleteCallback
```

**响应示例**

```javascript
{
 "code" : 200,
 "data" : 22,
 "msg" : "success",
 "ts" : 1734503029282
}
```

#### **实例状态回调**

**使用场景**

实例状态变更的情况会通过该回调接口通知给客户。

| 字段        | 类型    | 示例     | 说明                                                    |
|-----------| ------- |--------|-------------------------------------------------------|
| taskBusinessType | Integer | 999    | 任务业务类型                                                |
| padCode   | String  | 212254 | 实例标识                                                  |
| padStatus | Integer | 1      | 实例状态： 10-运行中 11-重启中 12-重置中 13-升级中 14-异常 15-未就绪,-1 已删除 |
| padConnectStatus | Integer | 1 | 实例连接状态，1：在线，0：离线 |

#### **云机状态回调**

**使用场景**

云机状态变更的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer | 1000 | 任务业务类型|
|deviceStatus | Integer | 1 |物理机状态 0-离线；1-在线|
| deviceCode| String  | 212254      | 云机编号   |

#### **实例重启任务回调**

**使用场景**

实例重启任务的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer | 1000          |  任务业务类型|
| padCode| String  | 212254        | 实例编号   |
| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| taskId | Integer  | 1             | 任务ID      |
| taskContent | String  |               | 任务内容   |
| endTime | Long| 1756021166163 | 结束时间 |
| taskResult| String  | Success       | 任务结果   |

#### **实例重置任务回调**

**使用场景**

实例重置任务的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer | 1001          |  任务业务类型|
| padCode| String  | 212254        | 实例编号   |
| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| taskId | Integer  | 1             | 任务ID      |
| taskContent | String  |               | 任务内容   |
| endTime | Long| 1756021166163 | 结束时间 |
| taskResult| String  | Success       | 任务结果   |

#### **异步执行ADB任务回调**

**使用场景**

客户调用异步执行ADB命令，会通过该回调接口通知给客户。

| 字段               | 类型    | 示例            | 说明                                                |
|------------------| ------- |---------------|---------------------------------------------------|
| taskBusinessType | Integer| 1002          | 任务业务类型                                            |
| padCode          | String  | AC22030022001 | 实例标识                                              |
| taskId           | Integer| 1             | 任务id                                              |
| taskStatus       | Integer| 3             | 任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| endTime          | Long| 1756021166163 | 任务执行结束时间                                          |
| taskResult       | String  | Success       | 任务结果                                              |
| taskContent      | String  |               | 任务内容                                              |
| cmd              | String  | cd /root;ls   | 执行的命令                                             |
| cmdResult        | String  | /ws           | 执行的命令返回                                           |

#### **应用安装任务回调**

**使用场景**

客户调用应用安装，应用的安装情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例             | 说明       |
| ------- | ------- |----------------| ---------- |
| taskBusinessType | Integer| 1003           | 任务业务类型                                            |
| taskId | Integer | 1              | 任务ID |
| apps   | Object[] |                | 应用信息   |
| ├─ appId | Integer | 10001          |应用ID|
| ├─ appName | String  | demo           | 应用名称   |
| ├─ pkgName | String | com.xxx.demo   | 包名 |
| ├─ padCode | String | AC22030022001  | 实例编号 |
| ├─ result | boolean | true           | 安装结果的标识。true：成功，false：失败 |
| ├─ failMsg | String | 此应用已加入黑名单，禁止安装 | 失败信息 |

#### **应用卸载任务回调**

**使用场景**

客户调用应用卸载，应用卸载的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer| 1004          | 任务业务类型                                            |
| taskId | Integer | 1             | 任务ID |
| apps   | Object |               | 应用信息   |
| ├─ appId | Integer | 10001         |应用ID|
| ├─ appName | String  | demo          | 应用名称   |
| ├─ pkgName | String | com.xxx.demo  | 包名 |
| ├─ padCode | String | AC22030022001 | 实例编号 |
| ├─ result | boolean | true          | 安装结果的标识。true：成功，false：失败 |

#### **应用停止任务回调**

**使用场景**

客户调用应用停止，应用停止的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer| 1005           | 任务业务类型                                            |
| padCode | String  | AC22030022001 | 实例标识   |
| taskId  | Integer | 1             | 任务ID     |
| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| packageName | String | xxx.test.com  | 包名 |

#### **应用启动任务回调**

**使用场景**

客户调用应用启动，应用启动的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer| 1007          | 任务业务类型                                            |
| padCode | String  | AC22030022001      | 实例标识   |
| taskId  | Integer | 1           | 任务ID     |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| packageName | String | xxx.test.com | 包名 |

#### **文件上传实例任务回调**

**使用场景**

客户调用实例文件上传api，会通过该回调接口通知客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer| 1009           | 任务业务类型                                            |
| padCode | String  | AC22030022001      | 实例编号   |
| taskId   | Integer | 1           | 任务ID   |
| result  | boolean  |   true     | 执行结果：true-成功，false-失败   |
| errorCode  | String  |        |  错误码 |
| fileId | String   |  cf08f7b685ab3a7b6a793b30de1b33ae34         | 文件id |

#### **查询实例应用列表回调**

**使用场景**

客户调用实例已安装应用列表，实例已安装应用列表的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer| 1011            | 任务业务类型                                            |
| padCode | String | AC22030022001 |pod标识|
| taskId   | Integer | 1           | 任务ID   |
| taskStatus | Integer | 1 |任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| apps | Object[] |  | |
| ├─appName | String  | test   | 应用名称 |
| ├─pkgName | String | text.xx.com | 包名 |

#### **实例升级镜像任务回调**

**使用场景**

实例升级镜像任务状态变更的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer | 1012          |  任务业务类型|
| padCode| String  | 212254        | 实例编号   |
| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| taskId | Integer  | 1             | 任务ID      |
| taskContent | String  |               | 任务内容   |
| endTime | Long| 1756021166163 | 结束时间 |
| taskResult| String  | Success       | 任务结果   |

#### **实例黑名单任务回调**

**使用场景**

实例设置应用黑名单任务状态变更的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer | 1015          |  任务业务类型|
| padCode| String  | 212254      | 实例编号   |
| taskId | Integer  | 1         | 任务ID     |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|

[//]: # (#### **备份实例回调**)

[//]: #
[//]: # (**使用场景**)

[//]: #
[//]: # (实例备份实例的情况会通过该回调接口通知给客户。)

[//]: #
[//]: # (| 字段    | 类型    | 示例            | 说明       |)

[//]: # (| ------- | ------- |---------------| ---------- |)

[//]: # (| taskBusinessType | Integer | 1024          |  任务业务类型|)

[//]: # (| padCode| String  | 212254        | 实例编号   |)

[//]: # (| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|)

[//]: # (| taskId | Integer  | 1             | 任务ID      |)

[//]: # (| taskContent | String  |               | 任务内容   |)

[//]: # (| endTime | Long| 1756021166163 | 结束时间 |)

[//]: # (| taskResult| String  | Success       | 任务结果   |)

[//]: #
[//]: # (#### **还原备份数据回调**)

[//]: #
[//]: # (**使用场景**)

[//]: #
[//]: # (还原备份数据的情况会通过该回调接口通知给客户。)

[//]: #
[//]: # (| 字段    | 类型    | 示例            | 说明       |)

[//]: # (| ------- | ------- |---------------| ---------- |)

[//]: # (| taskBusinessType | Integer | 1025          |  任务业务类型|)

[//]: # (| padCode| String  | 212254        | 实例编号   |)

[//]: # (| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|)

[//]: # (| taskId | Integer  | 1             | 任务ID      |)

[//]: # (| taskContent | String  |               | 任务内容   |)

[//]: # (| endTime | Long| 1756021166163 | 结束时间 |)

[//]: # (| taskResult| String  | Success       | 任务结果   |)

#### **一键新机回调**

**使用场景**

一键新机的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- |---------------| ---------- |
| taskBusinessType | Integer | 1124            |  任务业务类型|
| padCode| String  | 212254        | 实例编号   |
| taskStatus | Integer | 3             | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| taskId | Integer  | 1             | 任务ID      |
| taskContent | String  |               | 任务内容   |
| endTime | Long| 1756021166163 | 结束时间 |
| taskResult| String  | Success       | 任务结果   |

#### **网存实例开机回调**

**使用场景**  
网存实例开机的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- | --------------- | ---------- |
| taskBusinessType | Integer | 1201 | 任务业务类型 |
| padCode | String  | ACN250427352B7WU | 实例编号   |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| taskId | Integer | 10001661 | 任务ID      |
| taskContent | String | （空字符串） | 任务内容 |
| endTime | Long | null | 结束时间（时间戳，单位毫秒） |
| taskResult | String | （空字符串） | 任务结果 |

**示例内容**

```json
{
  "endTime": null,
  "padCode": "ACN250427352B7WU",
  "taskBusinessType": 1201,
  "taskContent": "",
  "taskId": 10001661,
  "taskResult": "",
  "taskStatus": 3
}
```

---

#### **网存实例关机回调**

**使用场景**  
网存实例关机的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- | --------------- | ---------- |
| taskBusinessType | Integer | 1202 | 任务业务类型 |
| padCode | String  | ACN250427352B7WU | 实例编号   |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| taskId | Integer | 10001662 | 任务ID      |
| taskContent | String | （空字符串） | 任务内容 |
| endTime | Long | null | 结束时间（时间戳，单位毫秒） |
| taskResult | String | （空字符串） | 任务结果 |

**示例内容**

```json
{
  "endTime": null,
  "padCode": "ACN250427352B7WU",
  "taskBusinessType": 1202,
  "taskContent": "",
  "taskId": 10001662,
  "taskResult": "",
  "taskStatus": 3
}
```

---

#### **网存实例删除回调**

**使用场景**  
网存实例删除的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- | --------------- | ---------- |
| taskBusinessType | Integer | 1203 | 任务业务类型 |
| padCode | String  | ACN250427352B7WU | 实例编号   |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| taskId | Integer | 10001662 | 任务ID      |
| taskContent | String | （空字符串） | 任务内容 |
| endTime | Long | null | 结束时间（时间戳，单位毫秒，null 表示尚未结束） |
| taskResult | String | （空字符串） | 任务结果 |

**示例内容**

```json
{
  "endTime": null,
  "padCode": "ACN250427352B7WU",
  "taskBusinessType": 1203,
  "taskContent": "",
  "taskId": 10001672,
  "taskResult": "",
  "taskStatus": 3
}
```

---

---

#### **网存存储备份回调**

**使用场景**  
网存存储备份的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- | --------------- | ---------- |
| taskBusinessType | Integer | 1204 | 任务业务类型 |
| padCode | String  | ACN250427352B7WU | 实例编号   |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| taskId | Integer | 10001664 | 任务ID      |
| taskContent | String | （空字符串） | 任务内容 |
| endTime | Long | null | 结束时间（时间戳，单位毫秒，null 表示尚未结束） |
| taskResult | String | （空字符串） | 任务结果 |

**示例内容**

```json
{
  "endTime": null,
  "padCode": "ACN250427352B7WU",
  "taskBusinessType": 1204,
  "taskContent": "",
  "taskId": 10001664,
  "taskResult": "",
  "taskStatus": 3
}
```

---

---

#### **网存存储删除回调**

**使用场景**  
网存存储删除的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例            | 说明       |
| ------- | ------- | --------------- | ---------- |
| taskBusinessType | Integer | 1205 | 任务业务类型 |
| padCode | String  | ACN250427352B7WU | 实例编号   |
| taskStatus | Integer | 3 | 任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
| taskId | Integer | 10001665 | 任务ID      |
| taskContent | String | （空字符串） | 任务内容 |
| endTime | Long | null | 结束时间（时间戳，单位毫秒，null 表示尚未结束） |
| taskResult | String | （空字符串） | 任务结果 |

**示例内容**

```json
{
  "endTime": null,
  "padCode": "ACN250427352B7WU",
  "taskBusinessType": 1205,
  "taskContent": "",
  "taskId": 10001665,
  "taskResult": "",
  "taskStatus": 3
}
```

---

#### **文件上传任务回调**

**使用场景**

客户调用上传文件，上传文件的情况会通过该回调接口通知给客户。

| 字段    | 类型    | 示例        | 说明       |
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer | 2000          |  任务业务类型|
| taskId   | Integer | 1           | 任务ID   |
| taskStatus | Integer | 3 |任务状态（-1：全失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）|
| originFileUrl | String | <www.xxx.com/xxx.apk> |原文件下载地址|
| fileUniqueId | String | test001      | 文件id |


### 文件管理

#### **文件上传到云盘**

进行文件上传操作，上传到云盘

**接口地址**查询实例状态

> /file-center/open/file/cache

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
fileUrl | <http://xxx.armcloud.apk> | String | 是 | 文件下载地址
fileName | 桃源深处有人家游戏官方版.apk | String | 是 | 文件名称
fileMd5 | 32e1f345f209a7dc1cc704913ea436d3 | String | 是 | ⽂件预期md5，⽤作下载⽂件校验最⼤⻓度32

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─taskId | 1 | Integer | 任务ID
├─fileUniqueId | 6865b417b7257d782afd5ac8bee4d311 | String | 文件唯一标识

**请求示例**

```javascript

{
    "fileUrl": "http://down.s.qq.com/download/11120898722/apk/10043132_com.tencent.fiftyone.yc.apk",
    "fileName": "桃源深处有人家游戏官方版.apk",
    "fileMd5": "c52585e13a67e13128d9963b2f20f69678a86ee8b5551ca593327d329719a5"
}

```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1713773577581,
 "data": {
  "taskId":1,
  "fileUniqueId": "6865b417b7257d782afd5ac8bee4d311"
 }
}
```

#### **实例文件删除**

实例文件删除

**接口地址**

> /file-center/open/file/batch/del

**请求方式**

> POST (支持批量删除，一次最多只能删除200个)

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
fileUniqueIds | cfc280111c435823c5409ddb9a4186420d | String[] | 是 | unique_id→文件id标识

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data | true | Boolean |

**请求示例**

```javascript

{
 "fileUniqueIds": [
  "cfc280111c435823c5409ddb9a4186420d",
  "cf77f930acbbe1707fffc661f2c4380a71"
 ]
}

```

**响应示例(全部成功)**

```javascript
{
  "code": 200,
  "msg": "success",
  "ts": 1734677342300, 
  "data": true
}

```

**响应示例(部分成功)**

```javascript
{
    "code": 200,
        "msg": "删除成功,存在无效文件id标识",
        "ts": 1734935677246,
        "data": [
        "cfc280111c435823c5409ddb9a4186420d"
    ]
}

```

#### **文件上传实例V3版本**

从文件管理中心推送文件到一个或多个云手机实例（异步任务）

**接口地址**

> /openapi/open/pad/v3/uploadFile

- 如果能通过md5值或者文件ID找到对应的文件,会直接通过OSS的路径下发给实例下载
- 如果OSS没有对应的文件,会直接将URL下发给实例下载,并且将该URL内容上传到OSS
- 如果选择安装应用,会检验包名是否有值,如果没值,会抛出异常.(安装应用默认会授权所有权限,通过isAuthorization字段可以选择不授权)
  **请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值                                                                             | 参数类型     | 是否必填 | 参数描述
--- |---------------------------------------------------------------------------------|----------|------| ---
padCodes |                                                                                 | String[] | 是    |
├─ | AC22030023061                                                                   | String   | 是    | 实例编号
autoInstall | 1                                                                               | Integer  | 否    | 是否需要⾃动安装 1需要、0不需要。不填默认不需要。仅对apk类型的⽂件⽣效
fileUniqueId | 1e5d3bf00576ee8f3d094908c0456722                                                | String   | 否    | 文件id唯一标识。
customizeFilePath | /Documents/                                                                     | String   | 否    | ⾃定义路径。非必传，需以/开头。
fileName | threads                                                                         | String   | 否    | 文件名称
packageName | com.zhiliaoapp.musically                                                        | String   | 否    | 文件包名
url | <https://file.vmoscloud.com/appMarket/2/apk/fe1f75df23e6fe3fd3b31c0f7f60c0af.apk> | String   | 否    | 文件安装路径
md5 | com.zhiliaoapp.musically                                                        | String   | 否    | 文件包名(使用url上传,选择需要安装时必填,否则会抛出异常)
isAuthorization | false                                                                           | Boolean  | 否    | 是否授权(默认全授权)
iconPath | <https://file.vmoscloud.com/appMarket/2/apk/fe1f75df23e6fe3fd3b31c0f7f60c0af.png> | string   | 否    | 安装时的图标展示

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─padCode | AC22010020062 | String | 实例编号
├─taskId | 1 | Integer | 任务ID
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
    "padCodes":["AC32010250022"],
        "customizeFilePath":"/DCIM/",
        "md5":"d97fb05b3a07d8werw2341f10212sdfs3sdfs24",
        "url":"https://file.vmoscloud.com/appMarket/2/apk/fe1f75df23e6fe3fd3b31c0f7f60c0af.apk ",
        "autoInstall":1,
        "packageName":"com.zhiliaoapp.musically",
        "fileName":"market",
        "isAuthorization":false
}


```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1737431505379,
        "data": [
        {
            "taskId": 13469,
            "padCode": "AC32010250022",
            "vmStatus": 0
        }
    ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- |---
140006 | 文件存储路径不正确| ⾃定义路径需以/开头
140005 | 文件不可用| 文件路径为空
110006 | 下载失败| 文件路径不能下载

#### **文件上传到实例**

从文件管理中心推送文件到一个或多个云手机实例（异步任务）

**接口地址**

> /openapi/open/pad/v2/uploadFile

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值                              | 参数类型     | 是否必填 | 参数描述
--- |----------------------------------|----------| --- | ---
padCodes |                                  | String[] | 是 |
├─ | AC22030023061                    | String   | 是 | 实例编号
autoInstall | 0                                | Integer  | 否 | 是否需要⾃动安装 1需要、0不需要。仅对apk类型的⽂件⽣效。不需要请不要填或填0
fileUniqueId | 1e5d3bf00576ee8f3d094908c0456722 | String   | 是 | 文件id唯一标识。
customizeFilePath | /Documents/                      | String   | 否 | ⾃定义路径。非必传，需以/开头。
isAuthorization | false                            | Boolean      | 否 | 是否授权应用(默认权授权)

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─padCode | AC22010020062 | String | 实例编号
├─taskId | 1 | Integer | 任务ID
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padCodes": [
  "AC22030023061"
 ],
 "autoInstall": 0,
 "fileUniqueId": "1e5d3bf00576ee8f3d094908c0456722",
 "customizeFilePath": "/Documents/"
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717571059834,
 "data": [
  {
   "taskId": 39,
   "padCode": "AC22030010001",
   "vmStatus": 1
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- |---
140006 | 文件存储路径不正确| ⾃定义路径需以/开头
140005 | 文件不可用| 文件路径为空
110006 | 下载失败| 文件路径不能下载

#### **文件列表**

查询已上传的文件列表信息

**接口地址**

> /file-center/open/file/list

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值     | 参数类型    | 是否必填 | 参数描述
--- |---------|---------|------| ---
page | 1       | Integer | 是    | 起始页,默认1
rows | 10      | Integer | 是    | 查询数量,默认10
fileName | "33"   | String  | 否    | 根据文件名查询

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object |
├─page | 1 | Integer | 当前页
├─rows | 10 | Integer | 每页的数量
├─size | 2 | Integer | 当前页的数量
├─total | 2 | Integer | 总记录数
├─totalPage | 1 | Integer | 总页数
├─pageData |  | object[] | 列表
├─├─fileUniqueId | c417cdf30cd13437a60a494f2fcee616 | String | 文件id标识
├─├─fileName | 15b18072b01049dfa30da046aaf5b213.apk | String | 文件名
├─├─fileMd5 | 49f526ec07f261ef6c22081fd61fb6b2836b84214ab6f4620e89d2f2d454253 | String | 文件内容值
├─├─fileSize | 165936779 | Integer | 文件大小（单位：字节）
├─├─originUrl | <https://xxx.armcloud.apk> | String | 文件原地址
├─├─createTime | 1713884498000 | Long | 创建时间

**请求示例**

```javascript

{
    "page": 1,
    "rows": 10,
    "fileName":"33"
}

```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1738810095624,
        "data": {
        "page": 1,
            "rows": 10,
            "size": 3,
            "total": 3,
            "totalPage": 1,
            "pageData": [
            {
                "fileUniqueId": "cf8761fb5f040e03dffde5a44238f2a97a",
                "fileName": "222333.txt",
                "fileId": "25123",
                "fileMd5": "f983eca4f19984b5d1733966b06a9827",
                "fileSize": 29,
                "originUrl": "",
                "createTime": 1738808289000
            },
            {
                "fileUniqueId": "cfa9252a7a8ed0e3045afe68a60760ff12",
                "fileName": "file_3388fb2de8977bce634e402ed7d52249.zip",
                "fileId": "25081",
                "fileMd5": "39661085ca57602ffd57d49a013a0eb5",
                "fileSize": 1310,
                "originUrl": "",
                "createTime": 1737109818000
            },
            {
                "fileUniqueId": "cf558127ed68c2f3a0a025f4b93131bf2e",
                "fileName": "b02b09f2f0833c092ead1dbf6831d452B02B09F2F0833C092EAD1DBF6831D452.apk",
                "fileId": "64",
                "fileMd5": "b02b09f2f0833c092ead1dbf6831d452",
                "fileSize": 1419011078,
                "originUrl": "",
                "createTime": 1726816761000
            }
        ]
    }
}
```

#### **文件任务详情**

查询指定文件任务的执行结果详细信息。

**接口地址**

> /task-center/open/task/fileTaskDetail

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
taskIds |  | Integer[] | 是 |
├─taskId | 1 | Integer | 是 |任务ID

**响应参数**

|参数名 | 示例值 | 参数类型 | 参数描述 |
|--- | --- | --- | --- |
|code | 200 | Integer | 状态码 |
|msg | success | String | 响应消息 |
|ts | ************* | Long | 时间戳 |
|data |  | Object[] | 任务列表详情 |
|├─ taskId | 1 | Integer | 子任务ID |
|├─ appId | 134 | Long | 应用id |
|├─ fileUniqueId | e2c07491309858c5cade4bfc44c03724 | String | ⽂件唯⼀标识 |
|├─ fileName | xx.apk | String | 文件名称 |
|├─ taskStatus | 2 | Integer | 任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成） |
|├─ endTime | 1713429401000 | Long | 子任务结束时间戳 |

**请求示例**

```javascript
{
 "taskIds":[
  1,2
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1716283460673,
 "data": [
  {
   "taskId": 1,
   "appId": 134,
   "fileUniqueId": "e2c07491309858c5cade4bfc44c03724",
   "fileName": "xx.apk",
   "taskStatus": 2,
   "endTime": 1713429401000
  },
  {
   "taskId": 2,
   "appId": 135,
   "fileUniqueId": "e2c07491309858c5cade4bfc43c03725",
   "fileName": "xx.apk",
   "taskStatus": 2,
   "endTime": 1713429401001
  }
 ]
}
```

### 应用管理

#### **应用上传**

上传应用安装文件到指定业务的应用管理中心（异步任务）。

**接口地址**

> /file-center/open/app/cache

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
parse | true | Boolean | 是 | 是否缓存并解析（解析并缓存） 如解析则无需填包信息
apps |  | Object[]  | 是 | 应用列表
├─ appId | 1243 | Integer | 否 | 自定义应用ID
├─ url | <https://xxx.armcloud.apk> | String | 是 | 源文件下载地址
├─ appName | kuaishou | String | 否 | 应用名称
├─ pkgName | com.smile.gifmaker | String | 否 | 包名
├─ signMd5 | 0F938C4F0995A83C9BF31F0C64322589 | String | 否 | 应用签名MD5
├─ versionNo | 36000 | Integer | 否 | 版本号
├─ versionName | 12.3.20.36000 | String | 否 | 版本名
├─ description | kuai | String | 否 | 描述
├─ md5sum | e673a204b8f18a0f6482da9998 | String | 否 | 应用唯一标识

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─ taskId | 12 | Integer |  任务ID
├─ appId | 1243 | Integer |  应用ID

**请求示例**

```javascript
{
 "parse": true,
 "apps": [
  { 
   "appId": 1243,
   "appName": "kuaishou",
   "url": "https://xxx.armcloud.apk",
   "pkgName": "com.smile.gifmaker",
   "signMd5": "0F938C4F0995A83C9BF31F0C64322589",
   "versionNo": 36000,
   "versionName": "12.3.20.36000",
   "description": "kuai",
   "md5sum": "e673a204b8f18a0f6482da9998"
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": [
   {
    "taskId": 12,
    "appId": 1243
   }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
100001 | 没有访问权限| 是否没有订购机房

#### **应用列表**

可根据应用ID查询应用信息列表。

**接口地址**

> /file-center/open/app/list

**请求方式**

> POST

**请求数据类型**
> application/json

**请求Query参数**

参数名 | 示例值                      | 参数类型      | 是否必填 | 参数描述
--- |--------------------------|-----------| --- | ---
page | 1                        | Integer   | 是 | 起始页,默认1
rows | 10                       | Integer   | 是 | 查询数量,默认10
appIds | 1,2                      | Integer[] | 否 | 应用id
packageName | com.ss.android.ugc.aweme | String    | 否 | 应用包名

**响应参数**

参数名 | 示例值 | 参数类型     | 参数描述
--- | -- |----------| ---
code | 200 | Integer  | 状态码
msg | success | String   | 响应消息
ts | ************* | Long     | 时间戳
packageName | com.ss.android.ugc.aweme | String     | 包名
pageData |  | Object   |
├─page | 1 | Integer  | 当前页
├─rows | 10 | Integer  | 每页显示数量
├─size | 10 | Integer  | 当前页的数量
├─total | 2 | Integer  | 总数量
├─totalPage | 1 | Integer  | 总页数
├─pageData |  | Object[] |
├─├─originUrl | downloadUrl_tr0bi | String   | 文件原下载地址
├─├─appId | dwadawdf | Integer  | 自定义应用ID
├─├─description | description_1cq3m | String   | 描述
├─├─packageName | packageName_e6lw8 | String   | 包名
├─├─appName | appName_o4mhn | String   | 应用名称
├─├─versionName | versionName_s4o2i | String   | 版本名
├─├─versionNo | 1 | Integer  | 版本号
├─├─signMd5 | 0F938C4F0995A83C9BF31F0C64322589 | String   | MD5
├─├─available | true | Boolean  | 当前文件是否可用
├─├─createTime | 1709895492000 | Integer  | 创建时间
├─├─fileId | 文件id | Integer  | 文件id
├─├─iconPath | <http://**************:18100/icon/110_6ca4d3e6e5111ec38e8eca6c1998ce89..png> | String   | 应用图标
├─├─classifyNameList | 黑白名单类别 | Object[] | 黑白名单类别
├─├─├─classifyName | 类别1 | String | 黑白名单类别名称
├─├─├─classifyType | 1 | Integer | 黑白名单类型(1白名单 2黑名单)
├─├─newAppClassifyList | 应用分类 | Object[] | 应用分类
├─├─├─classifyName | test | String | 应用分类名称
├─├─├─classifyId | 1 | Long | 应用分类id
├─├─├─description | 这个应用质量很高 | String | 应用描述 备注 
├─├─├─sortNum | 1 | Integer | 排序-输入框-仅支持整数输入，最大限制10000，列表按照数值从小到大排序展示 

**请求示例**

```javascript

{
    "rows": 10,
    "page": 1,
    "appIds": [
        1,2
    ],
    "packageName":"com.ss.android.ugc.aweme"
}

```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": {
  "page": 1,
  "rows": 10,
  "size": 10,
  "total": 30,
  "totalPage": 3,
  "pageData": [
   {
    "originUrl": "downloadUrl_tr0bi",
    "appId": 1,
    "description": "description_1cq3m",
    "packageName": "packageName_e6lw8",
    "appName": "appName_o4mhn",
    "versionName": "versionName_s4o2i",
    "versionNo": 1,
    "signMd5": "0F938C4F0995A83C9BF31F0C64322589",
    "available": true,
    "createTime": 1709895492000,
                "iconPath": "http://**************:18100/icon/110_6ca4d3e6e5111ec38e8eca6c1998ce89..png",
                "fileId": 1,
                "newAppClassifyList": [
                    {
                        "classifyName": "f1",
                        "classifyId": 3
                    }
                ]
   },
   {
    "originUrl": "downloadUrl_xh9ig",
    "appId": 2,
    "description": "description_9j663",
    "packageName": "packageName_o7t9j",
    "appName": "appName_szxpe",
    "versionName": "versionName_hwio7",
    "versionNo": 1,
    "signMd5": "0F938C4F0995A83C9BF31F0C64322587",
    "available": true,
    "createTime": 1710479133000,
                "fileId": 1,
                "iconPath": "http://**************:18100/icon/110_6ca4d3e6e5111ec38e8eca6c1998ce89..png",
                "newAppClassifyList": [
                    {
                        "classifyName": "f1",
                        "classifyId": 3
                    }
                ]
   }
  ]
 }
}
```

#### **应用详情**

查询指定实例上的应用安装情况

**接口地址**

> /file-center/open/app/detail

**请求方式**

> POST

**请求数据类型**
> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
appId | 1 | Integer | 是 | 应用id

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object |
├─appId | 1 | Integer | 应用id
├─originUrl | <http://www.xx.com/test.apk> | String | 原文件下载地址
├─description | test | String | 自定义描述
├─packageName | xxx.xxx.com | String | 包名
├─appName | test | String | 应用名
├─versionName | 1.0.1 | String | 版本名
├─versionNo | 124511 | Integer | 版本号
├─signMd5 | 0F938C4F0995A83C9BF31F0C64322589 | String | 签名md5
├─createTime | 1711595044000 | Integer | 文件创建时间

**请求示例**

```javascript
{
 "appId":1
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data": {
  "appId": 1,
  "originUrl": "http://www.xx.com/test.apk",
  "customizeFileId": "customizeId_v5o26",
  "description": "test",
  "packageName": "xxx.xxx.com",
  "appName": "test",
  "versionName": "1.0.1",
  "versionNo": 124511,
  "signMd5": "0F938C4F0995A83C9BF31F0C64322589",
  "createTime": 1711595044000
 }
}
```

#### **查询实例应用列表**

查询已上传的文件列表信息

**接口地址**

> /openapi/open/pad/listApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 否 |
├─ | AC22010020062 | String | 是 | 实例编号

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long | 时间戳
data |  | Object[] |
├─taskId | 1 | Integer | 任务ID
├─padCode | AC22010020062 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
    "padCodes": [
        "AC22010020062"
    ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570838398,
 "data": [
  {
   "taskId": 31,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 32,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
100010 | 处理失败| 请重新安装

#### **应用安装**

为单台或多台实例同时安装单个或多个APP。此接口为异步操作。
增加黑白名单逻辑。

**接口地址**

> /openapi/open/pad/installApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- |------| ---
apps |  | Object[] | 是    | 应用列表
├─appId | 124 | Integer | 是    | 应用ID
├─appName | 葫芦侠 | String | 否    | 应用名称
├─pkgName | com.huluxia.gametools | String | 否    | 应用包名
├─padCodes |  | String[] | 是    |
├─├─ | AC22010020062 | String | 是    | 实例编号

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String | 响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22010020062 | String |实例编号
├─vmStatus | 1 | Integer |实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "apps":[
  {
   "appId":124,
   "appName":"葫芦侠",
   "pkgName":"com.huluxia.gametools",
   "padCodes":["AC22010020062"]
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570991004,
 "data": [
  {
   "taskId": 37,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 38,
   "padCode": "AC22030010002",
   "vmStatus": 1
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
140005 | 文件不可用 | 查看文件是否存在

#### **应用卸载**

为单台或多台实例同时卸载单个或多个APP。此接口为异步操作。

**接口地址**

> /openapi/open/pad/uninstallApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- |------| ---
apps |  | Object[] | 是    | 应用列表
├─appId | 124 | Integer | 否    | 应用ID
├─appName | 葫芦侠 | String | 否    | 应用名称
├─pkgName | com.huluxia.gametools | String | 是    | 应用包名
├─padCodes |  | String[] | 是    |
├─├─ | AC22010020062 | String | 是    | 实例编号

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | | Object[] |
├─taskId | 2 | Integer |  任务ID
├─padCode | AC22010020062 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "apps":[
  {
   "appId":124,
   "appName":"demo",
   "pkgName":"com.demo",
   "padCodes":["AC22010020062"]
  }
 ]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570615524,
 "data": [
  {
   "taskId": 22,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 23,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110007 | 卸载应用失败| 稍后请重试

#### **应用启动**

根据实例编号和应用包名对实例进行应用启动的操作。

**接口地址**

> /openapi/open/pad/startApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

| 参数名      | 示例值       | 参数类型 | 是否必填 | 参数描述 |
| ----------- | ------------ | -------- | -------- | -------- |
|padCodes |  | String[] | 是 | |
|├─ | AC22010020062 | String | 是 | 实例编号|
| pkgName | xxx.test.com | String   | 是       | 包名     |

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─taskId | 1 | Integer | 任务ID
├─padCode | AC22020020793 | String |  实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padCodes": [
  "AC22030022693"
 ],
 "pkgName": xxx.test.com
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": *************,
 "data": [
  {
   "taskId": 24,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 25,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110008 | 启动应用失败 | 重启云机后再启动应用

#### **应用停止**

根据实例编号和应用包名对实例进行应用停止的操作。

**接口地址**

> /openapi/open/pad/stopApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22010020062 | String | 是 | 实例编号
pkgName | xxx.test.com | String   | 是       | 包名

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer | 状态码
msg | success | String | 响应消息
ts | ************* | Long |时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22010020062 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padCodes": [
  "AC22010020062"
 ],
 "pkgName": xxx.test.com
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570700415,
 "data": [
  {
   "taskId": 26,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 27,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110010 | 停止应用失败 | 重启云机关闭应用

#### **应用重启**

根据实例编号和应用包名对实例进行应用重启的操作。

**接口地址**

> /openapi/open/pad/restartApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
padCodes |  | String[] | 是 |
├─ | AC22010020062 | String | 是 | 实例编号
pkgName | xxx.test.com | String   | 是       | 包名

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─taskId | 1 | Integer |  任务ID
├─padCode | AC22010020062 | String | 实例编号
├─vmStatus | 1 | Integer | 实例在线状态（0：离线；1：在线）

**请求示例**

```javascript
{
 "padCodes": [
  "AC22030022693"
 ],
 "pkgName": xxx.test.com
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts": 1717570855874,
 "data": [
  {
   "taskId": 33,
   "padCode": "AC22030010001",
   "vmStatus": 1
  },
  {
   "taskId": 34,
   "padCode": "AC22030010002",
   "vmStatus": 0
  }
 ]
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110009 | 重启应用失败 | 重启云机后再启动应用

#### **黑白名单列表**

分页查询黑白名单列表

**接口地址**

> /openapi/open/appClassify/pageList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
classifyName | test | String | 否 | 黑白名单名称(模糊查询)
classifyType | 1 | Integer | 否 | 黑白名单类型 1白名单 2黑名单
page | 1 | Integer | 否 | 页码 默认1
rows | 10 | Integer | 否 | 每页数量 默认10 最大值100

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─id | 8 | Integer |  黑白名单id
├─classifyName | 白名单A组1 | String | 黑白名单名称
├─classifyType | 1 | Integer | 黑白名单类型 1白名单 2黑名单
├─appNum | 3 | Integer | 已关联的应用数量
├─remark | 白名单A组测试1 | String | 描述
├─applyAllInstances | false | Boolean | 是否应用所有实例模式

**请求示例**

```javascript
{
    "classifyName": "",
    "classifyType": 1,
    "page": 1,
    "rows": 10
}
```

**响应示例**

```javascript
{
     "code": 200,
     "msg": "success",
     "ts": 1735019791977,
     "data": [
          {
               "id": 8,
               "classifyName": "白名单A组1",
               "classifyType": 1,
               "appNum": 3,
               "remark": "白名单A组测试1",
               "applyAllInstances": false
          }
     ]
}
```

#### **黑白名单保存**

保存黑白名单和关联应用，参数id不为null，则表示更新，此操作会覆盖该分类下所有的关联应用

**接口地址**

> /openapi/open/appClassify/save

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | test | Integer | 否 | 黑白名单id(不为空则为修改)
classifyName | 1 | String | 是 | 黑白名单名称
classifyType | 1 | Integer | 是 | 黑白名单类型 1白名单 2黑名单
applyAllInstances | false | Boolean | 否 | 是否应用所有实例 默认false
remark | 10 | String | 否 | 黑白名单描述
appInfos |  | Object[] | 否 | 关联应用集合 范围0-500个
├─fileId | 1 | Integer | 否 | 文件id
├─appId | 1 | Integer | 否 |  应用id
├─appName | 测试app | String | 否 |  应用名称
├─packageName | com.xxx.xxx | String | 是 |  包名
├─appVersionNo | 123 | Integer | 否 |  版本号
├─appVersionName | 1.2.3 | String | 否 |  版本名称

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | 1 | Long | 黑白名单id

**请求示例**

```javascript
{
 "id": null,
 "classifyName": "白名单A组1",
 "classifyType": 1,
    "applyAllInstances": false,    
 "remark": "白名单A组测试1",
 "appInfos": [
  {
   "fileId": 1,
   "appId": 1,
   "appName": "测试app",
   "packageName": "com.xxx.xxx",
   "appVersionNo": 123,
   "appVersionName": "1.2.3"
  }
 ]
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735019933306,
    "data": 1
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在
110085 | 黑白名单名称重复 | 黑白名单名称重复

#### **黑白名单详情**

查询黑白名单详情和关联应用

**接口地址**

> /openapi/open/appClassify/detail

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | 1 | Integer | 是 | 黑白名单id

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─id | 8 | Integer |  黑白名单id
├─classifyName | 白名单A组1 | String | 黑白名单名称
├─classifyType | 1 | Integer | 黑白名单类型 1白名单 2黑名单
├─applyAllInstances | false | Boolean | 是否应用所有实例模式
├─appNum | 3 | Integer | 已关联的应用数量
├─remark | 白名单A组测试1 | String | 描述
├─appInfos |  | Object[] | 关联应用集合
├─├─fileId | 1 | Integer | 文件id
├─├─appId | 1 | Integer |  应用id
├─├─appName | 测试app | String |  应用名称
├─├─packageName | com.xxx.xxx | String |  包名
├─├─appVersionNo | 123 | Integer |  版本号
├─├─appVersionName | 1.2.3 | String |  版本名称

**请求示例**

```javascript
/openapi/open/appClassify/detail?id=1
```

**响应示例**

```javascript
{
     "code": 200,
     "msg": "success",
     "ts": 1735020014142,
     "data": {
          "id": 8,
          "classifyName": "白名单A组1",
          "classifyType": 1,
          "applyAllInstances": false,  
          "appNum": 3,
          "remark": "白名单A组测试1",
          "appInfos": [
               {
                    "fileId": 1,
                    "appId": 1,
                    "appName": "测试app",
                    "packageName": "com.xxx.xxx",
                    "appVersionNo": 123,
                    "appVersionName": "1.2.3"
               }
          ]
     }
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **黑白名单实例关联保存**

保存关联实例，此操作会删除该分类下原所有的关联实例

**接口地址**

> /openapi/open/appClassify/padSave

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | test | Integer | 是 | 黑白名单id
appPadInfos |  | Object[] | 否 | 关联实例集合 范围0-500个
├─padCode | AC32010230001 | String | 是 | 实例编号
├─deviceLevel | m2-4 | String | 否 |  规格
├─padIp | ********** | String | 否 |  实例ip

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | null |  |

**请求示例**

```javascript
{
 "id": 1,
 "appPadInfos": [
  {
   "padCode": "AC32010230001",
   "deviceLevel": "m2-4",
   "padIp": "**********"
  }
 ]
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735019933306,
    "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **黑白名单实例关联详情**

查询黑白名单详情和关联实例

**接口地址**

> /openapi/open/appClassify/padDetail

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | 1 | Integer | 是 | 黑白名单id

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─id | 8 | Integer |  黑白名单id
├─classifyName | 白名单A组1 | String | 黑白名单名称
├─classifyType | 1 | Integer | 黑白名单类型 1白名单 2黑名单
├─appNum | 3 | Integer | 已关联的应用数量
├─appPadInfos |  | Object[] | 关联实例集合
├─├─padCode | AC111 | String | 实例编号
├─├─deviceLevel | m2-3 | String |  规格
├─├─padIp | 127.0.0.1 | String |  实例ip

**请求示例**

```javascript
/openapi/open/appClassify/padDetail?id=1
```

**响应示例**

```javascript
{
     "code": 200,
     "msg": "success",
     "ts": 1735020251631,
     "data": {
          "id": 8,
          "classifyName": "白名单A组1",
          "classifyType": 1,
          "appNum": 3,
          "appPadInfos": [
               {
                    "padCode": "AC111",
                    "deviceLevel": "m2-3",
                    "padIp": "127.0.0.1"
               }
          ]
     }
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **删除黑白名单**

删除黑白名单 所关联的应用和实例都会清除

**接口地址**

> /openapi/open/appClassify/del

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | 1 | Integer | 是 | 黑白名单id

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | null |  |

**请求示例**

```javascript
/openapi/open/appClassify/del?id=1
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735020371601,
    "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110084 | 该黑白名单不存在或已删除 | 该黑白名单不存在或已删除

#### **添加黑白名单App**

添加关联应用，此操作不会覆盖该分类下所有的关联应用，直接添加到分类下

**接口地址**

> /openapi/open/appClassify/addApp

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | -- | ---
id | test | Integer | 是 | 黑白名单id
appInfos |  | Object[] | 是 | 关联应用集合 范围1-500个
├─fileId | 1 | Integer | 否 | 文件id
├─appId | 1 | Integer | 否 |  应用id
├─appName | 测试app | String | 否 |  应用名称
├─packageName | com.xxx.xxx | String | 是 |  包名
├─appVersionNo | 123 | Integer | 否 |  版本号
├─appVersionName | 1.2.3 | String | 否 |  版本名称

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | null |  |

**请求示例**

```javascript
{
 "id": 1,
 "appInfos": [
  {
   "fileId": 1,
   "appId": 1,
   "appName": "测试app",
   "packageName": "com.xxx.xxx",
   "appVersionNo": 123,
   "appVersionName": "1.2.3"
  }
 ]
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735019933306,
    "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **添加黑白名单实例关联**

添加关联实例，此操作不会覆盖该分类下所有的关联实例，直接添加到分类下

**接口地址**

> /openapi/open/appClassify/addPad

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- |----| ---
id | test | Integer | 是  | 黑白名单id
appPadInfos |  | Object[] | 是  | 关联实例集合 范围1-500个
├─padCode | AC32010230001 | String | 是  | 实例编号
├─deviceLevel | m2-4 | String | 否  |  规格
├─padIp | ********** | String | 否   |  实例ip

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | null |  |

**请求示例**

```javascript
{
 "id": 1,
 "appPadInfos": [
  {
   "padCode": "AC32010230001",
   "deviceLevel": "m2-4",
   "padIp": "**********"
  }
 ]
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735019933306,
    "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **删除黑白名单关联的实例**

删除黑白名单关联的实例,即指定实例解绑黑白名单

**接口地址**

> /openapi/open/appClassify/delPad

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- |----| ---
id | test | Integer | 是  | 黑白名单id
padCodes |  | String[] | 是  | 关联实例集合 范围1-200个

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data | null |  |

**请求示例**

```javascript
{
 "id": 100,
 "padCodes": ["AC32010250003"]
}
```

**响应示例**

```javascript
{
    "code": 200,
    "msg": "success",
    "ts": 1735019933306,
    "data": null
}
```

**错误码**

错误码 | 错误说明|操作建议
--- | --- | ---
110080 | 黑白名单不存在 | 黑白名单不存在

#### **应用分类列表**

分页查询应用分类

**接口地址**

> /openapi/open/newAppClassify/pageList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值        | 参数类型    | 是否必填 | 参数描述
--- | --- |---------| --- | ---
enable | true       | Boolean | 否 | 是否启用
page | 1       | Integer | 否 |  页码 默认1
rows | 10 | Integer | 否 |  每页数量 默认10 最大100

**响应参数**

参数名 | 示例值         | 参数类型     | 参数描述
--- |-------------|----------| ---
code | 200         | Integer  |  状态码
msg | success     | String   |  响应消息
ts | ************* | Long     |  时间戳
data |             | Object[] |
├─id | 3           | Integer  |  应用分类id
├─classifyName | f1          | String   |  应用分类名称
├─appNum | 4           | Integer  |  应用数量
├─enable | true        | Boolean  |  是否启用
├─remark | test        | String   |  描述
├─createBy | admin            | String   |  创建人
├─createTime | 2024-12-27 17:16:20            | String   |  创建时间

**请求示例**

```javascript
{
    "enable":true,
    "page":1,
    "rows":10
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1735883927104,
        "data": [
        {
            "id": 3,
            "classifyName": "f1",
            "appNum": 4,
            "enable": true,
            "remark": "你好",
            "createBy": "admin",
            "createTime": "2024-12-27 17:16:20"
        }
    ]
}
```

#### **应用启停执行结果**

通过应用启停任务ID来获取实例的应用启停的结果

**接口地址**

> /task-center/open/task/appOperateInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
taskIds |  | Integer [] | 是 |
├─ | 1 | Integer | 是 | 任务ID

**响应参数**

参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | Integer |  状态码
msg | success | String |  响应消息
ts | ************* | Long |  时间戳
data |  | Object[] |
├─ taskId | 1 | Integer | 任务ID
├─ padCode | AC22020020793 | String | 实例编号
├─ taskStatus | 3| Integer |  任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）
├─ endTime | 1756021166163 | Long |  任务执行结束时间
├─ taskContent | Success | String |  任务内容
├─ taskResult | Success | String |  任务结果

**请求示例**

```javascript
{
 "taskIds": [1]
}
```

**响应示例**

```javascript
{
 "code": 200,
 "msg": "success",
 "ts":1713773577581,
 "data":[
    {
    "taskId": 1,
    "taskStatus": 3,
    "padCode": "AC22020020793",
    "taskContent": "Success",
    "taskResult": "Success",
    "endTime": 1756121167163
    }
   ]
}
```

### 镜像管理

#### **获取镜像列表**

获取可用镜像列表。

**接口地址**

> /openapi/open/image/queryImageList

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Query参数**

参数名 | 示例值                 | 参数类型    | 是否必填 | 参数描述
--- |---------------------|---------|------| ---
imageType | 1                   | Integer | 否    |镜像类型, 1:公共镜像,2:自定义镜像
releaseType | 1                   | Integer | 否    |镜像版本 1测试版 2正式版
romVersion | android13           | String  | 否    |AOSP版本(android13，android14)
createTimeStart | 2025-03-11 00:00:00 | String  | 否    |创建开始时间(格式为：yyyy-MM-dd HH:mm:ss)
createTimeEnd | 2025-03-11 00:00:00 | String  | 否    |创建结束时间(格式为：yyyy-MM-dd HH:mm:ss)
page | 1                   | Integer | 否    |页码,默认1
rows | 10                  | Integer | 否    |每页条数,默认10,最大100

**响应参数**

|参数名 | 示例值             | 参数类型 | 参数描述           |
|--- |-----------------| --- |----------------|
|code | 200             | Integer | 状态码            |
|msg | success         | String | 响应消息           | | |
|ts | 1717643679112   | Long | 时间戳            | |
|data |                 | Object |                | |
|├─records |                 | Object[] | 记录列表           | |
|├─├─imageId | img-25030796215 | String | 镜像ID           |
|├─├─imageName | 20250307_4      | String | 镜像名称           |
|├─├─imageTag | 20250307_4      | String | 镜像Tag          |
|├─├─serverType |     Cruise10            | String | SOC类型          |
|├─├─romVersion | android13       | String | Rom版本          |
|├─├─imageDesc | WIFI自定义版        | String | 描述             |
|├─├─releaseType | 1               | Integer | 发版版本 1测试版 2正式版 |
|├─total |                 | Integer | 总记录条数          | |
|├─size |                 | Integer | 每页大小             | |
|├─current |                 | Integer | 当前页码           | |
|├─pages |                 | Integer | 总页数           | |

**请求示例**

```javascript
{
    "imageType":1,
        "releaseType":1,
        "romVersion":"android13",
        "createTimeStart":"2025-02-11 00:00:00",
        "createTimeEnd":"2025-03-11 00:00:00",
        "page":1,
        "rows":10
}
```

**响应示例**

```javascript
{
    "code": 200,
        "msg": "success",
        "ts": 1741688704152,
        "data": {
        "records": [
            {
                "imageId": "img-25030796215",
                "imageName": "20250307_4",
                "imageTag": "20250307_4",
                "serverType": "Cruise10",
                "romVersion": "android13",
                "imageDesc": "",
                "releaseType": 1
            }
        ],
            "total": 4,
            "size": 10,
            "current": 1,
            "pages": 1
    }
}
```


#### **镜像上传任务结果查询**

通过镜像ID来获取镜像上传状态。

**接口地址**

> /openapi/open/image/selectImageInfo

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名 | 示例值             | 参数类型   | 是否必填 | 参数描述|
|--- |-----------------|--------| --- | --- |
|imageId | img-25052870125 | String | 是 | 镜像ID|

**响应参数**

|参数名 | 示例值           | 参数类型 | 参数描述                            |
|--- |---------------| --- |---------------------------------|
|code | 200           | Integer | 状态码                             |
|msg | success       | String | 响应消息                            |
|ts | 1718262521168 | Long | 时间戳                             |
|data |               | Object |                                 |
|├─imageId | img-25052870125            | Integer | 镜像ID                            |
|├─status | 2             | Integer | 任务状态：（-1：失败；2：成功；10：初始化，11：下载中） |
|├─ failMsg | 文件下载失败        | String | 错误信息                            

**请求示例**

```javascript
{
   "imageId":"img-25052870125"
}
```

**响应示例**

```javascript
{
   "code": 200, 
   "msg": "success",
   "ts": 1748576968083,
   "data": {
      "imageId": "img-25052870125",
      "status": 2,
      "failMsg": "SUCCESS"
   }
}
```
### 网存2.0
---
<font color=red size=5>目前网存集群2.0 处于测试阶段，需要联系官方才能使用</font>
#### 一、网存2.0对比网存1.0

![NetPadV2FlowDoc.png](netPadV2FlowDoc.png)

- 大幅优化了 API 接口，降低开机/关机任务失败率。
- 网存2.0将实例与存储二合一，无需再进行复杂的存储块与实例关系管理，删除实例即删除实例与对应存储。
- 后续将增加网存克隆、1.0网存迁移2.0网存的能力，可以批量将单个实例复制成多个。
---
#### 二、快速入门

![netPadV2UseDoc.png](netPadV2UseDoc.png)

1. 购买板卡获得算力
2. 查询板卡信息
3. 设置板卡规格（多开数）
4. 创建网存实例
5. 使用刚创建好的实例或之前关机的实例进行开机
6. 完成业务关机
7. （待上线）将单个实例克隆成多个后使用（克隆出的实例是单独的实体，可以修改实例的机型属性、镜像版本等）
---
#### 三、API 接口
##### **创建网存实例**

用于创建网存2.0实例接口，支持批量创建，单次最多创建100个实例。

**接口地址**

/openapi/open/pad/v2/net/storage/res/create

**请求方式**

POST

**请求数据类型**

application/json

**请求Body参数**

| 参数名                 | 示例值        | 参数类型    | 是否必填 | 参数描述                                 |
|---------------------|------------|---------|------|--------------------------------------|
| clusterCode         | 001        | String  | 是    | 集群编码，标识实例所属集群                        |
| specificationCode   | SPEC_001   | String  | 是    | 规格代码，定义实例的硬件规格                       |
| imageId             | IMG_123456 | String  | 是    | 镜像ID，用于创建实例的系统镜像                     |
| number              | 5          | Integer | 是    | 实例数量，必须在1-100之间                      |
| storageSize         | 32         | Integer | 是    | 存储大小(GB)，必须大于0，支持：4,16,32,64,128,256 |
| screenLayoutCode    | LAYOUT_001 | String  | 否    | 屏幕布局编码，非随机ADI模板且未传入ADI模板参数时必填        |
| randomADITemplates  | false      | Boolean | 否    | 是否随机选择ADI模板，默认false                  |
| realPhoneTemplateId | 12345      | Long    | 否    | ADI模板ID，指定使用的ADI模板                   |
| countryCode         | SG         | String  | 否    | 国家编码，默认SG                            |
| groupId             | 0          | Integer | 否    | 分组ID，默认为公共池。                         |

**响应参数**

| 参数名         | 示例值              | 参数类型     | 参数描述          |
| ----------- | ---------------- | -------- | ------------- |
| code        | 200              | Integer  | 状态码（200表示成功）  |
| msg         | success          | String   | 接口请求状态信息      |
| ts          | 1742536327373    | Long     | 时间戳           |
| data        | [ {...} ]        | Object[] | 创建结果列表        |
| ├─ padCode  | ACN1234567890123 | String   | 实例编码          |
| ├─ padName  | 网存实例001         | String   | 实例名称          |
| ├─ status   | 0                | Integer  | 实例状态          |

**请求示例**

```json
{
  "clusterCode": "001",
  "specificationCode": "SPEC_001",
  "imageId": "IMG_123456",
  "number": 5,
  "storageSize": 32,
  "screenLayoutCode": "LAYOUT_001",
  "randomADITemplates": false,
  "countryCode": "SG"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN1234567890123",
      "clusterCode": "001",
      "storageSize": 32
    },
    {
      "padCode": "ACN1234567890124",
      "clusterCode": "001", 
      "storageSize": 32
    }
  ],
  "ts": 1742536327373
}
```

**错误码**

| 错误码    | 错误说明               | 操作建议                          |
| ------ | ------------------ |-------------------------------|
| 100000 | 集群编码不能为空           | 提供有效的集群编码                     |
| 100000 | 规格代码不能为空           | 提供有效的规格代码                     |
| 100000 | 镜像ID不能为空           | 提供有效的镜像ID                     |
| 100000 | 实例数量必须在1-100之间     | 调整实例数量到有效范围内                  |
| 100000 | 存储大小必须大于0          | 提供有效的存储大小                     |
| 100000 | 屏幕布局编码不能为空         | 在非随机ADI模板并且未指定ADI模板时下提供屏幕布局编码 |
| 100000 | DNS格式错误            | 确认DNS格式是否正确                   |
| 110044 | 实例规格不存在            | 确认规格代码是否正确，联系管理员确认规格配置        |
| 110041 | 镜像不存在              | 确认镜像ID是否正确，检查镜像是否可用           |
| 110045 | 屏幕布局不存在            | 确认屏幕布局编码是否正确                  |
| 110099 | ADI模板不存在,请检查参数     | 确认ADI模板ID是否正确                 |
| 220003 | 暂不支持当前存储规格,请参考文档设置 | 使用支持的存储规格：4,16,32,64,128,256GB |
| 220009 | 当前集群存储容量不足,请联系管理员  | 联系管理员增加存储容量或删除清理实例            |
| 110077 | 获取实例MAC失败，请重试      | 稍后重试或联系管理员                    |
##### **网存实例批量开机**

用于对多个网存实例进行批量开机操作

**接口地址**

> `/openapi/open/pad/v2/net/storage/batch/boot/on`

**请求方式**

> `POST`

**请求数据类型**

> `application/json`

**请求Body参数**

| 参数名         | 示例值                                                 | 参数类型     | 是否必填 | 参数描述                                                                                                  |
| ----------- | --------------------------------------------------- | -------- | ---- | ----------------------------------------------------------------------------------------------------- |
| padCodes    | ["ACN250321HRKNE3F", "ACN250321HRKNE3G"]            | String[] | 是    | 需要开机的实例编码列表，数量范围1-200个                                                                                |
| dns         | *******                                             | String   | 否    | DNS配置                                                                                                 |
| countryCode | SG                                                  | String   | 否    | 国家编码(具体查看:[https://chahuo.com/country-code-lookup.html](https://chahuo.com/country-code-lookup.html)) |
| androidProp | {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"} | Object   | 否    | 安卓属性配置，参考 [安卓改机属性列表](https://docs.armcloud.net/cn/server/InstanceAndroidPropList.html)                |
| timeout     | 1800                                                | Integer  | 否    | 超时时间（秒），范围300-7200秒（5分钟-120分钟）                                                                        |

**响应参数**

| 参数名                       | 示例值                | 参数类型    | 参数描述                                        |
| ------------------------- | ------------------ | ------- | ------------------------------------------- |
| code                      | 200                | Integer | 状态码（200不代表实例操作成功，实例是否操作成功需要自行判断successList） |
| msg                       | success            | String  | 接口请求状态信息                                    |
| ts                        | 1742536327373      | Long    | 时间戳                                         |
| data                      | {...}              | Object  | 操作结果数据                                      |
| ├─ successList            | [{...}]            | Array   | 操作成功的列表（异步回调，代表当前异步执行成功，业务结果需等待回调）          |
| ├─ successList[].taskId   | 13023              | Integer | 任务ID                                        |
| ├─ successList[].padCode  | ACN280512603291648 | String  | 实例编码                                        |
| ├─ successList[].vmStatus | 0                  | Integer | 实例状态                                        |
| ├─ failList               | [{...}]            | Array   | 操作失败的列表，由于业务校验不通过或是其他异常导致的无法操作              |
| ├─ failList[].padCode     | ACN279133733257216 | String  | 实例编码                                        |
| ├─ failList[].errCode     | 110028             | String  | 错误码                                         |
| ├─ failList[].errMsg      | 实例不存在              | String  | 失败原因                                        |
**请求示例**

``` json
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "dns": "*******",
  "countryCode": "US",
  "androidProp": {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"},
  "timeout": 1800
}
```

**响应示例**

``` json
{
    "code": 200,
    "msg": "success",
    "ts": 1752480095637,
    "data": {
        "successList": [
            {
                "taskId": 10111,
                "padCode": "ACN281696605700096",
                "vmStatus": 0
            }
        ],
        "failList": [
            {
                "errCode": 110028,
                "padCode": "ACN281696605700095",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700094",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700093",
                "errMsg": "实例不存在"
            }
        ]
    }
}
```

**错误码**

| 错误码     | 错误说明                           | 操作建议                     |
| ------- | ------------------------------ | ------------------------ |
| 100000  | 实例编码列表不能为空                     | 确保padCodes参数不为空          |
| 100000  | 实例数量范围1-200                    | 确保padCodes数量在1-200范围内    |
| 100000  | 超时时间必须在5分钟-120分钟之间             | 确保timeout参数在300-7200秒范围内 |
| 100000  | DNS格式错误                        | 确认DNS格式是否正确              |
| 110042  | 存在不属于当前用户的实例                   | 检查实例是否属于当前用户             |
| 110071  | 存在非网存实例                        | 确认实例类型为网存实例              |
| 110079  | 存在未释放算力的实例                     | 等待算力释放后重试                |
| 111070  | 请勿重复操作，当前实例正在开机中               | 等待当前开机操作完成后重试            |
| 111076  | 请勿重复操作，当前实例正在关机中               | 等待关机操作完成后重试              |
| 111071  | 存在非关机且非开机失败状态的实例，不允许操作开机       | 确认实例状态为关机或开机失败状态         |
| 111074  | 网存实例批量开机失败（系统异常）               | 系统异常，请稍后重试或联系技术支持        |
| 110074  | 获取算力单元失败，请重试                   | 稍后重试或联系管理员               |
| 110075  | 获取实例IP失败，请重试                   | 稍后重试                     |
| 110076  | ARM服务器网段不存在                    | 联系管理员检查网段配置              |
| 2200014 | 当前集群下没有可用的算力资源，算力资源不足，请联系管理员处理 | 等待算力资源释放或联系管理员           |
| 111079 | 当前实例所属板卡已离线      | 稍后重试或联系管理员                    |
| 111081 | 不支持网存1.0实例      | 使用网存2.0实例调用接口                 |
##### 网存实例批量关机

用于对网存2.0实例进行批量关机操作。支持同时关机多个实例，只有运行中或关机失败状态的实例才能进行关机操作。

**接口地址**

> `/openapi/open/pad/v2/net/storage/batch/off`

**请求方式**

> `POST`

**请求数据类型**

> `application/json`

**请求Body参数**

| 参数名      | 示例值                                      | 参数类型 | 是否必填 | 参数描述 |
|----------|------------------------------------------| - | ---- |  |
| padCodes | ["ACN250321HRKNE3F", "ACN250321HRKNE3G"] | String[] | 是    | 需要关机的实例编码列表，最多允许同时关机200个实例 |
| timeout  | 1800                                     | Integer | 否    | 超时时间（秒），范围：300-7200秒（5分钟-120分钟） |
| forceDel | false                                    | Boolean  | 否    | <font color=red size=5>**请谨慎使用**</font> 是否强制删除（会直接关机并删除实例，<font color=red size=5>不保留数据</font>） 默认为false  CBS版本2.3.5以上支持 |

**响应参数**

| 参数名                       | 示例值                | 参数类型    | 参数描述                                        |
| ------------------------- | ------------------ | ------- | ------------------------------------------- |
| code                      | 200                | Integer | 状态码（200不代表实例操作成功，实例是否操作成功需要自行判断successList） |
| msg                       | success            | String  | 接口请求状态信息                                    |
| ts                        | 1742536327373      | Long    | 时间戳                                         |
| data                      | {...}              | Object  | 操作结果数据                                      |
| ├─ successList            | [{...}]            | Array   | 操作成功的列表（异步回调，代表当前异步执行成功，业务结果需等待回调）          |
| ├─ successList[].taskId   | 13023              | Integer | 任务ID                                        |
| ├─ successList[].padCode  | ACN280512603291648 | String  | 实例编码                                        |
| ├─ successList[].vmStatus | 0                  | Integer | 实例状态                                        |
| ├─ failList               | [{...}]            | Array   | 操作失败的列表，由于业务校验不通过或是其他异常导致的无法操作              |
| ├─ failList[].padCode     | ACN279133733257216 | String  | 实例编码                                        |
| ├─ failList[].errCode     | 110028             | String  | 错误码                                         |
| ├─ failList[].errMsg      | 实例不存在              | String  | 失败原因                                        |

**请求示例**

``` json
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "timeout": 1800
}
```

**响应示例**

``` json
{
    "code": 200,
    "msg": "success",
    "ts": 1752480095637,
    "data": {
        "successList": [
            {
                "taskId": 10111,
                "padCode": "ACN281696605700096",
                "vmStatus": 0
            }
        ],
        "failList": [
            {
                "errCode": 110028,
                "padCode": "ACN281696605700095",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700094",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700093",
                "errMsg": "实例不存在"
            }
        ]
    }
}
```

**错误码**

| 错误码    | 错误说明 | 操作建议                 |
|--------|  | -------------------- |
| 100000 | 实例编码列表不能为空 | 确保padCodes参数不为空      |
| 100000 | 实例数量范围1-200 | 确保实例数量在1-200范围内      |
| 100000 | 超时时间必须在5分钟-120分钟之间 | 设置合理的超时时间（300-7200秒） |
| 110028 | 实例不存在 | 检查实例编码是否正确           |
| 110071 | 存在非网存实例 | 确保操作的都是网存实例          |
| 110042 | 存在不属于当前用户的实例 | 检查实例归属权限             |
| 111072 | 实例未绑定算力，无法关机 | 确保实例已绑定算力资源          |
| 111073 | 网存实例连续关机失败 | 检查实例状态，联系管理员处理       |
| 111070 | 请勿重复操作，当前实例正在开机中 | 等待当前开机操作完成后重试        |
| 111076 | 请勿重复操作，当前实例正在关机中 | 等待关机操作完成后重试          |
| 111078 | 存在非运行中且非关机失败状态的实例，不允许操作关机 | 确认实例状态为运行中或关机失败      |
| 111079 | 当前实例所属板卡已离线      | 稍后重试或联系管理员                    |
| 111080 | CBS版本不支持强制删除 | 有需要请联系管理员升级CBS版本   |
| 111081 | 不支持网存1.0实例      | 使用网存2.0实例调用接口                 |
##### 网存实例批量删除

用于批量删除网存实例，支持传入多个实例进行批量删除操作。

**接口地址**

> `/openapi/open/pad/v2/net/storage/batch/delete`

**请求方式**

> `POST`

**请求数据类型**

> `application/json`

**请求Body参数**

| 参数名      | 示例值                                      | 参数类型     | 是否必填 | 参数描述                           |
| -------- | ---------------------------------------- | -------- | ---- | ------------------------------ |
| padCodes | ["ACN250321HRKNE3F", "ACN250321HRKNE3G"] | String[] | 是    | 需要删除的实例编码列表，数量范围1-200个         |
| timeout  | 1800                                     | Integer  | 否    | 超时时间（秒），范围300-7200秒（5分钟-120分钟） |

**响应参数**

| 参数名                       | 示例值                | 参数类型    | 参数描述                                          |
| ------------------------- | ------------------ | ------- | --------------------------------------------- |
| code                      | 200                | Integer | 状态码（200不代表全部实例操作成功，实例是否操作成功需要自行判断successList） |
| msg                       | success            | String  | 接口请求状态信息                                      |
| ts                        | 1742536327373      | Long    | 时间戳                                           |
| data                      | {...}              | Object  | 操作结果数据                                        |
| ├─ successList            | [{...}]            | Array   | 操作成功的列表（异步回调，代表当前异步执行成功，业务结果需等待回调）            |
| ├─ successList[].taskId   | 13023              | Integer | 任务ID                                          |
| ├─ successList[].padCode  | ACN280512603291648 | String  | 实例编码                                          |
| ├─ successList[].vmStatus | 0                  | Integer | 实例状态                                          |
| ├─ failList               | [{...}]            | Array   | 操作失败的列表，由于业务校验不通过或是其他异常导致的无法操作                |
| ├─ failList[].padCode     | ACN279133733257216 | String  | 实例编码                                          |
| ├─ failList[].errCode     | 110028             | String  | 错误码                                           |
| ├─ failList[].errMsg      | 实例不存在              | String  | 失败原因                                          |
| ├─ completeList           | [{...}]            | Array   | 操作完成的列表，不需要进行异步操作的实例                          |
| ├─ completeList[].padCode | ACN279133733257231 | String  | 实例编码                                          |

**请求示例**

``` json
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "timeout": 1800
}
```

**响应示例**

``` json
{
    "code": 200,
    "msg": "success",
    "ts": 1752480095637,
    "data": {
        "successList": [
            {
                "taskId": 10111,
                "padCode": "ACN281696605700096",
                "vmStatus": 0
            }
        ],
        "failList": [
            {
                "errCode": 110028,
                "padCode": "ACN281696605700095",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700094",
                "errMsg": "实例不存在"
            },
            {
                "errCode": 110028,
                "padCode": "ACN281696605700093",
                "errMsg": "实例不存在"
            }
        ],
        "completeList": [
            {
                "padCode": "ACN281696605700096"
            }
        ]
    }
}
```

**错误码**

| 错误码    | 错误说明               | 操作建议                       |
| ------ | ------------------ | -------------------------- |
| 100000 | 实例编码列表不能为空         | 检查padCodes参数是否传递           |
| 100000 | 实例数量范围1-200        | 检查padCodes数组长度是否在有效范围内     |
| 100000 | 超时时间必须在5分钟-120分钟之间 | 检查timeout参数是否在300-7200秒范围内 |
| 110028 | 实例不存在              | 检查传入的实例编码是否正确              |
| 110042 | 存在不属于当前用户的实例       | 检查实例是否属于当前登录用户             |
| 110071 | 存在非网存实例            | 确认传入的实例都是网存实例              |
| 111070 | 请勿重复操作，当前实例正在开机中   | 等待开机操作完成后再进行删除             |
| 111073 | 当前实例非关机状态实例不允许删除   | 确保所有实例都处于关机状态再进行删除         |
| 111077 | 请勿重复操作，当前实例正在删除中   | 等待当前删除操作完成                 |
| 111079 | 当前实例所属板卡已离线      | 稍后重试或联系管理员                    |
| 111081 | 不支持网存1.0实例      | 使用网存2.0实例调用接口                 |
