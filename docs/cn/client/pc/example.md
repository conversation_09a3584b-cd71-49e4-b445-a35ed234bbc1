---
title: 示例搭建
---

### 示例demo

[点击下载](/armcloud_pc_demo.zip)

### 环境要求

推荐： Windows10、Visual Studio 2022、c++14或以上

最低兼容：Windows7、Visual Studio 2015、c++11

### 下载SDK

[点击下载](/armcloud_pc_sdk_v1.0.3.zip)

### 快速接入

#### 创建工程

创建一个windows桌面应用程序

![](1.png)

#### 设置依赖

设置头文件依赖目录

![](2.png)

#### 链接库

1、设置 链接器>常规>附加库目录

![](3.png)

2、设置 链接器>输入>附加依赖项

![](4.png)

#### 拷贝dll文件

将bin目录下对应的dll文件

![](5.png)

拷贝至

![](6.png)

#### 调试运行
