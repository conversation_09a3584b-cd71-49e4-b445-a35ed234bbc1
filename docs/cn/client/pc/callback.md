---
title: 回调函数
---

## SDK回调概览

### 云机回调(SessionObserver)

| 接口名称  | 接口描述  |
| ------------ | ------------ |
| [onConnected](#连接成功回调) | 网络连接成功 |
| [onDisconnected](#连接断开回调)  | 网络断开连接 |
| [onClose](#连接关闭回调) | 网络远端关闭 |
| [onScreenChange](#分辨率变化回调) | 收到云机宽、高、角度变化回调  |
| [onClipboardMessage](#剪切板回调) | 收到云机实列返回的剪贴板数据回调  |
| [onFirstVideoFrame](#视频首帧回调) | 启动云手机实列，订阅到视频流后的视频首帧回调  |
| [onLocalScreenshot](#本地截图回调) | 调用screenshot(true)后的视频帧回调 |
| [onNetworkQuality](#网络状态回调) | 当前网络状态回调  |
| [onIdeTimeout](#空闲超时回调) | 当前云手机实列无操作超时回调  |
| [onError](#错误回调) | 当前云手机错误回调  |
| [onCameraChanged](#摄像头状态回调) | 当前云手机摄像头状态回调  |

### 视频帧回调(VideoRenderSink)

| 接口名称  | 接口描述  |
| ------------ | ------------ |
| [onFrame](#视频帧回调) | 视频帧回调 |

### 批量拉流回调(BatchControlObserver)

| 接口名称  | 接口描述  |
| ------------ | ------------ |
| [onBatchPodStartResult](#批量拉流结果回调) | 批量拉流结果回调 |
| [onError](#批量拉流错误回调) | 批量拉流错误回调 |

### 视频帧(VideoFrame)

| 接口名称 | 接口描述 |
| ---- | ---- |
| [width](#视频帧宽度) | 视频帧宽度，单位PX |
| [height](#视频帧高度) | 视频帧高度，单位px |
| [rotation](#视频帧旋转角度) | 视频帧旋转角度（0度、90度、180度、270度） |
| [buffer](#视频帧数据) | 视频帧数据 |
| [size](#视频帧大小) | 视频帧大小 |

## SDK回调详细说明

#### 连接成功回调

描述：网络连接成功
语法：

```cpp
void onConnected()
```

#### 断开连接回调

描述：网络断开连接
语法：

```cpp
void onDisconnected()
```

#### 连接关闭回调

描述：网络远端关闭
语法：

```cpp
void onClose()
```

#### 分辨率变化回调

描述：收到云机宽高
语法：

```cpp
void onScreenChange(int width, int height, int rotation)
```

| 参数 | 描述  |
| ------------ | ------------ |
| width  | 云机宽度 |
| height  | 云机高度 |
| rotation  | 云机旋转角度 |

#### 剪切板回调

描述：收到云机实列返回的剪贴板数据回调
语法：

```cpp
void onClipboardMessage(const std::string& text)
```

| 参数 | 描述  |
| ------------ | ------------ |
| text | 云手机剪贴板文本，UTF-8编码 |

#### 视频首帧回调

描述：启动云手机实列，订阅到视频流后的视频首帧回调
语法：

```cpp
void onFirstVideoFrame()
```

#### 本地截图回调

描述：调用screenshot(true)后的视频帧回调
语法：

```cpp
void onLocalScreenshot(std::shared_ptr<VideoFrame>& frame)
```

| 接口 | 描述 |
| ---- | ---- |
| frame | 视频帧回调，参考[VideoFrame](#视频帧) |

#### 网络状态回调

描述：当前网络状态回调
语法：

```cpp
void onNetworkQuality(int rtt)
```

| 参数 | 描述  |
| ------------ | ------------ |
| rtt | 网络延迟 |

#### 空闲超时回调

描述：当前云手机实列无操作超时回调
语法：

```cpp
void onIdeTimeout()
```

#### 云机错误回调

描述：当前云手机实列无操作超时回调
语法：

```cpp
void onError(int error, const std::string& msg)
```

| 参数 | 描述 |
| ---- | ---- |
| error | 错误码 |
| msg | 错误描述 |

#### 摄像头状态回调

描述：当前云手机实列无操作超时回调
语法：

```cpp
void onCameraChanged(bool isFront, bool isOpen)
```

| 参数 | 描述 |
| ---- | ---- |
| isFront | 是否是前摄像头 |
| isOpen | 是否打开 |

#### 视频帧回调

描述：视频帧回调
语法：

```cpp
void onFrame(std::shared_ptr<VideoFrame>& frame)
```

| 参数 | 描述 |
| ---- | ---- |
| frame | 参考[VideoFrame](#视频帧) |

#### 批量拉流结果回调

描述：批量拉流结果回调
语法：

```cpp
void onBatchPodStartResult(int error, const std::string& msg, const std::vector<std::string>& podList)
```

| 参数 | 描述 |
| ---- | ---- |
| error | 批量拉流结果 |
| msg | 批量拉流错误信息 |
| podList | 批量拉流的云机列表 |

#### 批量拉流错误回调

描述：批量拉流错误回调
语法：

```cpp
void onError(int error, const std::string& msg)
```

| 参数 | 描述 |
| ---- | ---- |
| error | 批量拉流结果 |
| msg | 批量拉流错误信息 |

#### 视频帧宽度

描述：视频帧宽度，单位PX
语法：

```cpp
uint32_t width()
```

#### 视频帧高度

描述：视频帧高度，单位px
语法：

```cpp
uint32_t height()
```

#### 视频帧旋转角度

描述：视频帧旋转角度（0度、90度、180度、270度）
语法：

```cpp
uint32_t rotation()
```

#### 视频帧数据

描述：视频帧数据
语法：

```cpp
uint8_t* buffer()
```

#### 视频帧大小

描述：视频帧大小
语法：

```cpp
uint32_t size()
```
