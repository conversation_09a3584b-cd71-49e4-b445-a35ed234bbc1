---
title: 接口说明
---

本文档描述了云手机PC客户端SDK提供的接口。

## API概览

### 引擎(ArmcloudEngine)

| 接口名称 | 接口描述 |
| ------- | ------- |
| [instance](#获取引擎实例) | 获取引擎实例（单例模式） |
| [init](#初始化引擎) | 初始化引擎 |
| [destory](#销毁引擎) | 销毁引擎 |
| [setSdkHost](#设置服务器地址) | 设置服务器地址 |
| [createPhoneClient](#创建云机会话) | 创建云机会话，用于操控云机 |
| [createBatchControlVideo](#创建批量拉流控制器) | 创建批量拉流控制器 |
| [getVideoDeviceList](#获取摄像头列表) | 获取摄像头列表 |
| [getAudioDeviceList](#获取麦克风列表) | 获取麦克风列表 |

### 群控(GroupControl)

| 接口名称 | 接口描述 |
| ------- | ------- |
| [destory](#销毁) | 销毁 |
| [startEventSync](#开启群控) | 开启群控 |
| [stopEventSync](#停止群控) | 停止群控 |
| [setEventSyncMaster](#设置主控) | 设置主控 |
| [sendInputText](#发送文本到指定云机输入框) | 发送文本到指定云机输入框 |
| [startVideoCapture](#开启摄像头采集) | 开启摄像头采集 |
| [stopVideoCapture](#停止采集)| 停止采集 |
| [getCameraCount](#获取摄像头数量)| 获取摄像头数量 |
| [getCameraInfo](#获取摄像头信息) | 获取摄像头信息 |

### 小流(BatchControlVideo)

| 接口名称 | 接口描述 |
| ------- | ------- |
| [start](#开始拉流) | 开始拉流 |
| [stop](#停止拉流) | 停止拉流 |
| [stop](#批量停止拉流) | 批量停止拉流 |
| [stopAll](#全部停止拉流) | 全部停止拉流 |
| [contains](#是否包含) | 是否包含 |
| [subscribe](#开始订阅) | 开始订阅 |
| [unsubscribe](#取消订阅)| 取消订阅 |
| [setVideoSink](#设置视频渲染器)| 设置视频渲染器 |
| [setBatchControlObserver](#设置回调) | 设置回调 |

### 大流(PhoneClient)

| 接口名称 | 接口描述 |
| ------- | ------- |
| [start](#开启拉流) | 开启拉流 |
| [stop](#停止拉流) | 停止拉流 |
| [setVideoSink](#设置视频渲染器) | 设置视频渲染器 |
| [setSessionObserver](#设置云手机事件回调) | 设置回调 |
| [sendSwipe](#发送滚轮事件) | 发送滚轮事件 |
| [sendKeyCode](#发送按键事件) | 发送键盘按键事件（发送一次完整的DOWN+UP事件） |
| [sendKeyCode](#发送按键事件) | 发送键盘按键事件（可选择发送单状态事件） |
| [volumeUp](#增大音量) | 增大云机设备音量 |
| [volumeDown](#减小音量) | 减小云机设备音量 |
| [sendTouchEvent](#发送触摸事件) | 发送触摸事件 |
| [setVideoLevel](#修改拉流画质) | 修改拉流画质 |
| [screenshot](#截图) | 截图 |
| [sendInputText](#发送输入文本) | 发送输入文本 |
| [enableAudio](#开启音频) | 开启音频 |
| [enableVideo](#开启视频) | 开启视频 |
| [enableBlow](#开启吹一吹) | 开启吹一吹 |
| [shake](#摇一摇) | 摇一摇 |
| [startVideoCapture](#开启摄像头采集) | 开启摄像头采集 |
| [stopVideoCapture](#停止采集) | 停止采集 |
| [publishStream](#发布视频流) | 发布视频流 |
| [unPublishStream](#停止发布) | 停止发布 |
| [sendCommand](#发送adb命令) | 发送adb命令 |

## 接口详细说明

#### 获取引擎实例

描述：获取ArmcloudEngine引擎实例

语法：

``` cpp
static ArmcloudEngine* instance();
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| appId | string | 是 | 设置日志文件路径 |
| logPath | wstring| 否 | 日志文件路径 |
| isInitEngine | bool | 否 | 是否提前初始化引擎 |

#### 初始化引擎

描述：初始化ArmcloudEngine引擎

语法：

``` cpp
bool init(const std::string& appId, const std::wstring& logPath = L"", bool isInitEngine = false);
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| appId | string | 是 | 设置日志文件路径 |
| logPath | wstring| 否 | 日志文件路径 |
| isInitEngine | bool | 否 | 是否提前初始化引擎 |

#### 销毁引擎

描述：销毁ArmcloudEngine引擎

语法：

``` cpp
void destory();
```

#### 设置服务器地址

描述：设置服务器地址
语法：

``` cpp
void setSdkHost(const std::string& host);
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| host | string | 是 | 设置服务器地址 |

#### 创建云机会话

描述：创建云机会话
语法：

``` cpp
PhoneClient* createPhoneClient();
```

#### 创建批量拉流控制器

描述：创建批量拉流控制器

语法：

``` cpp
BatchControlVideo* createBatchControlVideo();
```

#### 获取摄像头列表

描述：获取摄像头列表

语法：

``` cpp
std::vector<DeviceInfo> getVideoDeviceList();
```

#### 获取麦克风列表

描述：获取麦克风列表

语法：

``` cpp
std::vector<DeviceInfo> getAudioDeviceList();
```

#### 销毁

描述：销毁

语法：

```cpp
void destory();
```

#### 开启群控

描述：开启群控

语法：

``` cpp
void startEventSync(const BatchPhoneConfig& config)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| config | BatchPhoneConfig | 是 | 群控配置 |

#### 停止群控

描述：停止群控

语法：

``` cpp
void stopEventSync()
```

#### 设置主控

描述：设置主控

语法：

``` cpp
void setEventSyncMaster(const std::string& podId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 主控云机ID |

#### 发送文本到指定云机输入框

描述：发送文本到指定云机输入框

语法：

``` cpp
void sendInputText(const std::string& podId, const std::string& content)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 主控云机ID |
| content | string | 是 | 发送的文本内容，必须为UTF-8编码 |

#### 开始拉流

描述：开始拉流

语法：

``` cpp
void start(const BatchPhoneConfig& config)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| config | BatchPhoneConfig | 是 | 批量拉流配置 |

#### 停止拉流

描述：停止拉流

语法：

``` cpp
void stop(const std::string& podId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 需要停止拉流的云机ID |

#### 批量停止拉流

描述：批量停止拉流

语法：

``` cpp
void stop(const std::vector<std::string>& podList)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podList | string[] | 是 | 需要停止拉流的云机ID列表 |

#### 全部停止拉流

描述：全部停止拉流

语法：

``` cpp
void stopAll()
```

#### 是否包含

描述：是否包含

语法：

``` cpp
bool contains(const std::string& podId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 需要查询的云机ID |

#### 开始订阅

描述：开始订阅

语法：

``` cpp
void subscribe(const std::string& podId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 需要订阅视频流的云机ID |

#### 取消订阅

描述：取消订阅

语法：

``` cpp
void unsubscribe(const std::string& podId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 需要取消订阅视频流的云机ID |

#### 设置视频渲染器

描述：设置视频渲染器

语法：

``` cpp
void setVideoSink(const std::string& podId, VideoRenderSink* sink)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| podId | string | 是 | 需要设置渲染器的云机ID |
| sink | VideoRenderSink | 是 | 视频渲染器 |

#### 设置回调

描述：设置回调

语法：

``` cpp
void setBatchControlObserver(BatchControlObserver* observer)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| observer | BatchControlObserver | 是 | 回调 |

#### 开启拉流

描述：开启拉流

语法：

``` cpp
void start(PhoneConfig& config)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| config | PhoneConfig | 是 | 连接云机配置 |

#### 停止拉流

描述：停止拉流

语法：

``` cpp
void stop();
```

#### 设置视频渲染器

描述：设置视频渲染器

语法：

``` cpp
void setVideoSink(VideoRenderSink* sink)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| sink | VideoRenderSink* | 是 | 视频渲染器 |

#### 设置云手机事件回调

描述：调用setSessionObserver()设置云手机事件回调

语法：

``` cpp
void setSessionObserver(SessionObserver* observer)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| observer | SessionObserver* | 是 | 事件回调 |

#### 发送滚轮事件

描述：发送滚轮事件

语法：

``` cpp
void sendSwipe(int action, int x, int y, int width, int height, int swipe)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| action | int | 是 | 事件回调 |
| x | int | 是 | 事件回调 |
| y | int | 是 | 事件回调 |
| width | int | 是 | 事件回调 |
| height | int | 是 | 事件回调 |
| swipe | int | 是 | 事件回调 |

#### 发送按键事件

描述：发送键盘按键事件指令到云机实例。此接口是发送一次完整的 DOWN+UP 事件，如需模拟 Home键、返回键、菜单键等系统功能键，推荐使用。

语法：

```cpp
void sendKeyCode(int code)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| code | int | 是 | 键值 <br> 3：HOME键 <br> 4：BACK键 <br> 82：MENU键 <br> 187：TASK键 |

#### 发送按键事件

描述：发送键盘按键事件指令到云机实例。此接口可以选择发送单状态（DOWN 或 UP 事件），如需模拟一些状态要求较高的按键事件，如 DELETE 等，推荐使用。

语法：

```cpp
void sendKeyCode(int action, int code)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| action | int | 是 | 按键类型 <br> 0：DOWN状态 <br> 1：UP状态 |
| code | int | 是 | 键值 <br> 3：HOME键 <br> 4：BACK键 <br> 82：MENU键 <br> 187：TASK键 |

#### 增大音量

描述：增大云机设备音量

语法：

```cpp
void volumeUp()
```

#### 减小音量

描述：减小云机设备音量

语法：

```cpp
void volumeDown()
```

#### 发送鼠标按键事件

描述：发送鼠标按键事件

语法：

```cpp
void sendTouchEvent(int action, int x, int y, int width, int height)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| action | int | 是 | 按键类型 <br> 0、DOWN状态 <br> 1、UP状态 <br> 2、MOVE状态 |
| x | int | 是 | x轴方向点击坐标 |
| y | int | 是 | y轴方向点击坐标 |
| width | int | 是 | 云手机本地画面宽度 |
| height | int | 是 | 云手机本地画面高度 |

#### 修改拉流画质

描述：设置云手机画面清晰度

语法：

```cpp
void setVideoLevel(int resolution, int fps, int bitrate)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| resolution | int | 是 | 07：144x256 <br> 08：216x384 <br> 08：216x384 <br> 09：288x512 <br> 10：360x640 <br> 11：480x848 <br> 12：540x960 <br> 13：600x1024 <br> 14：480x1280 <br> 15：720x1280 <br> 16：720x1920 <br> 17：1080x1920 <br> 18：1440x1920 <br> 19：1600x2560 <br> 20：2880x1080 |
| fps | int | 是 | 1：20fps <br> 2：25fps <br> 3：30fps <br> 4：60fps <br> 5：1fps <br> 6：5fps <br> 7：10fps <br> 8：15fps <br> 9：2fps |
| bitrate | int | 是 | 01：1Mbps <br> 02：1.5Mbps <br> 03：2Mbps <br> 04：2.5Mbps <br> 05：3Mbps <br> 06：3.5Mbps <br> 07：4Mbps <br> 08：5Mbps <br> 09：6Mbps <br> 10：8Mbps <br> 11：10Mbps <br> 12：12Mbps <br> 13：200kbps <br> 14：400kbps <br> 15：600kbps |

#### 截图

描述：对当前云手机画面进行截图

语法：

```cpp
void screenshot(bool local)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| local | bool | 否 | 截图保存位置 <br> true：本地 <br> false：云机 |

#### 发送输入文本

描述：发送文本替换云机输入框内容（当云机输入框在焦点上才会生效）。

语法：

```cpp
void sendInputText(const std::string& text)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| text | string | 是 | 发送的文本内容，必须为UTF-8编码 |

#### 开启音频

描述：音频开启开关

语法：

```cpp
void enableAudio(bool enable)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| enable | bool | 是 | 状态 true、开启 false、停止 |

#### 开启视频

描述：视频开启开关

语法：

```cpp
void enableVideo(bool enable)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| enable | bool | 是 | 状态 true、开启 false、停止 |

#### 开启吹一吹

描述：吹一吹开启开关

语法：

```cpp
void enableBlow(bool enable)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| enable | bool | 是 | 状态 true、开启 false、停止 |

#### 摇一摇

描述：摇一摇

语法：

```cpp
void shake()
```

#### 开启摄像头采集

描述：开启摄像头采集

语法：

```cpp
void startVideoCapture(int cameraId)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| cameraId | int | 是 | 摄像头id |

#### 停止采集

描述：停止采集

语法：

```cpp
void stopVideoCapture()
```

#### 发布视频流

描述：发布视频流

语法：

```cpp
void publishStream()
```

#### 停止发布

描述：停止发布

语法：

```cpp
void unPublishStream()
```

#### 发送adb命令

描述：发送adb命令

语法：

```cpp
void sendCommand(const std::string& cmd)
```

| 参数 | 类型 | 是否必填 | 描述  |
| ---- | ---- | ---- | ---- |
| cmd | string | 是 | adb命令 |
