---
title: 示例搭建
---

## 示例搭建

环境要求

```text
    iOS 11.0 及以上系统版本
    Android 浏览器：需要支持 WebRTC 的浏览器内核
    PC Web 浏览器：内核为 Chrome 58+ 的浏览器，以及 Safari 11 及以上版本
```

## 快速接入

方法一：下载 armcloudH5SDK 包文件后，解压后引入即可

[点击下载(armcloudH5SDK.zip)](/armcloudH5SDK.zip)

```js
import { ArmcloudEngine } from "@/lib/index.es.js";
```

方法二：通过 npm 加载。

```sh
安装：
# npm
npm i armcloud-rtc

# yarn
yarn add armcloud-rtc

# pnpm
pnpm add armcloud-rtc
```

```js
// 使用
import { ArmcloudEngine } from "armcloud-rtc";
const engine = new ArmcloudEngine({
  //...初始化参数
});
```

说明： 1.在页面上创建一个容器

```html
<div
  class="phoneBox"
  id="phoneBox"
  style="background-color: grey;overflow:hidden;width: 100%;height: 100%;text-align: center;"
></div>
```

2.通过 PaaS 接口获取实例信息，获得实例连接信息。 3.获取到实例信息数据后 **const armCloud = new ArmcloudEngine({//...初始化参数})** 进行初始化。 4.初始化成功后，通过 **armCloud.start()** 进行设备链接。 5.需要停止链接云手机，调用 **armCloud.stop()** 断开链接。

## 示例Demo

[点击下载(armcloudH5SDK-demo.zip)](/armcloudH5SDK-demo.zip)

## 示例代码

<p style='color:red'>baseUrl: 请设置对应的地址</p>

- 国内：<https://api.xiaosuanyun.com>
- 海外：<https://openapi-hk.armcloud.net>

<p style="color:red">
  token: 通过调用服务端接口
  <a href="/server/OpenAPI.html#sdk-token签发">/openapi/open/token/stsToken</a>

</p>

- 注：国内请调用国内的OpenAPI地址，海外请调用海外的OpenAPI地址

```html
// 以vue项目为例
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { showNotify, showDialog } from "vant";
import "vant/es/notify/style";
import "vant/es/dialog/style";
import { ArmcloudEngine } from "armcloud-rtc";

const armCloud: any = ref(null);

const handleStart = () => {
    // RTC初始化
    const params = {
        baseUrl: 'https://openapi-hk.armcloud.net', // sdk请求域名 默认 https://openapi.armcloud.net
        token: '', //  必填，从服务端调用/openapi/open/token/stsToken获取到的token
        retryCount: 2, // ws重连次数 默认2
        retryTime: 2000, // ws每次重连间隔 默认2000
        enableMicrophone: true, // 是否开启麦克风
        enableCamera: true, // 是否开启摄像头
        deviceInfo: {
            padCode: 'AC22030020000', // 必填，房间号
            userId: 'xxxx-xxxx-xxxx-xxx', // 必填, 你产品的用户id或其他唯一id
            videoStream: { // 选填，具体配置请查看 API详情-设置分辨率，码率，帧率
                resolution: 12, // 选填，分辨率，默认12（540 * 960）
                frameRate: 2, // 选填，帧率，默认2（25fps）
                bitrate: 3 // 选填，码率，默认3（2Mbps）
            },
            autoRecoveryTime: 300, // 选填，无操作回收时间，范围 1s~7200s，默认值：300
            mediaType: 3, // 选填，媒体流类型 1：只控制音频 2：只控制视频 3：同时控制音频和视频，默认值：3
            rotateType: 0, // 选填，旋转屏幕 0：竖屏， 1：横屏，默认值：0
            keyboard: 'pad', // 选填，当前键盘 'local'：本机键盘，'pad'：云机键盘，默认值：'pad'
            saveCloudClipboard: true, // 选填，是否接收云机剪切板回调 true: 接收，false：不接收，默认 true
        },
        viewId: "phoneBox", // 必填，容器id
        callbacks: { // 选填，回调集合
            // 初始化回调
            onInit: ({ code, msg }) => {
                console.log("init:", code, msg);
       
                // 初始化成功后，进行后续业务操作
                if (code === 0) {
                    // 浏览器是否支持rtc服务监听
                    const isSupported =  armCloud.value.isSupported();
                    if (!isSupported) {
                        showNotify({
                            type: "warning",
                            message: "该浏览器不支持RTC服务"
                        });
                        return false;
                    }
                    // 加入房间
                    armCloud.value.start();
                }
       
            },
            // 链接成功回调
            onConnectSuccess: () => {
                showNotify({
                    type: "success",
                    message: "进入房间成功"
                });
                // 获取SDK版本号
                const version = armCloud.value.version
                console.log('当前SDK版本号：', version)
            },
            // 链接失败回调
            onConnectFail: ({ code, msg }) => {
                console.log("fail:" code, msg);
                showNotify({
                    type: "danger",
                    message: msg
                });
            },
            // 无操作回收回调
            onAutoRecoveryTime: () => {
                console.log("触发回收回调");
                // 触发无操作回收后，SDK内部会执行 armCloud.value.stop() 离开房间业务逻辑，需重新再次加入房间
                showDialog({
                    message: "触发无操作回收，暂停拉流; 点击确认，恢复拉流"
                }).then(() => {
                    // on close
                    // 加入房间
                    armCloud.value.start();
                });
            },
            // 自动播放失败回调
            onAutoplayFailed: e => {
                console.log("自动播放失败", e);
                if (e.kind === "video") {
                    showDialog({
                        message: "自动播放视频失败; 点击确认，手动播放"
                    }).then(() => {
                        // on close
                        armCloud.value.startPlay();
                    });
                }
                if (e.kind === "audio") {
                    showDialog({
                        message: "自动播放音频失败; 点击确认，手动播放"
                    }).then(() => {
                        // on close
                        armCloud.value.startPlay();
                    });
                }
            },
            // 当前运行信息回调
            onRunInformation: (info) => {
                console.log("当前网络状况", info);
            },
            // 当分辨率发生变化时触发
            onChangeResolution: (width, height) => {
                console.log("当前分辨率", width, height);
            },
            // 当收到云端app透传的消息时的触发
            onTransparentMsg: (type, msg) => {
                console.log("消息透传", type, msg);
            },
            // 当播放出现异常时触发
            onErrorMessage: (event) => {
                console.log("异常", event);
            },
            // 云机文本内容复制触发回调
            onOutputClipper: (message) => {
                // 若初始化传入了配置想 saveCloudClipboard: false, 将无法收到回调消息
                console.log("复制文本内容", message);
            },
            // 视频首帧渲染成功
            onRenderedFirstFrame: () => {
                console.log("视频首帧渲染成功");
            }
        }
    };
    armCloud.value = new ArmcloudEngine(params);
}

/** 离开房间 */
const handleStop = () => {
    armCloud.value.stop();
}
</script>

<template>
  <div>
    <div
      id="phoneBox"
      class="phone-box"
      style="background-color: grey; overflow: hidden; width: 100%; height: 80%; text-align: center"
    />
    <div class="flex">
      <div @click="handleStart">加入房间</div>
      <div @click="handleStop">离开房间</div>
    </div>
  </div>
</template>
```
