---
title: 错误码
---

| 名称                | 值                    | 描述                                                                                                 |
| ------------------- | --------------------- | ---------------------------------------------------------------------------------------------------- |
| INVALID_ENGINE      | 'INVALID_ENGINE'      | 调用 destroyEngine 时，传入的参数不是合法的 engine 对象。                                            |
| INVALID_PARAMS      | 'INVALID_PARAMS'      | 通用错误码。调用方法时，传入的参数不合法。请根据各方法说明传入正确的参数。                           |
| INVALID_TOKEN       | 'INVALID_TOKEN'       | 调用 start 进房时使用了已过期的 Token 或使用的 Token 无效。请重新获取 Token 后再次调用 start 进房。  |
| JOIN_ROOM_FAILED    | 'JOIN_ROOM_FAILED'    | 调用 start 进房失败，具体错误原因查看 message。                                                      |
| REPEAT_JOIN         | 'REPEAT_JOIN'         | 重复进房。已加入房间后，又再次调用 start 时触发。                                                    |
| ROOM_FORBIDDEN      | 'ROOM_FORBIDDEN'      | 调用 start 进房失败，原因是房间被封禁。                                                              |
| USER_FORBIDDEN      | 'USER_FORBIDDEN'      | 调用 start 进房失败，原因是本地用户被封禁。                                                          |
| DUPLICATE_LOGIN     | 'DUPLICATE_LOGIN'     | 有相同用户 ID 的用户加入本房间，当前用户被踢出房间。通过 onErrorMessage 回调。                       |
| RTM_DUPLICATE_LOGIN | 'RTM_DUPLICATE_LOGIN' | 相同用户 ID 的用户登录，导致本地已登录用户被顶出。通过 onErrorMessage 回调                           |
| RTM_TOKEN_ERROR     | 'RTM_TOKEN_ERROR'     | 重连实时信令服务器时使用的 Token 异常。通过 onErrorMessage 回调。请使用新的 Token 重新登录。         |
| TOKEN_EXPIRED       | 'TOKEN_EXPIRED'       | 加入房间后，Token 过期。通过 onErrorMessage 回调。请获取新的 Token 后，调用 start 重新加入房间。     |
| RECONNECT_FAILED    | 'RECONNECT_FAILED'    | SDK 与服务端重连失败，并不再自动重试。通过 onErrorMessage 回调。你需退房后重新进房，或联系技术支持。 |
| KICKED_OUT          | 'KICKED_OUT'          | 服务端调用 OpenAPI 将当前用户踢出房间。通过 onErrorMessage 回调。                                    |
| ROOM_DISMISS        | 'ROOM_DISMISS'        | 服务端调用 OpenAPI 解散房间，所有用户被移出房间。通过 onErrorMessage 回调。                          |
