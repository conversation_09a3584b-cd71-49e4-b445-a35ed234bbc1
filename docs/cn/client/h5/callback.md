---
title: 回调函数
---

# H5 SDK 回调函数参考

**统一状态码说明：** `code: 0` 表示成功，`code: -1` 表示失败

## 初始化相关回调

### onInit：初始化

**功能描述：** SDK初始化结果回调。

**调用签名：**

```js
onInit({ code, msg })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `code` | String | 初始化结果状态码 |
| `msg` | String | 初始化失败原因 |

**返回码说明：**
| Code | 说明 |
|------|------|
| `0` | 初始化成功 |
| `-1` | 网络波动导致请求失败（可重试连接） |
| `100006` | 请求头信息缺失必要参数：token |
| `100007` | 无效的 token |
| `100008` | token 验证失败 |
| `120001` | pad 对应的房间不存在 |
| `120002` | 连接云机失败，请重试！ |
| `120003` | 中止推流错误，指令发送失败 |
| `120004` | 中止推流错误，指令服务异常 |
| `120005` | 实例不存在 |
| `120006` | token 绑定的 uuid 与请求 uuid 不一致 |
| `120007` | 原 token 信息不存在，请检查 |
| `120008` | 当前批次实例存在不同的推拉流方式，请联系管理员处理 |
| `120009` | token 绑定的 padCode 与请求 padCode 不一致 |

## 连接相关回调

### onConnectSuccess：连接成功

**功能描述：** 调用 `start()` 接口成功时触发。

**调用签名：**

```js
onConnectSuccess()
```

### onConnectFail：连接失败

**功能描述：** 调用 `start()` 接口失败时触发。

**调用签名：**

```js
onConnectFail({ code })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `code` | Number | 错误码，详见错误码说明文档 |

### onConnectionStateChanged：连接状态

**功能描述：** 连接状态变化回调，start成功后连接状态出现变更时触发。

**调用签名：**

```js
onConnectionStateChanged(e)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `e.state` | Number | 连接状态码 |

**状态码说明：**
| 状态码 | 描述 |
|--------|------|
| `0` | 进行连接前准备，锁定相关资源 |
| `1` | 连接断开 |
| `2` | 首次连接，正在连接中 |
| `3` | 首次连接成功 |
| `4` | 连接断开后重新连接中 |
| `5` | 连接断开后重连成功 |
| `6` | 处于断开状态超过10秒且重连未成功，SDK将继续尝试重连 |

### onSocketCallback：WebSocket状态

**功能描述：** WebSocket状态信息回调。

**调用签名：**

```js
onSocketCallback({ code })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `code` | Number | WebSocket状态码 |

**状态码说明：**
| 状态码 | 描述 |
|--------|------|
| `0` | 连接成功 |
| `1` | 连接关闭 |
| `-1` | 连接失败 |

## 媒体相关回调

### onRenderedFirstFrame：首帧渲染

**功能描述：** 视频首帧渲染成功时触发。

**调用签名：**

```js
onRenderedFirstFrame(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `event.width` | number | 分辨率高度 |
| `event.height` | number | 分辨率高度 |
| `event.userId` | string | 用户ID |
| `event.isScreen` | boolean | 是否全屏 |


### onChangeResolution：分辨率变化

**功能描述：** 用于监听分辨率变化，返回旧与新分辨率。

**调用签名：**

```js
onChangeResolution({ from, to })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `from` | Object | 旧分辨率对象 `{width, height}` |
| `to` | Object | 新分辨率对象 `{width, height}` |

### onAutoplayFailed：自动播放失败

**功能描述：** 自动播放音视频流失败时触发。

**调用签名：**

```js
onAutoplayFailed(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `userId` | String | 自动播放失败的流所属用户ID（不带此参数时表示本地流播放失败） |
| `kind` | String | 自动播放失败的媒体类型（`"video"` 或 `"audio"`） |
| `streamIndex` | Number | 视频流属性（`0`: 主流，`1`: 屏幕流） |
| `mediaType` | Number | 远端媒体流类型（`1`: audio, `2`: video, `3`: audio and video） |

### onChangeRotate：屏幕旋转

**功能描述：** 横竖屏切换时触发。

**调用签名：**

```js
onChangeRotate(type, info)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `type` | Number | 屏幕方向（`0`: 竖屏, `1`: 横屏） |
| `info` | Object | 屏幕尺寸信息 `{width, height}` |

## 数据采集相关回调

### onAudioInit：音频采集

**功能描述：** 采集音频数据回调，表示开始收取麦克风声音。

**调用签名：**

```js
onAudioInit(audioTrackSettings)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `audioTrackSettings` | Object | 音频轨道设置，参见 [MediaTrackSettings](https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackSettings) |

### onAudioError：音频错误

**功能描述：** 采集音频数据错误回调。

**调用签名：**

```js
onAudioError(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `event.code` | String | 错误码 |

**错误码说明：**
| 错误码 | 描述 |
|--------|------|
| `REPEAT_CAPTURE` | 重复采集 |
| `GET_AUDIO_TRACK_FAILED` | 采集音频失败，请确认是否有可用的采集设备，或是否被其他App占用 |
| `STREAM_TYPE_NOT_MATCH` | 流类型不匹配 |

### onVideoInit：视频采集

**功能描述：** 采集视频数据回调，表示开始收取摄像头画面。

**调用签名：**

```js
onVideoInit(videoTrackSettings)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `videoTrackSettings` | Object | 视频轨道设置，参见 [MediaTrackSettings](https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackSettings) |

### onVideoError：视频错误

**功能描述：** 采集视频数据错误回调。

**调用签名：**

```js
onVideoError(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `event.code` | String | 错误码 |

**错误码说明：**
| 错误码 | 描述 |
|--------|------|
| `REPEAT_CAPTURE` | 重复采集 |
| `GET_AUDIO_TRACK_FAILED` | 采集音频失败，请确认是否有可用的采集设备，或是否被其他App占用 |
| `STREAM_TYPE_NOT_MATCH` | 流类型不匹配 |

## 状态监控回调

### onErrorMessage：播放异常

**功能描述：** 当播放出现异常时触发。

**调用签名：**

```js
onErrorMessage({ code })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `code` | Number | 错误类型（`0`: RTC通道中断, `1`: 获取统计信息时出错） |

### onRunInformation：运行信息

**功能描述：** 回调当前运行信息，每2秒统计一次。

**调用签名：**

```js
onRunInformation(stats)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `stats` | Object | 远端媒体流统计信息 |
| `stats.userId` | String | 进房用户的userId |
| `stats.audioStats` | Object | 远端音频流信息 |
| `stats.videoStats` | Object | 远端视频流信息 |

**audioStats参数说明：**
| 参数名称 | 类型 | 描述 |
|----------|------|------|
| `audioLossRate` | Number | 音频丢包率（范围：[0, 1]） |
| `receivedKBitrate` | Number | 接收码率（单位：kbps） |
| `stallCount` | Number\|undefined | 统计周期内的卡顿次数 |
| `stallDuration` | Number\|undefined | 卡顿时长（单位：ms） |
| `totalRtt` | Number | 全链路往返时延（单位：ms，FireFox浏览器可能不准确） |
| `statsInterval` | Number | 统计间隔（单位：ms） |
| `rtt` | Number | 往返时延（单位：ms） |
| `jitterBufferDelay` | Number | 抖动延迟（单位：ms） |
| `numChannels` | Number | 声道数 |
| `receivedSampleRate` | Number | 接收采样率 |
| `concealedSamples` | Number | 音频丢包补偿(PLC)样点总数 |
| `concealmentEvent` | Number | 音频丢包补偿(PLC)累计次数 |

**videoStats参数说明：**
| 参数名称 | 类型 | 描述 |
|----------|------|------|
| `width` | Number | 视频宽度（单位：px） |
| `height` | Number | 视频高度（单位：px） |
| `videoLossRate` | Number | 视频丢包率（范围：[0, 1]） |
| `receivedKBitrate` | Number | 接收码率（单位：kbps） |
| `decoderOutputFrameRate` | Number | 解码器输出帧率（单位：fps） |
| `stallCount` | Number\|undefined | 统计周期内的卡顿次数 |
| `stallDuration` | Number\|undefined | 卡顿时长（单位：ms） |
| `totalRtt` | Number | 全链路往返时延（单位：ms，FireFox浏览器可能不准确） |
| `isScreen` | Boolean | 是否为屏幕流 |
| `statsInterval` | Number | 统计间隔（单位：ms） |
| `rtt` | Number | 往返时延（单位：ms） |
| `codecType` | String | 视频编码格式（"H264"或"VP8"） |

### onNetworkQuality：网络质量

**功能描述：** 网络质量信息回调，加入房间后每2秒返回一次。

**调用签名：**

```js
onNetworkQuality(uplinkNetworkQuality, downlinkNetworkQuality)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `uplinkNetworkQuality` | Number | 上行网络质量 |
| `downlinkNetworkQuality` | Number | 下行网络质量 |

**网络质量等级：**
| 值 | 说明 |
|----|------|
| `0 (UNKNOWN)` | 网络质量未知 |
| `1 (EXCELLENT)` | 网络质量极好 |
| `2 (GOOD)` | 网络质量良好（与EXCELLENT主观感受相近，但码率可能略低） |
| `3 (POOR)` | 轻微卡顿，但基本不影响沟通 |
| `4 (BAD)` | 通信不流畅，建议降低采样率或切换为纯语音模式 |
| `5 (VBAD)` | 网络极差，基本无法沟通，建议提示用户网络较弱 |
| `6 (DOWN)` | 网络连接断开，无法通话 |

### onProgress：加载进度

**功能描述：** 加载进度相关回调。

**调用签名：**

```js
onProgress({ code, msg })
```

**参数说明：**
| 代码 | 说明 |
|------|------|
| `WS_CONNECT` (100) | WS开始连接 |
| `WS_SUCCESS` (101) | WS连接成功 |
| `WS_CLOSE` (102) | WS连接关闭 |
| `WS_ERROR` (103) | WS连接出错 |
| `OWN_JOIN_ROOM` (200) | 收到加入房间信息 |
| `RECEIVE_OFFER` (201) | 设置offer信息成功 |
| `RECEIVE_OFFER_ERR` (202) | 设置offer信息失败 |
| `SEND_ANSWER` (203) | 发送answer信息 |
| `SEND_ANSWER_ERR` (204) | 发送answer信息失败 |
| `RECEIVE_ICE` (205) | 添加ICE信息成功 |
| `RECEIVE_ICE_ERR` (206) | 添加ICE信息失败 |
| `SEND_ICE` (207) | 发送ICE信息 |
| `RTC_CONNECTING` (300) | RTC正在连接 |
| `RTC_CONNECTED` (301) | RTC连接成功 |
| `RTC_DISCONNECTED` (302) | RTC断开连接 |
| `RTC_CLOSE` (303) | RTC连接关闭 |
| `RTC_FAILED` (304) | RTC连接失败 |
| `RTC_TRACK_VIDEO` (305) | RTC接收VIDEO流 |
| `RTC_TRACK_VIDEO_LOAD` (306) | RTC接收VIDEO流后在VIDEO中加载成功 |
| `RTC_CHANNEL_OPEN` (307) | RTC消息通道连接成功 |
| `RTC_CHANNEL_ERR` (308) | RTC消息通道连接失败 |
| `VIDEO_FIRST_FRAME` (309) | VIDEO加载成功当未收到云机的UI信息 |
| `VIDEO_FIRST_FRAME` (310) | VIDEO第一帧渲染成功 |

## 用户交互回调

### onUserLeaveOrJoin：用户进退房

**功能描述：** 用户加入或离开房间时触发。

**调用签名：**

```js
onUserLeaveOrJoin({ type, userInfo })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `type` | String | 事件类型（`join`: 加入房间, `leave`: 离开房间） |
| `userInfo` | Object | 用户信息 `{userId, extraInfo}` |

### onUserLeave：用户退出

**功能描述：** 远端用户退出房间时触发。

**调用签名：**

```js
onUserLeave(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `userId` | String | 离开房间的用户ID |
| `reason` | Number | 离开原因 |

**离开原因说明：**
| 值 | 说明 |
|----|------|
| `0 (QUIT)` | 主动退出（调用`leaveRoom`） |
| `1 (DROPPED)` | 掉线（如token过期、网络问题） |
| `2 (SWITCH_TO_INVISIBLE)` | 切换为不可见（调用`setUserVisibility`） |
| `3 (KICKED_BY_ADMIN)` | 被管理员踢出 |

### onAutoRecoveryTime：无操作超时

**功能描述：** 无操作时间到期时触发。

**调用签名：**

```js
onAutoRecoveryTime()
```

### onMonitorOperation：操作信息

**功能描述：** 开启监控操作回调的信息信息

**调用签名：**

```js
onMonitorOperation(event)
```

**参数说明：**

| 参数                     | 类型     | 描述                       |
| ---------------------- | ------ | ------------------------ |
| `event.actionTime`     | number | 按键时间    |
| `event.actionType`     | number | 操作类型（如按下、抬起）             |
| `event.keyCode`     | number | 物理按键编号    |
| `event.simulateHeight` | number | 屏幕高度                     |
| `event.simulateWidth`  | number | 屏幕宽度                     |
| `event.swipe`          | number | 滚动距离（`-1` 表示上划，`1` 表示下划） |
| `event.touchType`      | string | 按键类型                     |
| `event.x`              | number | 屏幕点击的 X 坐标               |
| `event.y`              | number | 屏幕点击的 Y 坐标               |


## 功能特性回调

### onTransparentMsg：透传消息

**功能描述：** 收到云端app透传消息时触发。

**调用签名：**

```js
onTransparentMsg(type, msg)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `type` | String | 保留字段（当前默认填`0`） |
| `msg` | String | 消息内容（字符串） |

### onOutputClipper：内容复制

**功能描述：** 在云机复制内容时触发。

**调用签名：**

```js
onOutputClipper(data)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `data.content` | String | 复制的内容 |

### onAdbOutput：ADB命令结果

**功能描述：** 执行ADB命令后返回结果。

**调用签名：**

```js
onAdbOutput(event)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `isSuccess` | Boolean | 是否成功 |
| `content` | String | 成功后的返回结果 |

### onInjectVideoResult：视频注入结果

**功能描述：** 视频注入摄像头的开始或停止操作结果回调。

**调用签名：**

```js
onInjectVideoResult(type, data)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `type` | String | 回调类型（`startVideoInjection`或`stopVideoInjection`） |
| `data.isSuccess` | Boolean | 操作是否成功 |
| `data.content` | String | 操作失败原因 |

### onMediaDevicesToggle：云机内打开或关闭(摄像头/麦克风)

**功能描述：** 云机打开或关闭(摄像头/麦克风)回调。

**调用签名：**

```js
onMediaDevicesToggle(stats)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `stats.type` | String | media：摄像头和麦克风，camera：摄像头 ，microphone：麦克风|
| `stats.enabled` | Boolean | 是否打开 |
| `stats.isFront` | Boolean | 是否前置，注：类型media和camera才有该属性 |


## 错误处理回调

### onGroupControlError：群控错误

**功能描述：** 群控相关错误回调。

**调用签名：**

```js
onGroupControlError({ code, msg })
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `code` | String | 错误码 |
| `msg` | String | 错误信息 |

**错误码说明：**
| 错误码 | 描述 |
|--------|------|
| `TOKEN_ERR` | 获取token接口失败 |
| `INVALID_TOKEN` | token无效或已过期 |
| `JOIN_ROOM_FAILED` | 进房失败 |
| `REPEAT_JOIN` | 重复进房 |
| `ROOM_FORBIDDEN` | 房间被封禁 |
| `USER_FORBIDDEN` | 用户被封禁 |

### onSendUserError：指令发送错误

**功能描述：** 发送远端指令时出现异常。

**调用签名：**

```js
onSendUserError(error)
```

**参数说明：**
| 参数 | 类型 | 描述 |
|------|------|------|
| `error.code` | String | 错误码 |

**错误码说明：**
| 错误码 | 描述 |
|--------|------|
| `USER_MESSAGE_TIMEOUT` | 发送消息超时 |
| `USER_MESSAGE_BROKEN` | 通道断开，发送失败 |
| `USER_MESSAGE_NO_RECEIVER` | 未找到接收者 |
| `USER_MESSAGE_NOT_JOIN` | 发送方未加入房间 |
| `USER_MESSAGE_UNKNOWN` | 未知错误 |

