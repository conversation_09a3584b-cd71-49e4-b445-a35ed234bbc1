---
title:  接口说明
---


## 目录

- [初始化参数](#初始化参数)
- [接口分类](#接口分类)
- [基础接口](#基础接口)
- [音视频控制](#音视频控制)
- [设备控制](#设备控制)
- [输入控制](#输入控制)
- [云手机控制](#云手机控制)
- [群控功能](#群控功能)
- [媒体注入](#媒体注入)

## 初始化参数

```js
const engine = new ArmcloudEngine(params)
```

### 1. 顶层参数

| 参数名              | 类型    | 必填 | 说明                            | 默认值      |
|---------------------|---------|------|--------------------------------|-------------|
| **token**           | string  | ✓   | 服务端身份验证Token             | -           |
| **baseUrl**         | string  | ✓   | SDK接口请求域名                 | -           |
| enableMicrophone    | boolean | ✗   | 是否启用麦克风                  | true        |
| enableCamera        | boolean | ✗   | 是否启用摄像头                  | true        |
| masterIdPrefix      | string  | ✗   | 群控主控设备ID前缀              | ""          |

### 2. 设备信息参数

| 参数名                    | 类型    | 必填 | 说明                              | 默认值      |
|---------------------------|---------|------|----------------------------------|-------------|
| **deviceInfo.padCode**    | string  | ✓   | 房间号/实例编号，标识云手机实例     | -           |
| **deviceInfo.userId**     | string  | ✓   | 用户ID                            | -           |
| deviceInfo.autoRecoveryTime | number  | ✗   | 无操作回收时间(秒)，0表示禁用     | 300         |
| deviceInfo.mediaType      | number  | ✗   | 媒体流类型(1:音频/2:视频/3:音视频) | 2           |
| deviceInfo.rotateType     | number  | ✗   | 屏幕方向(0:竖屏/1:横屏)           | 自动判断    |
| deviceInfo.keyboard       | string  | ✗   | 键盘模式("local"/"pad")          | "pad"       |
| deviceInfo.saveCloudClipboard | boolean | ✗ | 是否启用云机剪切板回调           | true        |
| deviceInfo.allowLocalIMEInCloud | boolean | ✗ | 云机键盘时能否使用本地输入法     | false       |
| deviceInfo.disableContextMenu | boolean | ✗ | 是否禁用右键菜单                 | false       |
| deviceInfo.videoDeviceId  | string  | ✗   | 指定摄像头设备ID                  | 自动选择    |
| deviceInfo.audioDeviceId  | string  | ✗   | 指定麦克风设备ID                  | 自动选择    |
| deviceInfo.disableLocalIME | boolean | ✗ | 禁用本地键盘                 | false       |

### 3. 视频流参数

| 参数名                          | 类型   | 必填 | 说明       | 默认值 |
|---------------------------------|--------|------|-----------|--------|
| deviceInfo.videoStream.resolution | number | ✗   | 视频分辨率 | 12     |
| deviceInfo.videoStream.frameRate  | number | ✗   | 视频帧率   | 2      |
| deviceInfo.videoStream.bitrate    | number | ✗   | 视频码率   | 3      |

### 视频质量预设

| 质量等级 | 分辨率ID | 帧率ID | 码率ID |
|----------|----------|--------|--------|
| 流畅     | 9        | 8      | 15     |
| 标清     | 10       | 8      | 15     |
| 高清     | 12       | 8      | 1      |
| 超清     | 15       | 1      | 3      |

> **注意:** 详细配置参数请参阅[设置分辨率、码率、帧率](#设置分辨率码率帧率)部分

## 接口分类

### 基础接口

- [isSupported](#浏览器兼容性检测) - 检测浏览器是否支持RTC服务
- [start](#加入房间) - 加入房间
- [stop](#离开房间) - 离开房间

### 音视频控制

- [setStreamConfig](#设置分辨率码率帧率) - 设置视频参数
- [muted/unmuted](#静音与取消静音) - 音频控制
- [increaseVolume/decreaseVolume](#音量控制) - 音量控制
- [resumeAllSubscribedStream](#恢复推流) - 恢复推流
- [pauseAllSubscribedStream](#暂停推流) - 暂停推流

### 设备控制

- [setMicrophone](#麦克风控制) - 麦克风控制
- [setCamera](#摄像头控制) - 摄像头控制
- [setVideoDeviceId](#指定设备) - 指定摄像头
- [setAudioDeviceId](#指定设备) - 指定麦克风
- [setMonitorOperation](#是否开启操作监控) - 是否开启操作监控

### 输入控制

- [setKeyboardStyle](#键盘输入控制) - 切换输入法类型
- [sendCommand](#按键控制) - 发送按键指令
- [sendInputString](#文本输入) - 发送文本到输入框
- [sendInputClipper](#剪贴板控制) - 发送文本到剪贴板
- [saveCloudClipboard](#剪贴板控制) - 控制剪贴板回调
- [triggerClickEvent](#触控模拟) - 模拟点击
- [triggerPointerEvent](#触控模拟) - 模拟触摸

### 云手机控制

- [setGPS](#位置模拟) - 设置GPS位置
- [sendShake](#设备控制) - 发送摇一摇指令
- [setAutoRecycleTime/getAutoRecycleTime](#回收控制) - 管理回收时间
- [setPhoneRotation](#屏幕方向) - 设置横竖屏
- [executeAdbCommand](#高级控制) - 执行ADB命令
- [saveScreenShotToLocal/saveScreenShotToRemote](#截图功能) - 截图功能
- [setScreenResolution](#屏幕分辨率和dpi) - 设置和恢复手机分辨率和DPI

### 群控功能

- [joinGroupRoom](#群控加入房间) - 群控加入房间
- [kickItOutRoom](#踢出群控房间) - 踢出群控房间

### 媒体注入

- [injectVideoStream](#视频注入) - 视频注入控制
- [startMediaStream/stopMediaStream](#摄像头麦克风注入) - 摄像头/麦克风注入
- [getInjectStreamStatus](#注入状态查询) - 获取注入状态

## 基础接口

### 浏览器兼容性检测

检测当前浏览器是否支持RTC服务。

**方法定义**

```js
engine.isSupported()
```

**返回值**

`Promise<boolean>` - 返回当前浏览器是否支持RTC服务

**示例代码**

```js
const isSupported = await engine.isSupported();
if (!isSupported) {
  console.warn("当前浏览器不支持RTC服务");
  return false;
}
```

### 加入房间

连接云手机，开始远程操控。

**方法定义**

```js
engine.start(isGroupControl, pads)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| isGroupControl | boolean | ✗ | 是否开启群控模式 | false |
| pads | string[] | ✗ | 需要被群控的实例编号数组 | [] |

**示例代码**

```js
// 普通模式连接
engine.start(false, []);

// 群控模式连接
engine.start(true, ["ACXXXX", "ACXXXX"]);
```

### 离开房间

断开连接，释放资源。

**方法定义**

```js
engine.stop()
```

**返回值**

`Promise<void>` - 返回停止操作的结果

**示例代码**

```js
try {
  await engine.stop();
  console.log("已断开连接");
} catch (error) {
  console.error("断开连接失败:", error);
}
```

> **重要提示:** `stop()` 方法会销毁引擎及创建的DOM节点。如需切换云机，请在每次 `new ArmcloudEngine()` 之前先调用 `stop()`。

## 音视频控制

### 设置分辨率码率帧率

调整视频流参数以满足不同网络环境和画质需求。

**方法定义**

```typescript
engine.setStreamConfig(definitionConfig: {
  definitionId: number;  // 分辨率ID
  framerateId: number;   // 帧率ID
  bitrateId: number;     // 码率ID
},forwardOff: boolean)
```

**参数说明**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| definitionConfig | Object | ✓ | 视频流配置对象 |
| definitionConfig.definitionId | number | ✓ | 分辨率ID，控制视频的清晰度 |
| definitionConfig.framerateId | number | ✓ | 帧率ID，控制视频的流畅度 |
| definitionConfig.bitrateId | number | ✓ | 码率ID，控制视频的数据量 |
| forwardOff | boolead | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 true |

**分辨率参数值 (definitionId)**

| ID | 分辨率 | 适用场景 |
|----|--------|----------|
| 7  | 144×256 | 极低带宽环境 |
| 8  | 216×384 | 低带宽环境 |
| 9  | 288×512 | 流畅模式 |
| 10 | 360×640 | 标清模式 |
| 11 | 480×848 | 增强标清 |
| 12 | 540×960 | 高清模式 |
| 13 | 600×1024 | 增强高清 |
| 14 | 480×1280 | 宽屏模式 |
| 15 | 720×1280 | 超清模式 |
| 16 | 720×1920 | 宽屏超清 |
| 17 | 1080×1920 | 全高清 |
| 18 | 1440×1920 | 2K画质 |
| 19 | 1600×2560 | 超高清 |
| 20 | 2880×1080 | 超宽视野 |

**帧率参数值 (framerateId)**

| ID | 帧率 | 适用场景 |
|----|------|----------|
| 1  | 20fps | 标准流畅度 |
| 2  | 25fps | 增强流畅度 |
| 3  | 30fps | 高流畅度 |
| 4  | 60fps | 极致流畅度，适合快速移动场景 |
| 5  | 1fps | 最低网络占用，静态场景 |
| 6  | 5fps | 低网络占用 |
| 7  | 10fps | 基础流畅度 |
| 8  | 15fps | 正常流畅度 |
| 9  | 2fps | 超低网络占用 |

**码率参数值 (bitrateId)**

| ID | 码率 | 适用场景 |
|----|------|----------|
| 1  | 1Mbps | 高清模式基础码率 |
| 2  | 1.5Mbps | 高清模式增强码率 |
| 3  | 2Mbps | 超清模式基础码率 |
| 4  | 2.5Mbps | 超清模式增强码率 |
| 5  | 3Mbps | 超清模式高级码率 |
| 6  | 3.5Mbps | 全高清基础码率 |
| 7  | 4Mbps | 全高清标准码率 |
| 8  | 5Mbps | 全高清增强码率 |
| 9  | 6Mbps | 2K基础码率 |
| 10 | 8Mbps | 2K标准码率 |
| 11 | 10Mbps | 2K增强码率 |
| 12 | 12Mbps | 超高清码率 |
| 13 | 200kbps | 极低带宽环境 |
| 14 | 400kbps | 低带宽环境 |
| 15 | 600kbps | 流畅模式基础码率 |

**常用画质预设**

| 使用场景 | 推荐配置 |
|----------|----------|
| 省流量模式 | `{ definitionId: 9, framerateId: 8, bitrateId: 15 }` |
| 均衡模式 | `{ definitionId: 10, framerateId: 8, bitrateId: 15 }` |
| 高清模式 | `{ definitionId: 12, framerateId: 8, bitrateId: 1 }` |
| 超清模式 | `{ definitionId: 15, framerateId: 1, bitrateId: 3 }` |
| 2K画质 | `{ definitionId: 18, framerateId: 3, bitrateId: 10 }` |

**示例代码**

```js
// 设置高清模式
engine.setStreamConfig({
  definitionId: 12,  // 540×960
  framerateId: 2,    // 25fps
  bitrateId: 3       // 2Mbps
});

// 设置流畅模式（低带宽环境）
engine.setStreamConfig({
  definitionId: 9,   // 288×512
  framerateId: 8,    // 15fps
  bitrateId: 15      // 600kbps
});

// 设置超清模式（高带宽环境）
engine.setStreamConfig({
  definitionId: 15,  // 720×1280
  framerateId: 3,    // 30fps
  bitrateId: 5       // 3Mbps
});
```

### 恢复推流

恢复音视频流传输。

**方法定义**

```js
engine.resumeAllSubscribedStream(mediaType)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mediaType | number | ✓ | 媒体类型：1(音频), 2(视频), 3(音视频) |

**示例代码**

```js
// 恢复音视频流
engine.resumeAllSubscribedStream(3);

// 只恢复音频
engine.resumeAllSubscribedStream(1);

// 只恢复视频
engine.resumeAllSubscribedStream(2);
```

### 暂停推流

暂停音视频流传输，节省带宽。

**方法定义**

```js
engine.pauseAllSubscribedStream(mediaType)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mediaType | number | ✓ | 媒体类型：1(音频), 2(视频), 3(音视频) |

**示例代码**

```js
// 暂停音视频流
engine.pauseAllSubscribedStream(3);

// 只暂停音频
engine.pauseAllSubscribedStream(1);

// 只暂停视频流
engine.pauseAllSubscribedStream(2);
```

### 静音与取消静音

控制云手机的音频输出。

**方法定义**

```js
// 开启静音
engine.muted()

// 关闭静音
engine.unmuted()
```

**返回值**

无

**示例代码**

```js
// 开启静音
engine.muted();

// 关闭静音
engine.unmuted();
```

### 音量控制

调整云手机的音量大小。

**方法定义**

```js
// 增加音量
engine.increaseVolume()

// 减小音量
engine.decreaseVolume()
```

**返回值**

无

**示例代码**

```js
// 调高音量
engine.increaseVolume();

// 调低音量
engine.decreaseVolume();
```

## 设备控制

### 麦克风控制

控制本地麦克风是否向云手机传输音频。

**方法定义**

```js
engine.setMicrophone(enable)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enable | boolean | ✓ | true表示启用麦克风，false表示禁用麦克风 |

**示例代码**

```js
// 启用麦克风
engine.setMicrophone(true);

// 禁用麦克风
engine.setMicrophone(false);
```

### 摄像头控制

控制本地摄像头是否向云手机传输视频。

**方法定义**

```js
engine.setCamera(enable)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enable | boolean | ✓ | true表示启用摄像头，false表示禁用摄像头 |

**示例代码**

```js
// 启用摄像头
engine.setCamera(true);

// 禁用摄像头
engine.setCamera(false);
```

### 指定设备

指定使用的摄像头或麦克风设备。

**方法定义**

```js
// 指定摄像头
engine.setVideoDeviceId(deviceId)

// 指定麦克风
engine.setAudioDeviceId(deviceId)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deviceId | string | ✓ | 设备ID，空字符串表示自动选择设备 |

**示例代码**

```js
// 指定摄像头
engine.setVideoDeviceId('ab484e060c5f1186004457c5be97e874e976983ebf66e1227e4e925523fc4f11');

// 指定麦克风
engine.setAudioDeviceId('ab484e060c5f1186004457c5be97e874e976983ebf66e1227e4e925523fc4f11');

// 自动选择设备
engine.setVideoDeviceId('');
engine.setAudioDeviceId('');
```

### 是否开启操作监控

是否开启操作监控。

**方法定义**

```js
engine.setMonitorOperation(enable:boolean, forwardOff:boolean)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| enable | boolean | ✓ | true表示启用操作监控，false表示禁用操作监控 |
| forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 true |

**示例代码**

```js
// 启用操作监控
engine.setMonitorOperation(true);

// 禁用操作监控
engine.setMonitorOperation(false);
```

## 输入控制

### 键盘输入控制

切换输入法类型。

**方法定义**

```js
engine.setKeyboardStyle(keyboard)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyboard | string | ✓ | 键盘模式："local"(本地输入法), "pad"(云机虚拟输入法) |

**示例代码**

```js
// 使用本地输入法
engine.setKeyboardStyle('local');

// 使用云机虚拟输入法
engine.setKeyboardStyle('pad');
```

### 按键控制

发送系统按键指令。

**方法定义**

```js
engine.sendCommand(command：string, forwardOff: boolean)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| command | string | ✓ | 按键命令："back"(返回键), "home"(Home键), "menu"(菜单键)，[其它参考 android.view.KeyEvent 官方文档](https://developer.android.com/reference/android/view/KeyEvent#KEYCODE_ENTER) |
| forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 false |

**示例代码**

```js
// 发送返回键
engine.sendCommand("back");

// 发送Home键
engine.sendCommand("home");

// 发送菜单键
engine.sendCommand("menu");

// 发送回车
engine.sendCommand("66");
```

### 文本输入

将文本发送到云手机的输入框。

**方法定义**

```js
engine.sendInputString(text, forwardOff)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| text | string | ✓ | 要输入的文本内容 |
| forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 false |


**示例代码**

```js
// 在聚焦的输入框中输入文本
engine.sendInputString('hello world');
```

> **注意:** 此操作仅将文本发送至输入框，不会更新云机剪切板内容。

### 剪贴板控制

管理云手机剪贴板内容。

**方法定义**

```js
// 发送文本到云手机剪贴板
engine.sendInputClipper(text, forwardOff)

// 控制是否接收云机剪贴板内容回调
engine.saveCloudClipboard(flag)
```

**参数**

| 方法 | 参数名 | 类型 | 必填 | 说明 |
|------|--------|------|------|------|
| sendInputClipper | text | string | ✓ | 要发送到剪贴板的文本内容 |
| sendInputClipper | forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 false |
| saveCloudClipboard | flag | boolean | ✓ | true表示接收剪贴板回调，false表示不接收 |

**示例代码**

```js
// 发送文本到云机剪贴板，需要在云机输入框长按粘贴才能使用
engine.sendInputClipper('hello world');

// 开启云机剪贴板内容回调
engine.saveCloudClipboard(true);

// 关闭云机剪贴板内容回调
engine.saveCloudClipboard(false);
```

### 触控模拟

模拟触摸和点击事件。

**方法定义**

```typescript
// 模拟点击
engine.triggerClickEvent(options: {
  x: number;       // 点击的x坐标
  y: number;       // 点击的y坐标
  width: number;   // 容器宽度
  height: number;  // 容器高度
})

// 模拟触摸
engine.triggerPointerEvent(
  action: number,  // 触摸动作类型
  options: {
    x: number;       // 触摸的x坐标
    y: number;       // 触摸的y坐标
    width: number;   // 容器宽度
    height: number;  // 容器高度
  }
)
```

**参数说明**

| 方法 | 参数名 | 类型 | 必填 | 说明 |
|------|--------|------|------|------|
| triggerClickEvent | options | Object | ✓ | 点击事件配置对象 |
| triggerClickEvent | options.x | number | ✓ | 点击的横坐标位置 |
| triggerClickEvent | options.y | number | ✓ | 点击的纵坐标位置 |
| triggerClickEvent | options.width | number | ✓ | 视频容器的宽度 |
| triggerClickEvent | options.height | number | ✓ | 视频容器的高度 |
| triggerPointerEvent | action | number | ✓ | 触摸动作类型：0(按下), 1(抬起), 2(移动中) |
| triggerPointerEvent | options | Object | ✓ | 触摸事件配置对象 |
| triggerPointerEvent | options.x | number | ✓ | 触摸的横坐标位置 |
| triggerPointerEvent | options.y | number | ✓ | 触摸的纵坐标位置 |
| triggerPointerEvent | options.width | number | ✓ | 视频容器的宽度 |
| triggerPointerEvent | options.height | number | ✓ | 视频容器的高度 |

> **坐标计算说明:** 坐标值(x,y)是相对于视频容器的位置，计算公式为：
>
> - 实际X坐标 = x / width * 实际视频宽度
> - 实际Y坐标 = y / height * 实际视频高度

**示例代码**

```js
// 模拟单击
engine.triggerClickEvent({
  x: 430,      // 点击位置的X坐标
  y: 1383,     // 点击位置的Y坐标
  width: 871,  // 容器宽度
  height: 1550 // 容器高度
});

// 模拟滑动操作
// 1. 按下
engine.triggerPointerEvent(0, {
  x: 92,       // 起始位置X坐标
  y: 1325,     // 起始位置Y坐标
  width: 871,  // 容器宽度
  height: 1550 // 容器高度
});

// 2. 移动过程
engine.triggerPointerEvent(2, {
  x: 92,       // 中间位置X坐标(横向不变)
  y: 1326,     // 中间位置Y坐标(微小变化)
  width: 871,  // 容器宽度
  height: 1550 // 容器高度
});

// 3. 抬起
setTimeout(() => {
  engine.triggerPointerEvent(1, {
    x: 92,       // 结束位置X坐标
    y: 1330,     // 结束位置Y坐标(纵向已移动)
    width: 871,  // 容器宽度
    height: 1550 // 容器高度
  });
}, 1);

// 模拟长按操作
// 1. 按下
engine.triggerPointerEvent(0, {
  x: 300,
  y: 500,
  width: 871,
  height: 1550
});

// 2. 延迟后抬起(模拟长按效果)
setTimeout(() => {
  engine.triggerPointerEvent(1, {
    x: 300,
    y: 500,
    width: 871,
    height: 1550
  });
}, 800); // 长按800毫秒
```

## 云手机控制

### 位置模拟

设置云手机的GPS位置信息。

**方法定义**

```js
engine.setGPS(longitude, latitude)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| longitude | number | ✓ | 经度值 |
| latitude | number | ✓ | 纬度值 |

**示例代码**

```js
// 设置经度113，纬度28
engine.setGPS(113, 28);
```

### 设备控制

发送设备特定指令。

**方法定义**

```js
engine.sendShake(time)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| time | number | ✗ | 摇一摇持续时间(毫秒) | 1500 |

**示例代码**

```js
// 默认持续1.5秒
engine.sendShake();

// 持续2秒
engine.sendShake(2000);
```

> **注意:** 时间过长可能导致多次截屏。

### 回收控制

管理云手机的无操作自动回收时间。

**方法定义**

```js
// 设置回收时间
engine.setAutoRecycleTime(second)

// 获取回收时间
engine.getAutoRecycleTime()
```

**参数**

| 方法 | 参数名 | 类型 | 必填 | 说明 |
|------|--------|------|------|------|
| setAutoRecycleTime | second | number | ✓ | 自动回收时间(秒)，0~7200，0表示禁用自动回收 |

**返回值**

| 方法 | 返回类型 | 说明 |
|------|----------|------|
| getAutoRecycleTime | Promise\<number\> | 当前设置的自动回收时间(秒) |

**示例代码**

```js
// 设置5分钟无操作回收
engine.setAutoRecycleTime(300);

// 禁用自动回收
engine.setAutoRecycleTime(0);

// 获取当前回收时间
engine.getAutoRecycleTime().then(time => {
  console.log(`当前无操作回收时间: ${time}秒`);
});
```

### 屏幕方向

设置云手机的屏幕方向。

**方法定义**

```js
engine.setPhoneRotation(type)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | number | ✓ | 屏幕方向：0(竖屏), 1(横屏) |

**示例代码**

```js
// 设置竖屏
engine.setPhoneRotation(0);

// 设置横屏
engine.setPhoneRotation(1);
```

### 高级控制

执行ADB命令。

**方法定义**

```js
engine.executeAdbCommand(command, forwardOff)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| command | string | ✓ | 要执行的ADB命令 |
| forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 true |

**示例代码**

```js
// 列出根目录文件
engine.executeAdbCommand('cd /;ls');
```

### 截图功能

捕获云手机屏幕截图。

**方法定义**

```js
// 截图保存到本地
engine.saveScreenShotToLocal()

// 截图保存到云机
engine.saveScreenShotToRemote()
```

**返回值**

| 方法 | 返回类型 | 说明 |
|------|----------|------|
| saveScreenShotToLocal | Promise\<ImageData\> | 截图的图像数据 |
| saveScreenShotToRemote | void | 无返回值 |

**示例代码**

```js
// 截图保存到本地
engine.saveScreenShotToLocal().then(imageData => {
  // 创建canvas元素
  const canvas = document.createElement("canvas");
  canvas.width = imageData.width;
  canvas.height = imageData.height;
  const ctx = canvas.getContext("2d");
  
  // 将图像数据绘制到canvas上
  ctx.putImageData(imageData, 0, 0);
  
  // 保存图片
  const link = document.createElement("a");
  link.href = canvas.toDataURL();
  link.download = "screenshot.png";
  link.click();
});

// 截图保存到云机相册
engine.saveScreenShotToRemote();
```

### 屏幕分辨率和DPI

修改和还原屏幕分辨率和DPI

**方法定义**

```js
engine.setScreenResolution(options, forwardOff)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| options.width | number | ✗ | 宽度 |
| options.height | number | ✗ | 高度 |
| options.dpi | number | ✗ | DPI |
| options.type | string | ✓ | 类型：<br>1. updateDensity(更新分辨率和DPI) <br>2. resetDensity(恢复默认分辨率和DPI，此类型的情况下options的其它参数无效) |
| forwardOff | boolean | ✗ | 是否关闭转发到群控房间，开启群控才生效，默认 true |

**示例代码**

```js
// 修改分辨率和DPI
engine.setScreenResolution({
  width: 720,
  height: 1280,
  dpi: 360,
  type: 'updateDensity'
});

// 恢复默认分辨率和DPI
engine.setScreenResolution({
  type: 'resetDensity'
});
```

## 群控功能

管理多台云手机的群控操作。

### 群控加入房间

**方法定义**

```js
engine.joinGroupRoom(pads)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pads | string[] | ✓ | 需要被群控的实例编号数组 |

**示例代码**

```js
// 加入多个实例到群控房间
engine.joinGroupRoom(['ACXXXX', 'ACXXXX']);
```

### 踢出群控房间

**方法定义**

```js
engine.kickItOutRoom(pads)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pads | string[] | ✓ | 需要踢出群控的实例编号数组 |

**示例代码**

```js
// 踢出指定实例
engine.kickItOutRoom(['ACXXXX']);
```

## 媒体注入

### 视频注入

将视频注入到云手机相机。

**方法定义**

```typescript
engine.injectVideoStream(
  type: "startVideoInjection" | "stopVideoInjection",
  options?: {
    fileUrl: string;    // 视频文件路径
    isLoop?: boolean;   // 是否循环播放
    fileName?: string;  // 视频文件名称
  }
)
```

**参数说明**

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| type | string | ✓ | 操作类型："startVideoInjection"(开始注入), "stopVideoInjection"(停止注入) | - |
| options | Object | type为"startVideoInjection"时必填 | 注入配置项 | - |
| options.fileUrl | string | ✓ | 视频文件路径，支持云手机内部路径或外部URL地址 | - |
| options.isLoop | boolean | ✗ | 是否循环播放视频 | true |
| options.fileName | string | fileUrl为URL时必填 | 视频文件名称 | - |

**技术要求**

| 项目 | 要求 |
|------|------|
| 支持的视频格式 | H.264、H.265编码 |
| 视频分辨率 | 宽高需为2的倍数 |

**示例代码**

```js
// 从URL注入视频
engine.injectVideoStream('startVideoInjection', {
  fileUrl: 'http://example.com/video.mp4',
  isLoop: true,
  fileName: 'video.mp4'  // URL地址时必填
});

// 从云手机内部存储注入视频
engine.injectVideoStream('startVideoInjection', {
  fileUrl: '/sdcard/Downloads/video.mp4',
  isLoop: true
});

// 从云手机内部存储注入视频(单次播放)
engine.injectVideoStream('startVideoInjection', {
  fileUrl: '/sdcard/Downloads/video.mp4',
  isLoop: false
});

// 停止视频注入
engine.injectVideoStream('stopVideoInjection');
```

### 摄像头麦克风注入

将本地摄像头和麦克风注入到云手机。

**方法定义**

```js
// 启动注入
engine.startMediaStream(mediaType)

// 停止注入
engine.stopMediaStream(mediaType)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mediaType | number | ✓ | 媒体类型：1(麦克风), 2(摄像头), 3(摄像头和麦克风) |

**示例代码**

```js
// 启动摄像头和麦克风注入
engine.startMediaStream(3);

// 只启动麦克风注入
engine.startMediaStream(1);

// 只启动摄像头注入
engine.startMediaStream(2);

// 停止摄像头注入
engine.stopMediaStream(2);

// 停止麦克风注入
engine.stopMediaStream(1);

// 停止所有注入
engine.stopMediaStream(3);
```

### 注入状态查询

获取媒体注入状态。

**方法定义**

```js
engine.getInjectStreamStatus(type, timeout)
```

**参数**

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| type | string | ✓ | 查询类型："video"(视频注入), "camera"(摄像头), "audio"(麦克风) | - |
| timeout | number | ✗ | 超时时间(毫秒)，0表示不设置超时 | 0 |

**返回值**

返回一个Promise，解析为包含以下属性的对象：

| 属性名 | 类型 | 说明 |
|--------|------|------|
| type | string | 注入类型："video", "camera", "audio" |
| status | string | 注入状态："live"(推流中), "offline"(未推流), "unknown"(未知), "timeout"(超时) |
| path | string | 推流路径，仅type为"video"时存在 |

**示例代码**

```js
// 查询视频注入状态
engine.getInjectStreamStatus("video").then(res => {
  console.log(`类型: ${res.type}`);  // 注入类型
  console.log(`状态: ${res.status}`); // 注入状态
  if (res.path) {
    console.log(`路径: ${res.path}`);  // 仅视频注入时有此字段
  }
});

// 查询摄像头注入状态，5秒超时
engine.getInjectStreamStatus("camera", 5000).then(res => {
  console.log(`类型: ${res.type}`);  // 注入类型
  console.log(`状态: ${res.status}`); // 注入状态
});
```
