---
title: 示例搭建
order: 1
---

## 环境要求

```text
    Android 5.0 或以上版本的 Android 真机或模拟器
    IDE：Android Studio（推荐使用最新版本）
    JDK 版本需要1.8+
```

## 添加 Maven 仓库地址

1.在 Project 根目录下的 build.gradle 文件中的 repositories 中配置了 maven 仓库地址，参考以下示例：

```groovy
    repositories {
          maven {
            url "https://maven.vmos.cn"
        }
    }
}
```

2.在项目 build.gradle 文件中引入 ArmCloudSDK：

```groovy
implementation "net.armcloud.armcloudsdk:armcloudsdkv3:1.1.2"
```

3.设置 Java 版本到1.8，参考以下示例：

```groovy
android {
    // ...
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
```

## 权限声明

根据实际场景在 AndroidManifest.xml 文件中声明 SDK 需要的权限，参考以下示例：

```xml
    <!--用于访问网络，网络定位需要上网-->
    <uses-permission android:name="android.permission.INTERNET"/>
    <!--WiFi网络状态，使用场景：用户手机网络状态变化监听-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <!--用于采集音频信息-->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <!--用于采集视频信息-->
    <uses-permission android:name="android.permission.CAMERA" />
 <!--用于震动控制-->
    <uses-permission android:name="android.permission.VIBRATE" />
 <!--文件读写权限 用于上传文件-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--文件管理权限(Android 11 (API level 30) 及以上) 用于上传文件-->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
```

## 快速开始

#### 初始化

调用 prepare 方法，初始化 ArmCloudEngine：

```java
        ArmCloudEngine.prepare(this, object : ICloudCoreManagerStatusListener {
            override fun onPrepared() {
               Log.i("ArmCloudEngine", "初始化完成")
            }
        })
```

#### 配置 PhonePlayConfig

配置启动云手机需要的参数：

```java
        val builder: PhonePlayConfig.Builder = PhonePlayConfig.Builder()
        
        builder.context(this)
            .userId(dto.userId) //必填参数 自定义客户端用户 ID
            .padCode(dto.padCode) // 必填参数, 云手机实例 ID
            .token(dto.token) // 必填参数，临时鉴权 token
            .clientType(dto.clientType) //必填参数 客户端类型
            .container(container) // 必填参数，用来承载画面的 Container
            .enableMultiControl(isMultiControl) // 选填参数 是否开启群控
            .setPadCodes(mChosePadCodes) // 选填参数 群控设备号集合
            .rotation(rotation) // 选填参数 屏幕的横竖屏 默认竖屏
            .videoStreamProfileId(videoStreamProfileId) // 选填参数，清晰度档位ID 默认高清
            .enableGyroscopeSensor(enableGyroscopeSensor) // 选填参数 打开陀螺仪开关 默认false
            .enableVibrator(enableVibrator) // 选填参数 打开本地振动开关 默认false
            .enableLocationService(enableLocationService) // 选填参数 打开本地定位功能开关 默认false
            .enableLocalKeyboard(enableLocalKeyboard) // 选填参数 打开本地键盘开关 默认false
            .enableClipboardCloudPhoneSync(isEnable) // 选填参数 打开云机剪切板同步至真机 默认true
            .enableClipboardLocalPhoneSync(isEnable) // 选填参数 打开真机剪切板同步至云机 默认true
            .enableCamera(isEnable) // 选填参数 开启相机权限 默认 true
            .enableMic(isEnable) // 选填参数 开启麦克风权限 默认 false
            .streamType(streamType) // 选填参数 指定启动云手机时拉取音视频流类型 默认拉取音视频流
            .videoRenderMode(renderMode) // 选填参数 指定视频流渲染模式 默认等比缩放居中模式
            .videoRotationMode(videoRotationMode) // 选填参数 指定视频旋转模式 默认非SDK处理旋转
            .autoRecycleTime(autoRecycleTime) // 选填参数 指定无操作回收时间 单位s 默认300s

        val phonePlayConfig: PhonePlayConfig = builder.build()
```

#### 启动云手机

调用 start 接口，启动云手机：

```java
ArmCloudEngine.start(phonePlayConfig, IPlayerListener playerListener)
```
