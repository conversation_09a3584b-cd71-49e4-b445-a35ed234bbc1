---
title: 接口说明
---

# API接口说明

本文档描述了云手机 Android 客户端 SDK 提供的接口。<br>
你也可以参考[示例项目](/ArmCloudSdkDemo_android.zip)，了解更完整的项目实现。

## API概览

### 初始化

|  方法名 | 方法描述  |
| ------------ | ------------ |
|  [prepare](./android-sdk.md#初始化ArmCloudEngine) | 初始化ArmCloudEngine  |

### 状态

|  变量名 | 变量描述  |
| ------------ | ------------ |
|  [engineState](./android-sdk.md#获取引擎状态) | 获取当前引擎状态  |

### 生命周期

| 方法名  |  方法描述 |
| ------------ | ------------ |
| [start](./android-sdk.md#启动云机)  | 启动云机  |
| [resume](./android-sdk.md#恢复从云机拉流) | 恢复从云机拉流  |
| [pause](./android-sdk.md#暂停从云机拉流)  | 暂停从云机拉流  |
| [stop](./android-sdk.md#停止云机)   | 停止云机  |
| [onRequestPermissionsResult](./android-sdk.md#权限处理) | 权限自动处理 |

### 功能控制

| 方法名  | 方法描述 |
| ------------ | ------------ |
|  [screenShot](./android-sdk.md#截图) |  截图  |
|  [videoStreamProfileId](./android-sdk.md#设置云机清晰度) | 设置云机清晰度  |
|  [rotate](./android-sdk.md#旋转本机屏幕方向) | 旋转本机屏幕方向  |
|  [muteAudio](./android-sdk.md#是否禁用音频) | 是否禁用音频  |
|  [muteVideo](./android-sdk.md#是否禁用视频) | 是否禁用视频  |
|  [enableLocalKeyboard](./android-sdk.md#是否开启本地键盘) | 是否开启本地键盘  |
|  [enableLocationService](./android-sdk.md#是否启用定位功能) | 是否启用定位功能  |
|  [enableGyroscopeSensor](./android-sdk.md#是否启用传感器功能) | 是否启用陀螺仪功能  |
|  [enableAccelerometerSensor](./android-sdk.md#是否启用重力感应功能) | 是否启用重力感应功能  |
|  [enableVibrator](./android-sdk.md#是否启用震动功能) | 是否启用震动功能  |
|  [enableCamera](./android-sdk.md#相机权限) | 相机权限 |
|  [enableMic](./android-sdk.md#麦克风权限) | 麦克风权限 |
|  [setLocationServiceMode](./android-sdk.md#设置定位模式) | 设置定位模式  |
|  [getLocationServer](./android-sdk.md#获取位置服务) | 获取位置服务  |
|  [getClipBoardServiceManager](./android-sdk.md#获取剪切板控制器) | 获取剪切板控制器  |
|  [switchCamera](./android-sdk.md#切换前后摄像头) | 切换前后摄像头  |
|  [sendKeyEvent](./android-sdk.md#发送模拟按键事件) | 发送模拟按键事件 |
|  [autoRecycleTime](./android-sdk.md#设置无操作回收服务时间) | 设置无操作回收服务时间 默认300s  |
|  [getAutoRecycleTime](./android-sdk.md#获取无操作回收服务时间) | 获取无操作回收服务时间  |
|  [streamType](./android-sdk.md#设置拉流类型) | 设置拉流类型： 拉视频/音频  |
|  [updateVideoRenderMode](./android-sdk.md#更新视频流渲染模式) | 更新视频流渲染模式  |
|  [setVideoRotationMode](./android-sdk.md#设置视频旋转模式) | 设置视频旋转模式  |
|  [getStatus](./android-sdk.md#获取当前播放状态) | 获取当前播放状态  |
|  [getMessageChannel](./android-sdk.md#获取消息透传控制器) | 获取消息透传控制器  |
|  [getUploadManager](./android-sdk.md#获取文件上传控制器) | 获取文件上传控制器  |
|  [getLocalInputManager](./android-sdk.md#获取本地键盘与云机应用控制器) | 获取本地键盘与云机应用控制器 |
|  [startCaptureVideo](./android-sdk.md#开始本地视频采集) | 开始本地视频采集 |
|  [startCaptureAudio](./android-sdk.md#开始本地音频采集) | 开始本地音频采集 |
|  [stopCaptureVideo](./android-sdk.md#停止本地视频采集) | 停止本地视频采集 |
|  [stopCaptureAudio](./android-sdk.md#停止本地音频采集) | 停止本地音频采集 |
|  [startInjectVideoStream](./android-sdk.md#开始视频注入) | 开始视频注入 |
|  [stopInjectVideoStream](./android-sdk.md#停止视频注入) | 停止视频注入 |
|  [getVideoInjectState](./android-sdk.md#获取视频注入状态) | 获取视频注入状态 |


### 设置监听

| 方法名                                                             | 方法描述             |
|-----------------------------------------------------------------|------------------|
| [setStreamProfileChangeListener](./android-sdk.md#设置清晰度切换回调监听)  | 设置清晰度切换回调监听      |
| [setAutoRecycleTimeCallback](./android-sdk.md#设置无操作回收服务时长的回调监听) | 设置无操作回收服务时长的回调监听 |
| [setScreenShotCallBack](./android-sdk.md#设置本地截图回调监听)            | 设置本地截图回调监听       |
| [setLocationEventListener](./android-sdk.md#设置定位回调监听)           | 设置定位回调监听         |
| [setExecuteAdbCommandCallback](./android-sdk.md#设置执行adb指令结果回调监听)  | 设置执行adb指令结果回调监听    |
| [setInjectVideoListener](./android-sdk.md#设置视频注入回调监听)  | 设置视频注入回调监听    |

## API详情

### 初始化

#### 初始化ArmCloudEngine

**方法：**

```kotlin
ArmCloudEngine.prepare(mContext: Application, cloudCoreManagerStatusListener: ICloudCoreManagerStatusListener)
```

**参数：**
mContext ：应用的上下文对象
cloudCoreManagerStatusListener： 初始化回调

**示例代码：**

```kotlin
class MyApp : Application() {

 override fun onCreate() {
  super.onCreate()
  ArmCloudEngine.prepare(this, object : ICloudCoreManagerStatusListener {
   override fun onPrepared() {
    Log.i("ArmCloudEngine", "初始化完成")
   }
  })
 }
}
```

### 状态

#### 获取引擎状态

**变量名：**

```kotlin
ArmCloudEngine.engineState
```

**变量描述：**
类型： int@EngineState
描述： 当前引擎状态
EngineState :

|  字段名 |  描述 |
| ------------ | ------------ |
| STATE_AVAILABLE  | 引擎可用 |
| STATE_UN_AVAILABLE  | 引擎不可用  |

注: 在调用start方法之前，请保证引擎状态为可用

### 生命周期

#### 启动云机

**方法：**

```kotlin
ArmCloudEngine.start(config PhonePlayConfig, playerListener IPlayerListener）
```

**参数：**
config ：初始配置信息
playerListener：拉流播放状态回调监听

config 字段描述如下：

|  字段名 | 字段类型 |  是否必填 |  字段描述 |
| ------------ | ------------ | ------------ | ------------ |
|  context | FragmentActivity | 是 | 当前Activity上下文  |
|  token |  String | 是  | 请求加入房间凭证 |
|  userId | String  | 是  | 用户id  |
|  clientType | String  | 是  | 客户端类型  |
|  padCode | String  | 是  | 云端推流Code  |
|  container | ViewGroup  | 是  | 画面显示容器  |
|  enableMultiControl | boolean  | 否  | 是否开启群控 |
|  padCodes | List  | 否  | 群控云机编号集合 |
|  videoStreamProfileId | int  | 否  | 清晰度 默认高清 |
|  enableGyroscopeSensor | boolean  | 否  | 是否启用传感器功能  |
|  enableAccelerometerSensor | boolean  | 否  | 是否启用重力感应功能  |
|  enableVibrator | boolean  | 否  | 是否启用震动功能  |
|  enableLocationService | boolean  | 否  | 是否启用定位服务功能  |
|  enableLocalKeyboard | boolean  | 否  | 是否启用本地键盘功能  |
|  enableClipboardCloudPhoneSync | boolean  | 否  | 是否启用云机剪切板同步至真机  |
|  enableClipboardLocalPhoneSync | boolean  | 否  | 是否启用真机剪切板同步至云机  |
|  enableCamera | boolean  | 否  | 是否开启摄像头权限  |
|  enableMic | boolean  | 否  | 是否开启麦克风权限  |
|  autoRecycleTime | int  | 否  | 超时自动停止服务时间 默认300s  |
|  streamType | String@StreamType  | 否  | 拉流类型 默认拉取音视频  |
|  remoteLocationMock | LocationInfo  | 否  | 位置信息 |
|  rotation | String@ScreenRotationType  | 否  | 本机屏幕旋转方向 默认竖屏 |
|  videoRenderMode |  int@VideoRenderMode | 否  | 视频流渲染模式 默认自适应  |
|  videoRotationMode | int@VideoRotationMode | 否  | 屏幕旋转模式  默认外部处理旋转 |
|  streamListener | IStreamListener  | 否  | 视频流监听  |

**字段类型信息：**
StreamType：

|  字段名 |  描述 |
| ------------ | ------------ |
| TYPE_VIDEO  | 视频 |
| TYPE_AUDIO  | 音频  |
| TYPE_BOTH  | 音视频  |

ScreenRotationType：

|  字段名 |  描述 |
| ------------ | ------------ |
| TYPE_LANDSCAPE  | 横屏 |
| TYPE_PORTRAIT  | 竖屏  |

VideoRenderMode：

|  字段名 |  描述 |
| ------------ | ------------ |
| VIDEO_RENDER_MODE_COVER  | 画面短边撑满容器 |
| VIDEO_RENDER_MODE_FIT  | 画面等比缩放居中显示于 Container  |
| VIDEO_RENDER_MODE_FILL  | 非等比拉伸画面并充满整个 Container  |

VideoRotationMode：

|  字段名 |  描述 |
| ------------ | ------------ |
| EXTERNAL  | EXTERNAL 外部旋转，默认值；此模式下，用户需通过监听 IStreamListener#onRotation(int) 回调，从外部调整页面方向从而实现画面方向与云手机实例同步 |
| INTERNAL  | INTERNAL，内部旋转；此模式下，SDK 内部对画面进行方向处理，外部不需要做任何画面方向的处理  |

**流媒体 ConnectionState ：**

|  字段名 | 值  |  描述 |
| ------------ | ------------ | ------------ |
| CONNECTION_STATE_DISCONNECTED | 1  |  连接断开超过 12s，此时 SDK 会尝试自动重连。 |
| CONNECTION_STATE_CONNECTING  | 2  | 首次请求建立连接，正在连接中。  |
| CONNECTION_STATE_CONNECTED  |  3 | 首次连接成功。  |
| CONNECTION_STATE_RECONNECTING  | 4  | 涵盖了以下情况： 首次连接时，10 秒内未连接成功; 连接成功后，断连 10 秒。自动重连中。  |
| CONNECTION_STATE_RECONNECTED  | 5  |  连接断开后，重连成功。 |
| CONNECTION_STATE_LOST  | 6  | 处于 CONNECTION_STATE_DISCONNECTED 状态超过 10 秒，且期间重连未成功。SDK 仍将继续尝试重连。  |
| CONNECTION_STATE_FAILED  |  7 |  连接失败，服务端状态异常。SDK 不会自动重连，请重新进房，或联系技术支持。  |

**P2P ConnectionState ：**
|  字段名 | 值  |  描述 |
| ------------ | ------------ | ------------ |
| STATE_NEW  |  10 |  初始化  |
| STATE_CHECKING  |  11 |  连接中  |
| STATE_CONNECTED  |  3 |  连接成功 |
| STATE_FAILED  |  7 |  连接失败  |
| STATE_COMPLETED  |  14 |  连接完成。  |
| STATE_DISCONNECTED  |  15 |  连接断开  |
| STATE_CLOSED  |  16 |  连接关闭  |

**示例代码：**

```kotlin
        val builder: PhonePlayConfig.Builder = PhonePlayConfig.Builder()
        builder.context(this)
            .userId(dto.userId) //必填参数 自定义客户端用户 ID
            .padCode(dto.padCode) // 必填参数, 云手机实例 ID
            .token(dto.token) // 必填参数，临时鉴权 token
            .clientType(dto.clientType) //必填参数 客户端类型
            .container(container) // 必填参数，用来承载画面的 Container, 参数说明: layout 需要是 FrameLayout 或者 FrameLayout 的子类
            .enableMultiControl(isMultiControl) // 选填参数 是否开启群控
            .setPadCodes(mChosePadCodes) // 选填参数 群控设备号集合
            .rotation(rotation) // 选填参数 屏幕的横竖屏 默认竖屏
            .videoStreamProfileId(
                videoStreamProfileId.definition,
                videoStreamProfileId.frameRate,
                videoStreamProfileId.bitrate
            ) // 选填参数，清晰度档位ID 默认高清
            .enableGyroscopeSensor(enableGyroscopeSensor) // 选填参数 打开陀螺仪开关 默认false
            .enableAccelerometerSensor(enableAccelerometerSensor) // 选填参数 打开重力感应开关 默认false
            .enableVibrator(enableVibrator) // 选填参数 打开本地振动开关 默认false
            .enableLocationService(enableLocationService) // 选填参数 打开本地定位功能开关 默认false
            .enableLocalKeyboard(enableLocalKeyboard) // 选填参数 打开本地键盘开关 默认false
            .enableClipboardCloudPhoneSync(enableCloudSync) // 选填参数 打开云机剪切板同步至真机 默认true
            .enableClipboardLocalPhoneSync(enableLocalSync) // 选填参数 打开真机剪切板同步至云机 默认true
            .enableCamera(enableCamera) // 选填参数 开启相机权限 默认 true
            .enableMic(enableMic) // 选填参数 开启麦克风权限 默认 false
            .streamType(streamType) // 选填参数 指定启动云手机时拉取音视频流类型 默认拉取音视频流
            .videoRenderMode(renderMode) // 选填参数 指定视频流渲染模式 默认等比缩放居中模式
            .videoRotationMode(videoRotationMode) // 选填参数 指定视频旋转模式 默认非SDK处理旋转
            .autoRecycleTime(autoRecyclerTime.toInt())
            // 获取音视频流信息回调监听
            .streamListener(object : IStreamListener {
                override fun onFirstAudioFrame(uid: String) {
                    runOnUiThread {
                        Toast.makeText(
                            this@MainActivity,
                            "订阅视频流到收到音频首帧回调",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
                
               override fun onRemoteAudio(byteBuffer: ByteBuffer) {
                }

                override fun onFirstRemoteVideoFrame(uid: String, width: Int, height: Int) {
                    runOnUiThread {
                        Toast.makeText(
                            this@MainActivity,
                            "订阅视频流到收到视频首帧回调",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }

                override fun onVideoSizeChanged(uid: String, width: Int, height: Int) {
                }

                override fun onStreamPaused() {
                    runOnUiThread {
                        Toast.makeText(
                            this@MainActivity,
                            "调用 pause()，暂停播放后的回调",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }

                override fun onStreamConnectionStateChanged(state: Int, reason: Int) {
                    runOnUiThread {
                        Toast.makeText(
                            this@MainActivity,
                            "视频流连接状态变更回调 state: $state",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }

                override fun onScreenRotation(rotationType: Int) {
                    runOnUiThread {
                        requestedOrientation =
                            if (rotationType == ScreenRotationType.TYPE_PORTRAIT) ActivityInfo.SCREEN_ORIENTATION_PORTRAIT else ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                        Toast.makeText(
                            this@MainActivity,
                            "屏幕旋转：${if (rotationType == ScreenRotationType.TYPE_PORTRAIT) "竖屏" else "横屏"}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            })
   
        ArmCloudEngine.start(builder.build(), object : IPlayerListener {
            override fun onPlaySuccess(videoStreamProfileId: Int) {
                Log.e("am", "onPlaySuccess ===> videoStreamProfileId: $videoStreamProfileId")
                runOnUiThread {
                    tvRoomId?.text = "当前房间： ${builder.padCode}"
                    Toast.makeText(this@MainActivity, "拉流成功", Toast.LENGTH_SHORT).show()
                }
                initListener()
            }

            override fun onError(code: Int, msg: String) {
                runOnUiThread {
                    Toast.makeText(
                        this@MainActivity,
                        "onError code：$code msg: $msg ",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onWarning(code: Int, msg: String) {
                runOnUiThread {
                    Toast.makeText(
                        this@MainActivity,
                        "onWarning code：$code  $msg ",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onNetworkChanged(var1: Int) {
                runOnUiThread {
                    Toast.makeText(
                        this@MainActivity,
                        "onNetworkChanged code：$var1",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onServiceInit(extras: Map<String, Any>?) {
                runOnUiThread {
                    ArmCloudEngine.getClipBoardServiceManager()?.setBoardSyncClipListener(object :
                        IClipBoardListener {
                        override fun onClipBoardMessageReceived(data: String) {
                            Toast.makeText(
                                this@MainActivity,
                                "收到剪切板消息： $data",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    })
                    Toast.makeText(this@MainActivity, "onServiceInit", Toast.LENGTH_SHORT).show()
                }
            }

            override fun networkQualityRtt(rtt: Int) {
                runOnUiThread {
                    tvQuality?.text = "NetworkQuality: $rtt ms"
                    Toast.makeText(this@MainActivity, "networkQualityRtt $rtt", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onMultiCloudPhoneJoin(padCode: String) {
                runOnUiThread {
                    ToastUtils.showShort("群控用户加入")
                }
            }

            override fun onMultiCloudPhoneLeave(padCode: String) {
                runOnUiThread {
                    ToastUtils.showShort("群控用户离开")
                }
            }
        })
```

#### 恢复从云机拉流

**方法：**

```kotlin
ArmCloudEngine.resume()
```

**代码示例：**

```kotlin
override fun onResume() {
 super.onResume()
 ArmCloudEngine.resume()
}
```

#### 暂停从云机拉流

**方法：**

```kotlin
ArmCloudEngine.pause()
```

**代码示例：**

```kotlin
override fun onPause() {
 super.onPause()
 ArmCloudEngine.pause()
}
```

#### 停止云机

**方法：**

```kotlin
ArmCloudEngine.stop()
```

**代码示例：**

```kotlin
override fun onDestroy() {
 super.onDestroy()
 ArmCloudEngine.stop()
}
```

#### 权限处理

**方法：**

```kotlin
ArmCloudEngine.onRequestPermissionsResult(requestCode, permissions, grantResults)
```

**代码示例：**

```kotlin
overridefunonRequestPermissionsResult(
 requestCode:Int,
 permissions:Array<String?>,
 grantResults:IntArray
){
 super.onRequestPermissionsResult(requestCode,permissions,grantResults)
 ArmCloudEngine.onRequestPermissionsResult(requestCode,permissions,grantResults)
}
```

### 功能控制

#### 截图

**方法：**

```kotlin
ArmCloudEngine.screenShot(@ScreenShotType screenShotType: Int)
```

**参数：**
screenShotType: 截图类型

| 字段名  | 描述 |
| ------------ | ------------ |
| TYPE_LOCAL  | 截图到本地  |
| TYPE_CLOUD  | 截图到云机  |

#### 设置云机清晰度

**方法1：**

```kotlin
ArmCloudEngine.videoStreamProfileId(videoDefinitionEnum: VideoDefinitionEnum)
```

**参数：**
VideoDefinitionEnum ：

|  字段名 |  描述 |
| ------------ | ------------ |
| DEFINITION_B  | 蓝光 |
| DEFINITION_S  | 超清  |
| DEFINITION_H  | 高清  |
| DEFINITION_L  | 标清  |

**方法2：**

```kotlin
ArmCloudEngine.videoStreamProfileId(definition: String, frameRateId: String, bitrateId: String)
```

**参数：**
definition ： 清晰度
frameRateId ： 帧率
bitrateId ： 码率

| 字段名  | 描述 |
| ------------ | ------------ |
| 清晰度  | 07：144&times;256<br> 08：216&times;384<br>09：288&times;512<br>10：360&times;640<br>11：480&times;848<br>12：540&times;960<br>13：600&times;1024<br>14：480&times;1280<br>15：720&times;1280<br>16：720&times;1920<br>17：1080&times;1920<br>18：1440&times;1920<br>19：1600&times;2560<br>20：2880&times;1080 |
| 帧率  | 1：20fps<br>2：25fps<br>3：30fps<br>4：60fps<br>5：1fps<br>6：5fps<br>7：10fps<br>8：15fps<br>9：2fps  |
| 码率  | 01：1Mbps<br>02：1.5Mbps<br>03：2Mbps<br>04：2.5Mbps<br>05：3Mbps<br>06：3.5Mbps<br>07：4Mbps<br>08：5Mbps<br>09：6Mbps<br>10：8Mbps<br>11：10Mbps<br>12：12Mbps<br>13：200kbps<br>14：400kbps<br>15：600kbps  |

#### 旋转本机屏幕方向

**方法：**

```kotlin
ArmCloudEngine.rotate(ScreenRotationType.TYPE_LANDSCAPE)
```

**参数：**
同 PhonePlayConfig 设置本机旋转方向

#### 是否禁用音频

**方法：**

```kotlin
ArmCloudEngine.muteAudio(isMetuAudio)
```

**参数：**
isMetuAudio : 是否禁用音频
true: 禁用
false：开启

#### 是否禁用视频

**方法：**

```kotlin
ArmCloudEngine.muteVideo(isMetuVideo)
```

**参数：**
isMetuVideo : 是否禁用视频
true: 禁用
false：开启

#### 是否开启本地键盘

**方法：**

```kotlin
ArmCloudEngine.enableLocalKeyboard(isEnable)
```

**参数：**
isEnable : 是否开启本地键盘
true: 启用
false：禁用

#### 是否启用定位功能

**方法：**

```kotlin
ArmCloudEngine.enableLocationService(isEnable)
```

**参数：**
isEnable : 是否启用定位功能
true: 启用
false：禁用

#### 是否启用陀螺仪功能

**方法：**

```kotlin
ArmCloudEngine.enableGyroscopeSensor(isEnable)
```

**参数：**
isEnable : 是否启用传感器功能
true: 启用
false：禁用

#### 是否启用重力感应功能

**方法：**

```kotlin
ArmCloudEngine.enableAccelerometerSensor(isEnable)
```

**参数：**
isEnable : 是否启用重力感应功能
true: 启用
false：禁用

#### 是否启用震动功能

**方法：**

```kotlin
ArmCloudEngine.enableVibrator(isEnable)
```

**参数：**
isEnable : 是否启用震动功能
true: 启用
false：禁用

#### 相机权限

**方法：**

```kotlin
ArmCloudEngine.enableCamera(isEnable)
```

**参数：**
isEnable : 是否开启相机权限
true: 启用
false：禁用

#### 麦克风权限

**方法：**

```kotlin
ArmCloudEngine.enableMic(isEnable)
```

**参数：**
isEnable : 是否开启麦克风权限
true: 启用
false：禁用

#### 设置定位模式

**方法：**

```kotlin
ArmCloudEngine.setLocationServiceMode(mode@LocationMode)
```

**参数：**
mode：定位模式

| 字段名  | 描述 |
| ------------ | ------------ |
| MODE_AUTO    | 自动模式： SDK自动采集定位  |
| MODE_MANUAL  | 手动模式： 设置 setLocationEventListener（locationEventListener: LocationEventListener） 监听 通过remoteLocationMock手动传入位置  |

#### 获取位置服务

**方法：**

```kotlin
ArmCloudEngine.getLocationServer()
```

**返回值：**
类型：ILocationService
描述：位置服务

**ILocationService**
|  方法 | 参数  |  描述 |
| ------------ | ------------ | ------------ |
|  enableLocationService(isEnable: Boolean) | isEnable: 是否开启  | 是否开启定位 |
| remoteLocationMock( @FloatRange(from = -90.0, to = 90.0) latitude: Double, @FloatRange(from = -180.0, to = 180.0) longitude: Double)  | latitude:纬度 <br>  longitude：经度  | 设置位置信息  |
|remoteLocationMock( @FloatRange(from = -90.0, to = 90.0) latitude: Double, @FloatRange(from = -180.0, to = 180.0) longitude: Double, altitude: Double = 0.0, bearing: Float = 0.0f, accuracy: Float = 0.0f,  speed: Float = 0.0f,   time: Long = System.currentTimeMillis(), elapsedRealtimeNanos: Long = SystemClock.uptimeMillis(), satellites: Int = 0)  | latitude:纬度 <br>  longitude：经度  <br> altitude: 高度 <br> bearing: 方位角 <br> accuracy: 水平精度半径（以米为单位） <br> speed: 此位置时的速度（以米/秒为单位 <br> time: 此位置修复的 Unix 纪元时间 <br> elapsedRealtimeNanos: 此修复程序的时间（以系统启动以来经过的实时纳秒为单位） <br> satellites: 卫星数  | 设置位置信息  |

#### 获取剪切板控制器

**方法：**

```kotlin
ArmCloudEngine.getClipBoardServiceManager()
```

**返回值：**
类型：IClipBoardServiceManager
描述：剪切板服务

**IClipBoardServiceManager**
|  方法 | 参数  |  描述 |
| ------------ | ------------ | ------------ |
| setBoardSyncClipListener(iClipBoardListener) | iClipBoardListener@IClipBoardListener: 云机剪切板数据监听  | 设置云机剪切板数据监听 |
| sendClipBoardMessage(data: String)  | data：文本数据  | 手动发送剪贴板数据  |
| enableClipboardLocalPhoneSync(isEnable: Boolean)  | isEnable：是否开启  | 是否开启剪切板真机同步云机  |
| enableClipboardCloudPhoneSync(isEnable: Boolean)  | isEnable：是否开启  | 是否开启剪切板云机同步到真机  |

#### 切换前后摄像头

**方法：**

```kotlin
ArmCloudEngine.switchCamera(isBack)
```

**参数：**
isBack: 是否为后置摄像头
true: 后置
false：前置

#### 发送模拟按键事件

**方法：**

```kotlin
ArmCloudEngine.sendKeyEvent(systemKeyStrokeEnum SystemKeyStrokeEnum)
```

**参数：**
systemKeyStrokeEnum: 按键枚举

| 枚举名  | 描述 |
| ------------ | ------------ |
| BACK  | 返回按键  |
| HOME  | 主页  |
| JOB  | 任务栏  |
| MENU  | 菜单  |
| VOLUME_ADD  | 音量增加  |
| VOLUME_LESS  | 音量减少  |

#### 设置无操作回收服务时间

**方法：**

```kotlin
ArmCloudEngine.autoRecycleTime(recycleTime）
```

**参数：**
recycleTime: 无操作回收服务时间 单位 s

#### 获取无操作回收服务时间

**方法：**

```kotlin
ArmCloudEngine.getAutoRecycleTime(）
```

**返回值：**
类型： int
描述： 无操作回收服务时间 单位 s

#### 设置拉流类型

**方法：**

```kotlin
ArmCloudEngine.streamType(streamType)
```

**参数：**
同 PhonePlayConfig 设置拉流类型

#### 更新视频流渲染模式

**方法：**

```kotlin
ArmCloudEngine.updateVideoRenderMode(renderMode)
```

**参数：**
同 PhonePlayConfig 设置视频流渲染模式

#### 设置视频旋转模式

**方法：**

```kotlin
ArmCloudEngine.setVideoRotationMode(rotationMode)
```

**参数：**
同 PhonePlayConfig 设置视频旋转模式

#### 获取当前播放状态

**方法：**

```kotlin
ArmCloudEngine.getStatus()
```

**返回值：**
类型： int@CloudPhoneState
描述： 当前播放状态
CloudPhoneState :

| 字段名  | 描述 |
| ------------ | ------------ |
| STATE_UN_INIT  | 0x0001 未初始化  |
| STATE_INIT_SUCCESS  | 0x0002 初始化成功  |
| STATE_START  | 0x0003 调用拉流  |
| STATE_RUN  |  0x0004 成功拉流 |
| STATE_STOP  | 0x0005 停止拉流  |

#### 获取消息透传控制器

**方法：**

```kotlin
ArmCloudEngine.getMessageChannel()
```

**返回值：**
类型： IMessageChannel
描述： 消息透传控制器

**IMessageChannel**
**方法:**
| 方法  |  参数 |  描述 |
| ------------ | ------------ | ------------ |
| setMessageListener(listener: IMessageReceiver?)  | listener@IMessageReceiver: 透传消息监听 | 设置消息透传监听，回调云机透传到真机的消息 |
| sendMessage(type: Int, data: String, binderService: String, packageName: String)  | type: 消息类型 <br> data:消息体 <br> binderService: 远端服务名称 <br> packageName: 包名 | 发送透传消息到云机内指定服务 |

#### 获取文件上传控制器

**方法：**

```kotlin
ArmCloudEngine.getUploadManager(application)
```

**参数:**
application ： 全局上下文

**返回值：**
类型： IUploadFileManager
描述： 文件上传控制器

**IUploadFileManager**
**方法:**
| 方法  |  参数 |  描述 |
| ------------ | ------------ | ------------ |
| uploadFile(lifecycleOwner: LifecycleOwner, padCode: String, token: String, path: String, uploadFilePath: String， uploadFileCallBack: IUploadFileCallBack )  | lifecycleOwner: 生命周期所有者 <br> padCode：云机ID <br> token: 临时鉴权 token <br> path: 文件路径 <br> uploadFilePath@UploadFilePath: 文件保存云机路径 <br> uploadFileCallBack@IUploadFileCallBack：文件上传监听| 带生命周期的文件上传 |
| uploadFile(lifecycleOwner: LifecycleOwner, padCode: String, token: String, file: File, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack )  | lifecycleOwner: 生命周期所有者 <br> padCode：云机ID <br> token: 临时鉴权 token <br> file: 待上传文件 <br> uploadFilePath@UploadFilePath: 文件保存云机路径 <br> uploadFileCallBack@IUploadFileCallBack：文件上传监听| 带生命周期的文件上传 |
| uploadFile(padCode: String, token: String, path: String, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack )  |  padCode：云机ID <br> token: 临时鉴权 token <br> path: 文件路径 <br> uploadFilePath@UploadFilePath: 文件保存云机路径 <br> uploadFileCallBack@IUploadFileCallBack：文件上传监听| 文件上传 |
| uploadFile(padCode: String, token: String, file: File, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack )  |  padCode：云机ID <br> token: 临时鉴权 token <br> file: 待上传文件 <br> uploadFilePath@UploadFilePath: 文件保存云机路径 <br> uploadFileCallBack@IUploadFileCallBack：文件上传监听| 文件上传 |
| cancelUpload(path: String)  | path: 文件路径  | 取消上传 |

**UploadFilePath:**

| 字段名  | 描述 |
| ------------ | ------------ |
| PATH_DCIM  | DCIM 目录  |
| PATH_DOCUMENTS  | Documents 目录  |
| PATH_DOWNLOAD  | Download 目录 |
| PATH_MOVIES  |  Movies 目录 |
| PATH_MUSIC  | Music 目录 |
| PATH_PICTURES  | Pictures 目录 |

#### 获取本地键盘与云机应用控制器

**方法：**

```kotlin
ArmCloudEngine.getLocalInputManager()
```

**返回值：**
类型： ILocalInputManager
描述： 获取本地键盘与云机应用控制器

**ILocalInputManager**
**方法:**
| 方法  |  参数 |  描述 |
| ------------ | ------------ | ------------ |
| sendInputText(text: String)  | text: 消息内容  | 发送消息到云机输入框 |

#### 开始本地视频采集

**方法：**

```kotlin
ArmCloudEngine.startCaptureVideo()
```

#### 开始本地音频采集

**方法：**

```kotlin
ArmCloudEngine.startCaptureAudio()
```

#### 停止本地视频采集

**方法：**

```kotlin
ArmCloudEngine.stopCaptureVideo()
```

#### 停止本地音频采集

**方法：**

```kotlin
ArmCloudEngine.stopCaptureAudio()
```

#### 开始视频注入

**方法：**

```kotlin
ArmCloudEngine.startInjectVideoStream(path: String, isLoop: Boolean, fileName: String?)
```
**参数:**
| 参数名  |  类型 |  是否必填 | 描述 |
| ------------ | ------------ | ------------ | ------------ |
| path  | String  | 是 | 视频路径: <br> url / 云机内视频文件路径 |
| isLoop  | Boolean  | 是 | 是否循环 |
| fileName  | String  | 否 | 文件名 <br> path为url类型时必传 |

**视频要求:**
| 名称  |  要求 |
| ------------ | ------------ |
| 支持的视频格式  |  H.264、H.265编码 |
| 视频分辨率  |  宽高需为2的倍数 |


#### 停止视频注入

**方法：**

```kotlin
ArmCloudEngine.stopInjectVideoStream()
```

#### 获取视频注入状态

**方法：**

```kotlin
ArmCloudEngine.getVideoInjectState()
```

**描述：**
视频注入状态回调在@IInjectVideoListener中获取



### 事件监听

#### 设置清晰度切换回调监听

**方法：**

```kotlin
setStreamProfileChangeListener(streamProfileChangeCallBack StreamProfileChangeCallBack)
```

**参数：**
streamProfileChangeCallBack ： 清晰度切换回调接口

**示例代码：**

```kotlin
ArmCloudEngine.setStreamProfileChangeListener(object:StreamProfileChangeCallBack{
 overridefunonVideoStreamProfileChange(
  isSuccess:Boolean,
  from:String,
  current:String
 ){
  runOnUiThread{
   Toast.makeText(
    this@MainActivity,
    "清晰度切换成功from：$fromto:$current",
    Toast.LENGTH_SHORT
   ).show()
  }
 }
})
```

#### 设置无操作回收服务时长的回调监听

**方法：**

```kotlin
setAutoRecycleTimeCallback(autoRecycleTimeCallback SetAutoRecycleTimeCallback)
```

**参数：**
autoRecycleTimeCallback ： 无操作回收服务时长的回调

**示例代码：**

```kotlin
ArmCloudEngine.setAutoRecycleTimeCallback(object : SetAutoRecycleTimeCallback {
 override fun onResult(autoRecycleTime: Int) {
  runOnUiThread {
   Toast.makeText(
    this@MainActivity,
    "无操作回收服务回调 超时时间：$autoRecycleTime",
    Toast.LENGTH_SHORT
   ).show()
  }
 }
})
```

#### 设置本地截图回调监听

**方法：**

```kotlin
setScreenShotCallBack(screenShotCallBack: ScreenShotCallBack)
```

**参数：**
ScreenShotCallBack ： 本地截图监听

**示例代码：**

```kotlin
ArmCloudEngine.setScreenShotCallBack(object : ScreenShotCallBack {
 override fun onScreenShot(bitmap: Bitmap) {
  runOnUiThread {
   Toast.makeText(
    this@MainActivity,
    "本地获取到截图：${bitmap.byteCount}",
    Toast.LENGTH_SHORT
   ).show()
  }
 }
})
```

#### 设置定位回调监听

**方法：**

```kotlin
setLocationEventListener(locationEventListener: LocationEventListener)
```

**参数：**
locationEventListener ： 定位监听

**示例代码：**

```kotlin
ArmCloudEngine.setLocationEventListener(object: LocationEventListener {

 override fun onReceivedRemoteLocationRequest(requestOptions: RequestOptions) {
  runOnUiThread {
   Toast.makeText(
    this@MainActivity,
    "云机请求开启定位",
    Toast.LENGTH_SHORT
   ).show()
  }
 }

 override fun onRemoteLocationRequestEnded() {
  runOnUiThread {
   Toast.makeText(
    this@MainActivity,
    "云机请求停止定位",
    oast.LENGTH_SHORT
   ).show()
  }
 }

 override fun onSentLocalLocation(locationInfo: LocationInfo) {
  runOnUiThread {
   Toast.makeText(
    this@MainActivity,
    "本机发送定位",
    Toast.LENGTH_SHORT
   ).show()
  }
 }

override fun onRemoteLocationUpdated(locationInfo: LocationInfo) {
 runOnUiThread {
  Toast.makeText(
    this@MainActivity,
    "云机位置更新",
    Toast.LENGTH_SHORT
   ).show()
  }
 }
})
```

#### 设置执行adb指令结果回调监听

**方法：**

```kotlin
setExecuteAdbCommandCallback(iExecuteAdbCommandCallback: IExecuteAdbCommandCallback)
```

**参数：**
iExecuteAdbCommandCallback ： 执行Adb指令结果回调

**示例代码：**

```kotlin
ArmCloudEngine.setExecuteAdbCommandCallback(object : IExecuteAdbCommandCallback {
      override fun onExecuteAdbCommandResult(isSuccess: Boolean, content: String) {
          ToastUtils.showShort("执行adb指令成功 isSuccess: $isSuccess resultMsg: $content")
          Log.e(
                    TAG,
                    "onExecuteAdbCommandResult isSuccess ===> $isSuccess  content ===> $content"
                )
            }
        })
```


#### 设置视频注入回调监听

**方法：**

```kotlin
setInjectVideoListener(iInjectVideoListener: IInjectVideoListener)
```

**参数：**
iInjectVideoListener ： 视频注入回调

**示例代码：**

```kotlin
        ArmCloudEngine.setInjectVideoListener(object : IInjectVideoListener {
            override fun onInjectCallBack(code: String, content: String) {
                Log.e(
                    TAG,
                    "onInjectCallBack code: $code  content: $content"
                )
                ToastUtils.showShort(content)
            }

            override fun onVideoInjectInfo(@VideoInjectState state: Int, videoPath: String?) {
                Log.e(
                    TAG,
                    "videoInjectState state: $state"
                )
                ToastUtils.showShort("videoInjectState state: $state  videoPath: $videoPath")
            }
        })
```