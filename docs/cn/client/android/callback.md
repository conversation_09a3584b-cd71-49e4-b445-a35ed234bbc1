---
title: 回调函数
---

## 监听接口

|  接口名 | 接口描述        |
| ------------ |-------------|
|  [ICloudCoreManagerStatusListener](#初始化监听) | 初始化监听       |
|  [IPlayerListener](#拉流播放状态监听) | 拉流播放状态监听    |
|  [IStreamListener](#视频流状态监听) | 视频流状态监听     |
|  [StreamProfileChangeCallBack](#清晰度切换成功监听) | 清晰度切换成功监听   |
|  [SetAutoRecycleTimeCallback](#无操作回收服务时长监听) | 无操作回收服务时长监听 |
|  [ScreenShotCallBack](#截图到本地监听) | 截图到本地监听     |
|  [LocationEventListener](#定位监听) | 定位监听        |
|  [IMessageReceiver](#消息透传监听) | 消息透传监听      |
|  [IUploadFileCallBack](#文件上传监听) | 文件上传监听      |
|  [IClipBoardListener](#云机剪切板监听) | 云机剪切板监听     |
|  [IExecuteAdbCommandCallback](#执行adb指令结果监听) | 执行adb指令结果监听 |
|  [IInjectVideoListener](#视频流注入监听) | 视频流注入监听 |

#### 初始化监听

**接口：** ICloudCoreManagerStatusListener

**方法：**

|  方法名 | 参数  |  描述  |
| ------------ | ------------ | ------------ |
| onPrepared()  | 无  | 初始化成功回调  |

#### 拉流播放状态监听

**接口：** IPlayerListener

**方法：**

|  方法名 |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onPlaySuccess(videoStreamProfileId: Int)  | videoStreamProfileId: 当前云手机画面的清晰度，首帧渲染到画面时触发该回调 |播放成功 |
| onError(code: Int, msg: String)  | code：错误码； msg：错误内容  | 错误信息  |
| onWarning(code: Int, msg: String)  | code：警告码； msg：警告内容  | 警告信息  |
| onNetworkChanged(int type)  | type:  -1: 网络连接类型未知;  0: 网络连接已断开;  1: 网络类型为 LAN;   2: 网络类型为 Wi-Fi（包含热点）;   3: 网络类型为 2G 移动网络;  4: 网络类型为 3G 移动网络;  5: 网络类型为 4G 移动网络;  6: 网络类型为 5G 移动网络  | 网络连接类型和状态切换回调  |
| onServiceInit(extras: Map<String, Any>?)  | extras：保留参数，用于透传一些额外参数  | 加入房间前回调，用于获取并初始化各个功能服务，例如设置各种事件监听回调  |
| networkQualityRtt(rtt: Int)  | rtt:数据在客户端和服务器之间来回往返所需的时间  | 加入房间并发布或订阅流后， 以每 2 秒一次的频率，报告本地用户和已订阅的远端用户的上下行网络质量信息  |
| onMultiCloudPhoneJoin(padCode: String) | padCode: 云机标识 | 群控状态下云机加入群控回调 |
| onMultiCloudPhoneLeave(padCode: String) | padCode: 云机标识 | 群控状态下云机离开群控回调 |

#### 视频流状态监听

**接口：** IStreamListener

**方法：**

| 方法名 | 参数                                               | 描述               |
| ------------ |--------------------------------------------------|------------------|
| onFirstAudioFrame(uid: String)  | uid：远端实例视频流 ID                                   | 订阅视频流到收到音频首帧回调   |
| onRemoteAudio(byteBuffer: ByteBuffer)  | byteBuffer：音频流数据           | 远端音频流数据      |
| onFirstRemoteVideoFrame(uid: String, width: Int, height: Int)  | uid：远端实例视频流 ID <br> width：云机画面宽度 <br> height：云机画面高度 | 订阅视频流到收到视频首帧回调   |
| onVideoSizeChanged(uid: String, width: Int, height: Int)  | uid：远端实例视频流 ID <br> width：云机画面宽度 <br> height：云机画面高度 | 云机画面宽高改变时回调      |
| onStreamPaused()  | 无                                                | 调用 pause()，暂停播放后的回调 |
| onStreamConnectionStateChanged(state: Int)  | state@ConnectionState: 视频流连接状态                   | 视频流连接状态变更回调      |
| onScreenRotation(rotationType: String)  | rotationType: TYPE_LANDSCAPE(横屏)、 TYPE_PORTRAIT（竖屏） | 云机屏幕发生旋转回调       |

#### 清晰度切换成功监听

**接口：** StreamProfileChangeCallBack

**方法:**
| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onVideoStreamProfileChange(isSuccess: Boolean, from: String, current: String)  | isSuccess： （清晰度是否成功切换）  from: （切换前的清晰度） current: （当前的清晰度）     | 清晰度切换回调 |

#### 无操作回收服务时长监听

**接口：** SetAutoRecycleTimeCallback

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onResult(autoRecycleTime: Int)  | autoRecycleTime：无操作回收服务时长 单位 s  | 无操作时长达到设置的时长后回调该方法 |

#### 截图到本地监听

**接口：** ScreenShotCallBack

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onScreenShot(bitmap: Bitmap)  | bitmap：截取云机屏幕的位图  | 调用截图到本地后回调 |

#### 定位监听

**接口：** LocationEventListener

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onReceivedRemoteLocationRequest(requestOptions: RequestOptions)  | options：位置请求选项  | 在手动定位模式下，收到云机实例位置请求的回调 |
| onRemoteLocationRequestEnded()  | 无  | 在手动定位模式下，云机实例定位请求结束 |
| onSentLocalLocation(locationInfo: LocationInfo)  | locationInfo：位置信息  |  在自动定位模式下，向云机实例发送本地设备位置信息后的回调 |
| onRemoteLocationUpdated(locationInfo: LocationInfo)  | locationInfo：位置信息  | 云机实例位置更新后的回调 |

#### 消息透传监听

**接口：** IMessageReceiver

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onReceiveMessage(message: String)  | message：透传消息体  | 云机发出的透传消息 |

#### 文件上传监听

**接口：** IUploadFileCallBack

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onUpdateStart()  | 无  | 上传开始 |
| onUpdateByteChange(totalByte: Long, updateByte: Long)  | totalByte：文件总大小 updateByte：当前上传大小 单位：字节 | 文件上传大小发生改变  |
| onUpdateProgressChange(progress: Int)  | progress：上传进度  | 文件当前上传进度改变 0 ~ 100 |
| onUpdateSuccess(mapData: Map<String, String>)  | mapData：预留数据  | 文件上传成功 |
| onUpdateFail(e: Throwable?)  | e：错误信息  | 文件上传失败 |
| onUpdateEnd()  | 无  | 文件上传结束 |
| onError(code: Int, msg: String)  | code：错误码 msg：错误信息  | 文件上传失败 除文件上传错误的其他错误信息 |

#### 云机剪切板监听

**接口：** IClipBoardListener

**方法:**

| 方法名  |  参数 | 描述 |
| ------------ | ------------ | ------------ |
| onClipBoardMessageReceived(data: String)  | data：云机同步的剪贴板数据 | 云机同步的剪贴板数据 |

#### 执行adb指令结果监听

**接口：** IExecuteAdbCommandCallback

**方法:**

| 方法名  | 参数                             | 描述 |
| ------------ |--------------------------------| ------------ |
| onExecuteAdbCommandResult(isSuccess: Boolean, content: String)  | isSuccess：是否执行成功 content： 执行结果 | 执行adb指令结果监听 |


#### 视频流注入监听

**接口：** IInjectVideoListener

**方法:**

| 方法名  | 参数                             | 描述 |
| ------------ |--------------------------------| ------------ |
| onInjectCallBack(code: String, content: String)  | code：code码 content：注入结果信息 | 视频流注入结果监听 |
| onVideoInjectInfo(@VideoInjectState state: Int, videoPath: String?)  | state：当前注入状态 videoPath： 当前注入视频路径 | 当前视频注入信息 |


**VideoInjectState:**
| 字段名  | 描述 |
| ------------ | ------------ |
| STATE_LIVE  | 注入中 |
| STATE_OFFLINE  | 未注入 |
