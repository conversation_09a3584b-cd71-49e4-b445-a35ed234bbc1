---
title: 云手机服务等级协议
---

## 服务水平协议 SLA

协议生效时间：2024 年 06 月 01 日

本服务等级协议（Service Level Agreement，以下简称 "SLA"）规定了小算云向客户提供的 ARM 云手机服务可用性等级指标及赔偿方案。
小算云会适时修改 SLA 协议，但在您的订购期限内，小算云不会以实质性降低服务水平的方式修改您的 SLA 条款；如果您续订服务，则续订之时届时适用的 SLA 协议的版本将适用于您的续订期。您继续使用服务即代表您同意届时适用的 SLA。

### 1. 定义

**服务周期**：一个服务周期为一个自然月。
**服务周期总时间**：指每个服务周期内的总天数 ×24（小时）×60（分钟）。
**服务时间**：一个服务周期内，扣除系统维护时间，其余时间均为服务时间。如果小算云进行有可能影响系统稳定性的系统维护，小算云会提前 72 小时通知，选择在影响最小的时间段进行系统维护。
**服务不可用**：本服务因小算云的原因在单位时间内（每 5 分钟为一个单位时间）持续无法申请服务时，视为该单位时间内服务不可用。
**服务周期不可用时间**：指一个服务周期内服务不可用的时间的总和。如服务不可用至服务恢复时间超过 1 个单位时间，则服务不可用时间计算为多个单位。无法申请服务持续不足 5 分钟，则不计入服务不可用时间。
**月度服务费用**：客户在一个自然月中就单个云手机实例所支付给小算云的服务费用总额，如果客户一次性支付了多个月份的服务费用，则将按照所购买的月数分摊计算月度服务费用。用户在云市场上购买第三方云手机服务所支付的费用不计入月度服务费。

### 2. 服务可用性

#### 2.1 服务可用性计算方式

服务可用性 =（（服务周期总时间 - 服务周期不可用时间 ）/ 服务周期总时间）× 100%

#### 2.2 服务可用性承诺

对于单实例维度，小算云承诺一个服务周期内提供的云手机服务可用性不低于 99%。
如果本服务未达到上述服务可用性承诺，客户可以依据本协议第 3 条约定获得赔偿。
如下免责情形导致的服务不可用时长不计入服务不可用时间：
1）云手机预先通知客户后进行系统维护所引起的，包括割接、维修、升级和模拟故障演练；
2）客户的业务内容内容违规、或其它原因而导致业务内容被封禁而产生的错误；
3）任何云手机所属设备以外的网络、设备故障或配置调整引起的；客户的应用程序或数据信息受到黑客攻击而引起的；
4）客户维护不当或保密不当致使数据、口令、密码等丢失或泄漏所引起的；
5）客户网站页面或应用程序受到攻击或其他不当行为引起的；
6）客户自行升级操作系统所引起的；客户未遵循本服务产品使用文档或使用建议引起的；
7）客户的应用程序或安装活动所引起的；
8）客户的疏忽或由客户授权的操作所引起的；
9）不可抗力以及意外事件引起的；
10）客户未遵循云手机产品使用文档或使用建议引起的；
11）请求来源非中国大陆 IP 地址的。

### 3 赔偿方案

3.1. 赔偿标准 1)对于单云手机，如服务可用性低于 99%，可按照下表中的标准获得赔偿，赔偿方式仅限于抵扣结算费用且赔偿总额不超过未达到服务可用性承诺当月客户就该云手机支付的单实例月度服务费，若发生其他未在本协议中明确约定的故障情形，赔偿周期按自然日计算，最高赔付额为所发生故障设备的单日结算费用。

| 服务可用性               | 赔付时长                   |
| ------------------------ | -------------------------- |
| 低于 99%但等于或高于 95% | 2 倍超过 99%部分的故障时长 |
| 低于 95%                 | 3 倍超过 95%部分的故障时长 |

2)网络联通性赔偿，以日服务费用为基数，每日累计或一次性超出 30 分钟以上（含 30 分钟），小算云减免当日服务费用 5%；每日累计或一次性超出不联通时间上限 1 小时以上（含 1 小时），小算云减免当日服务费用 15%；累计或一次性超出不联通时间上限 2 小时以上小算云减免当日服务费用 30%。

3.2 补偿申请时限
客户可以在每月第 5 个工作日后对之前的时间段（上个月）没有达到可用性的服务提出补偿申请。补偿申请必须于在云手机没有达到可用性的相关月份结束后 2 个月内提出。超出申请时限的补偿申请将不被受理。
您提出补偿申请后我方会进行相应核实，对于服务月度的服务可用性的计算，若双方出现争议的，双方均同意最终以小算云的后台记录为准。
