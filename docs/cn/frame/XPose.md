## 简介

类Xposed、LSPosed框架的插件加载系统，支持Java和Native Hook。

## 示例

### 1.Hook 普通App

#### 准备

- 被HookApp
- Hook插件apk
- 连接设备终端执行apmt指令加载Hook插件

##### 被HookApp内容

```java
public class MainActivity extends AppCompatActivity {

    static {
        System.loadLibrary("test");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        Log.d("___TestApp___", "call onCreate");
        Log.d("___TestApp___", "call stringFromJNI(\"haha\") return:" + stringFromJNI("haha"));
        Log.d("___TestApp___", "call add(10, 5) return:" + add(10, 5));
        Log.d("___TestApp___", "call add(1, 5) return:" + add(1, 5));
    }

    public native String stringFromJNI(String str);

    public native int add(int a, int b);
}
```

##### Hook插件内容

```java
public class Entry {

    private static final String TAG = "___HKAPP___";
    private static Context globalContext;

    public static void appMain(ClassLoader loader, Context context, String appClass, String pkg, String process) {
        Log.d(TAG, "appClass=" + appClass + ", filesDir=" + context.getFilesDir() + ", pid=" + Process.myPid() + ", process=" + process);
        if (process.contains("sandboxed_process")) {
            return;//webview相关的暂时忽略
        }
        if (pkg.equals("com.armcloud.testapp")) {
            try {
                Log.d(TAG, "do hookTestApp");
                Class<?> MainActivity = loader.loadClass("com.armcloud.testapp.MainActivity");
                XSHelpers.findAndHookMethod(MainActivity, "stringFromJNI", String.class, new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        super.beforeHookedMethod(param);
                        Log.d(TAG, "call MainActivity->stringFromJNI arg=" + param.args[0]);
                    }

                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        super.afterHookedMethod(param);
                        Log.d(TAG, "call stringFromJNI return:" + param.getResult());
                        param.setResult("byebye");
                    }
                });

                // android.app.Activity
                XSHelpers.findAndHookMethod(Activity.class, "onCreate", Bundle.class, new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        super.beforeHookedMethod(param);
                        Log.d(TAG, "call MainActivity->onCreate:" + param.args[0]);
                    }
                });
                Log.d(TAG, "exit hookTestApp");
            } catch (Exception e) {
                Log.d(TAG, "hookTestApp failed:\n" + Log.getStackTraceString(e));
            }
        }
    }
}
```

##### 执行apmt命令加载插件

将Hook插件apk导入到手机(此处导入到SD卡路径)

![](img/003.png)

添加插件

![](img/004.png)

提示"add Patch:appdemo success"表示插件添加成功。 <br>
还可以通过"apmt patch list"查看插件是否添加成功:

![](img/005.png)

运行被Hook apk, 开启logcat, 验证结果:

![](img/006.png)
![](img/007.png)

可以看到hook插件中打印的日志，表示hook成功。

### 2.Hook SystemServer

#### 准备

- Hook插件apk
- 连接设备终端执行apmt指令加载Hook插件

##### Hook插件内容

```java
public class Entry {
    private static final String TAG = "___HK_SysServer___";

    public static void systemMain(ClassLoader classLoader, String pkg, String processName) {
        doHook(classLoader);
    }

    private static void doHook(ClassLoader classLoader) {
        try {

            @SuppressLint("PrivateApi")
            Class<?> timingsTraceAndSlog = classLoader.loadClass("com.android.server.utils.TimingsTraceAndSlog");
            XSHelpers.findAndHookMethod("com.android.server.SystemServer", classLoader, "startBootstrapServices",
                    timingsTraceAndSlog, new XC_MethodHook() {
                        @Override
                        protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                            super.beforeHookedMethod(param);
                            Log.d(TAG, "before call startBootstrapServices");
                        }

                        @Override
                        protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                            super.afterHookedMethod(param);
                            Log.d(TAG, "after call startBootstrapServices");
                        }
                    });
            Log.d(TAG, "load system hook ok");
        } catch (Exception ex) {
            ex.printStackTrace();
            Log.d(TAG, "doHook failed:" + Log.getStackTraceString(ex));
        }
    }
}
```

##### 执行apmt命令加载插件

将Hook插件apk导入到手机(此处导入到SD卡路径)

![](img/009.png)

添加插件 <br>
注：Hook SystemServer -p 需要指定为 "android"

![](img/010.png)

重启设备，开启logcat，验证结果:

![](img/011.png)

可以看到hook插件中打印的日志，表示hook成功。

## 名词介绍

### 1.插件操作命令

该框架使用“[apmt命令](#apmt命令)”操作插件。

### 2.插件

通过“[自定义插件](#自定义插件)”编写Hook插件，使用插件操作命令加载插件。

## apmt命令

### 必要参数

```bash
apmt patch
```

### 基础操作

#### 1.添加补丁包

```bash
apmt patch add -n test -p com.armcloud.testapp -f /sdcard/test.apk
```

#### 2.删除补丁包

```bash
apmt patch del -n test
```

#### 3.查询补丁包

```bash
apmt patch list
```

#### 4.查看帮助信息

```bash
apmt patch help
```

### 全部参数

|  参数 |  描述 |
| ------------ | ------------ |
|  -n |  插件名称 |
|  -p |  需要应用插件的应用包名 |
|  -u |  需要通过url下载插件时，传入下载链接 |
|  -f |  使用本地插件时，传入插件文件路径 |

## 自定义插件

#### 1. Hook普通app

![](img/012.png)

- 入口类必须命名为:androidx.app.Entry
- 入口方法必须命名为:
  public static void appMain(
  ClassLoader loader, //类加载器
  Context context, //上下文
  String appClass, //包名
  String process //进程名
  )

#### 2.Hook SystemServer

![](img/013.png)

- 入口类必须命名为:androidx.app.Entry
- 入口方法必须命名为：
  public static void systemMain(
  ClassLoader classLoader, //类加载器
  String pkg, //包名，默认是android
  String processName  //进程名
  )

## Demo下载

点击下载 [ArmCloudXposeDemo](/ArmCloudXposed.zip)
