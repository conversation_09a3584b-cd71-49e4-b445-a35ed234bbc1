---
title: AIDL接入方式
---

### 1.在项目app目录下的build.gradle开启AIDL

**以build.gradle.kts为例**

```java
android {
   ....
    //开启AIDl
    buildFeatures {
        aidl = true
    }
}
```

### 2.创建aidl文件

**1.在app/src/main创建aidl文件夹 下载aidl压缩包解压到aidl目录(完整路径为aidl.cloud.api.server)**

### 3.在app项目中连接aidl服务

**以kotiln代码为例**

```java
    1. 创建service服务
    val mControl = object : ServiceConnection {
        override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
           LogUtils.d("创建aidl")
           control = ControlInterface.Stub.asInterface(p1)
           MyAidlManager.setControlAidl(control)
     }
        
     override fun onServiceDisconnected(p0: ComponentName?) {
         LogUtils.d("断开连接")
         //断开后重连
         GlobalScope.launch {
             delay(3000)
                startMyService()
            }
        }
    }
        
    val mRoot = object : ServiceConnection {
     override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
           LogUtils.d("创建aidl")
           root = RootInterface.Stub.asInterface(p1)
           MyAidlManager.setRootAidl(root)
        }
        
        override fun onServiceDisconnected(p0: ComponentName?) {
           LogUtils.d("断开连接")
           //断开后重连
           GlobalScope.launch {
               delay(3000)
               startMyService()
           }
        }
    }
        
    2.设置一个全局可使用的Utils
    object MyAidlManager {
        
        var control : ControlInterface? = null
        var root : RootInterface? = null
        
        fun setControlAidl(control : ControlInterface?){
            this.control = control
        }
        
        fun setRootAidl(root : RootInterface?){
           this.root = root
        }
    }
        
    3.绑定aidl服务
    fun startMyService(){
     val intent = Intent("aidl.cloud.api.ControlServer")
     intent.setPackage("com.cloud.rtcgesture")
     bindService(intent, mControl, BIND_AUTO_CREATE)
        
     val intent2 = Intent("aidl.cloud.api.RootServer")
     intent2.setPackage("com.cloud.rtcgesture")
     bindService(intent2, mRoot, BIND_AUTO_CREATE)
    }
        
    4.关闭服务
    override fun onDestroy() {
       super.onDestroy()
       unbindService(mControl)
       unbindService(mRoot)
    }
    
 5.启动服务
    startMyService()
```

### 4.在APP目录下的AndroidManifest.xml下添加适配

```java
    <manifest>
    
     ...
     <queries>
      <package android:name="com.cloud.rtcgesture" />
     </queries>
    
    </manifest>
```

### AIDL文件下载

[点击下载(aidl.zip)](/aidl.zip)
