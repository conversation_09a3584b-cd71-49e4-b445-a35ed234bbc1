---
title: 系统服务API(aidl)
---

### 接口方法描述

#### ControlInterface.java（控制工具类）

###### 1. `void isOpenGms(boolean isOpen)`

**描述**: 启用或禁用 GMS（Google 移动服务）功能。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `isOpen`   | `boolean` | `true` 表示启用 GMS，`false` 表示禁用 GMS |

---

###### 2. `void sendMessage(String message)`

**描述**: 发送消息。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `message`  | `String` | 需要发送的消息内容                 |

---

###### 3. `void setGpsLocation(double latitude, double longitude)`

**描述**: 设置 GPS 位置。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `latitude`   | `double`  | 纬度值（-90 到 90 之间）            |
| `longitude`  | `double`  | 经度值（-180 到 180 之间）         |

---

###### 4. `void setFrame(int width, int height)`

**描述**: 修改屏幕分辨率。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `width`    | `int`   | 屏幕的宽度                       |
| `height`   | `int`   | 屏幕的高度                       |

---

###### 5. `void openProxyUser(String ip, int port, String account, String password, String proxyType, String proxyName)`

**描述**: 打开一个代理，并使用用户名和密码进行身份验证。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `ip`         | `String`  | 代理服务器的 IP 地址               |
| `port`       | `int`     | 代理服务器的端口号                 |
| `account`    | `String`  | 代理账户名                         |
| `password`   | `String`  | 代理账户密码                       |
| `proxyType`  | `String`  | 代理方式    （proxy, vpn） |
| `proxyName`  | `String`  | 代理名称    （http-relay, socks5）                       |

---

###### 6. `void openProxy(String ip, int port, String proxyType, String proxyName)`

**描述**: 打开一个不需要用户身份验证的代理。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `ip`         | `String`  | 代理服务器的 IP 地址               |
| `port`       | `int`     | 代理服务器的端口号                 |
| `proxyType`  | `String`  | 代理方式    （proxy, vpn） |
| `proxyName`  | `String`  | 代理名称    （http-relay, socks5）                       |

---

###### 7. `void setProxyPassPackageName(String packageName)`

**描述**: 设置要绕过代理访问的应用包名。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 需要绕过代理访问的应用包名         |

---

###### 8. `void closeProxy()`

**描述**: 关闭当前代理连接。

---

###### 9. `void startCameraVideo(String path, boolean isLoop)`

**描述**: 开始注入摄像头视频。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `path`     | `String` | 视频文件保存路径                   |
| `isLoop`   | `boolean` | 是否循环播放视频，`true` 表示循环播放，`false` 表示不循环播放 |

---

###### 10. `void stopCameraVideo()`

**描述**: 停止摄像头视频录制。

---

###### 11. `List<String> getProxyPassPackageName()`

**描述**: 获取所有绕过代理访问的应用包名列表。

| 返回值     | 类型     | 描述                               |
|------------|----------|------------------------------------|
| `List<String>` | `List<String>` | 包含所有绕过代理访问的应用包名的列表 |

###### 12. `void startImage(String path)`

**描述**: 开始加载并显示图片。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `path`     | `String` | 图片文件的路径                     |

#### RootInterface.java（root工具类）

###### 1. `void allRoot(boolean isRoot)`

**描述**: 设置是否启用全局 root 权限。

| 参数       | 类型    | 描述                               |
|------------|---------|------------------------------------|
| `isRoot`   | `boolean` | `true` 表示启用全局 root 权限，`false` 表示禁用 |

---

###### 2. `void setRootPackageName(String packageName, boolean isRoot)`

**描述**: 设置指定包名的应用是否拥有 root 权限。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |
| `isRoot`     | `boolean` | `true` 表示启用 root 权限，`false` 表示禁用 |

---

###### 3. `boolean isRoot(String packageName)`

**描述**: 检查指定包名的应用是否拥有 root 权限。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |

| 返回值       | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| 返回值       | `boolean` | `true` 表示该应用具有 root 权限，`false` 表示没有 |

---

###### 4. `boolean isAllRoot()`

**描述**: 检查是否启用了全局 root 权限。

| 返回值       | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| 返回值       | `boolean` | `true` 表示启用了全局 root 权限，`false` 表示没有 |

---

### 真机消息透传到云机

**在aidl.cloud.api.server目录添加MessageInterface.aidl文件**
创建一个Service继承编译后的aidl文件MessageInterface.Stub()
在MessageInterface.Stub()的接口里面实现message方法

###### 1. `void message(int type, String data)`

**描述**: 消息透传

| 参数       | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| type       | `int` | 消息类型 |
| data       | `String` | 消息内容 |

### 获取后台下发任务下载进度

#### 创建ApkDownloaderServer目录需要与aidl路径相同并实现ApkDownloaderInterface aidl方法

###### 1. `void apkDownloaderEnd(String packageName, String appName, long taskId)`

**描述**: 通知 APK 下载任务结束。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |
| `appName`    | `String`  | 应用名称                           |
| `taskId`     | `long`    | 下载任务的唯一标识符               |

---

###### 2. `void apkDownloadStart(String packageName, String iconUrl, String appName, long taskId, String path)`

**描述**: 通知 APK 下载任务开始。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |
| `iconUrl`    | `String`  | 应用图标的 URL                     |
| `appName`    | `String`  | 应用名称                           |
| `taskId`     | `long`    | 下载任务的唯一标识符               |
| `path`       | `String`  | APK 下载的存储路径                 |

---

###### 3. `void apkProgress(String packageName, int progress)`

**描述**: 通知 APK 下载进度。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |
| `progress`   | `int`     | 当前下载进度，范围 0-100           |

---

###### 4. `void apkDownloadFail(String packageName)`

**描述**: 通知 APK 下载失败。

| 参数         | 类型      | 描述                               |
|--------------|-----------|------------------------------------|
| `packageName`| `String`  | 应用包名                           |

---
注意：一般由launcher集成显示进度，包名需要是com.android.mxLauncher3   其他包名需要获取后台下载进度需要联系商务或者客服定制
