## 产品概述

通过自研 ARM 服务器结合自研虚拟化技术和超低延迟音视频传输技术的跨终端安卓云手机，最大化地模拟真实手机的环境和性能。用户可以通过手机、平板、电脑或其他智能设备远程访问和控制这台云手机。

## 产品架构

云手机的产品架构如下图所示：

<img src="/images/cpjg.png" style="max-width:100%;max-height: 500px;"/>

## 应用场景

   **全球社媒营销：**

- 模拟不同国家和地区的网络环境，访问特定国家的应用或内容。
- 批量运营社交账号，自动化完成点赞、评论、发帖等任务。

   **移动办公：**

- 通过云手机远程访问企业应用，保障数据安全。
- 防止敏感数据泄露，即使设备丢失或损坏，重要数据仍存储在云端。
- 支持员工使用个人设备访问云手机，实现办公与个人环境隔离。

   **云游戏：**

- 在云端同时运行多个游戏账号，无需多台实体手机。
- 支持长时间挂机和自动化操作，不占用本地设备资源，避免设备发热和电池损耗。

   **云车机：**

- 场景渲染全部在云机运算，打通座舱硬件与云机算力，无需高配硬件也可以玩海量游戏，快速升级座舱体验。无需下载游戏，实现秒更秒开，即点即玩。

   **应用测试与开发：**

- 在不同安卓版本和设备环境中测试 App 兼容性和性能。
- 模拟各种分辨率和硬件环境，无需购买大量实体测试设备。

   **直播与短视频运营：**

- 在云端批量运行直播 App 或短视频平台，提升账号曝光率。
- 适合直播间刷人气、短视频点赞、转发等运营活动。

## 产品优势

云手机基于模拟真实手机的环境和性能和自主研发的音视频传输技术，具备快速接入、智能调度、安全稳定、超低延时等优势，帮助客户快速搭建云手机方案，满足业务需求。

**1. 快速接入与部署:**

- **API接入**：用户无需复杂配置即可快速接入云手机 PaaS 平台，通过 API 接口轻松实现云手机的管理。
- **多实例部署**：可以快速部署多个云手机实例，实现大规模并行管理，满足企业不同场景的需求。

**2. 高安全性与稳定性:**

- **数据隔离与加密**：云手机 PaaS 平台提供严格的数据隔离措施，并对用户数据进行加密保护，确保数据的安全性。
- **容灾备份**：平台支持数据备份和灾难恢复机制，在系统发生故障时，能够快速恢复服务，保障业务持续稳定运行。

**3. 低延迟与高性能:**

- **超低延迟**：平台通过优化网络架构和音视频传输技术，确保实时操作的超低延迟，提升用户体验。
- **高性能计算**：借助强大的云计算资源和硬件支持，平台能够提供高性能的计算支持，满足大规模计算任务。

**4. 跨平台与多设备支持:**

- **多终端兼容**：云手机 PaaS 平台支持多种终端设备，包括 PC、手机、平板等，用户可以在不同设备上无缝访问。
- **跨平台支持**：不仅支持 Android 系统，还可以灵活接入其他操作系统，满足多样化的业务需求。

**5. 简化管理与运维:**

- **集成管理平台**：提供统一的管理控制台，简化系统运维和监控，帮助用户轻松管理云手机实例及相关服务。
- **自动化运维**：平台提供自动化更新、故障检测和修复功能，减少人工干预，提高运维效率。

**6. 灵活的计费模式:**

- **按需付费**：云手机 PaaS 平台支持灵活的计费模式，用户可以根据实际使用情况按需付费，降低运营成本。

## 基本概念

本文为您介绍云手机产品相关基础概念，以便您更好地理解和使用云手机产品。

- **板卡**：
板卡作为云手机的硬件资源单元，通常封装了CPU、内存、存储等资源。一块板卡可能会被分割成多个虚拟化的资源池，用来创建多个云手机实例

- **实例**：
一个实例即一个云手机。每个实例都拥有独立的操作系统和用户界面，可模拟真实手机的功能和性能。

- **密钥对**：
为用户提供接入 PaaS 平台服务的认证方式和管理，在 PaaS 平台管理后台的用户管理—用户列表中的密钥对获取 Access Key ID 和 Secret Access Key （AK/SK）。

- **镜像**：
包含云手机实例运行所需的 Android 操作系统和应用数据文件的模板。提供 AOSP 13 和 AOSP 14 版本的公共镜像，支持用户快速部署和批量创建云手机实例。此外，用户还可以基于这些公共镜像创建自定义镜像，以满足个性化的配置需求。

- **ADB**：
ADB（Android Debug Bridge）是一个多功能命令行工具，它允许用户与 Android 设备进行通信，以便进行开发、调试和管理。您可以通过 ADB 远程操控云手机执行一系列设备管理和调试任务，例如安装和调试应用程序、访问设备的文件系统、以及进行屏幕截图和视频录制等操作。

- **ADI**：
ADI（Android Device Information）安卓设备信息，在ARMCloud的实例中分为虚拟机和云真机，当创建的实例为云真机时需要设置ADI模版信息，ADI模版信息一般由真实设备克隆而来，其中包含了设备品牌、机型、指纹、传感器、蓝牙、WIFI等大量的基础硬件信息。

## 使用限制

- 由于云手机合规要求，不支持 SIM 卡、手机号码、短信等相关功能。
- 云手机目前不支持在不同账号或地域间迁移业务数据。
- 不支持二次虚拟化，不支持挖矿等违规 app 使用。
