---
title: Callback Functions
---

## SDK Callback Overview

### Cloud Machine Callback (SessionObserver)
| Interface Name  | Description  |
| ------------ | ------------ |
| [onConnected](#connection-success-callback) | Network connection successful |
| [onDisconnected](#disconnection-callback)  | Network disconnected |
| [onClose](#connection-closed-callback) | Remote network closed |
| [onScreenChange](#resolution-change-callback) | Callback for receiving cloud machine width, height, and rotation changes  |
| [onClipboardMessage](#clipboard-callback) | Callback for receiving clipboard data from the cloud machine instance  |
| [onFirstVideoFrame](#first-video-frame-callback) | Callback for the first video frame after subscribing to the video stream upon cloud machine startup  |
| [onLocalScreenshot](#local-screenshot-callback) | Video frame callback after calling `screenshot(true)` |
| [onNetworkQuality](#network-status-callback) | Callback for current network status  |
| [onIdeTimeout](#idle-timeout-callback) | Callback for cloud machine instance idle timeout  |
| [onError](#error-callback) | Callback for cloud machine errors  |
| [onCameraChanged](#camera-status-callback) | Callback for cloud machine camera status  |

### Video Frame Callback (VideoRenderSink)
| Interface Name  | Description  |
| ------------ | ------------ |
| [onFrame](#video-frame-callback) | Video frame callback |

### Batch Stream Pulling Callback (BatchControlObserver)
| Interface Name  | Description  |
| ------------ | ------------ |
| [onBatchPodStartResult](#batch-stream-pull-result-callback) | Batch stream pull result callback |
| [onError](#batch-stream-pull-error-callback) | Batch stream pull error callback |

### Video Frame (VideoFrame)
| Interface Name | Description |
| ---- | ---- |
| [width](#video-frame-width) | Video frame width in pixels |
| [height](#video-frame-height) | Video frame height in pixels |
| [rotation](#video-frame-rotation-angle) | Video frame rotation angle (0°, 90°, 180°, 270°) |
| [buffer](#video-frame-data) | Video frame data |
| [size](#video-frame-size) | Video frame size |


## SDK Callback Detailed Description

#### Connection Success Callback
Description: Network connection successful
Syntax:
```cpp
void onConnected()
```

#### Disconnection Callback
Description: Network disconnected
Syntax:
```cpp
void onDisconnected()
```

#### Connection Closed Callback
Description: Remote network closed
Syntax:
```cpp
void onClose()
```

#### Resolution Change Callback
Description: Receives cloud machine width and height changes
Syntax:
```cpp
void onScreenChange(int width, int height, int rotation)
```
| Parameter | Description  |
| ------------ | ------------ |
| width  | Cloud machine width |
| height  | Cloud machine height |
| rotation  | Cloud machine rotation angle |

#### Clipboard Callback
Description: Callback for receiving clipboard data from the cloud machine instance
Syntax:
```cpp
void onClipboardMessage(const std::string& text)
```
| Parameter | Description  |
| ------------ | ------------ |
| text | Cloud machine clipboard text, UTF-8 encoded |

#### First Video Frame Callback
Description: Callback for the first video frame after subscribing to the video stream upon cloud machine startup
Syntax:
```cpp
void onFirstVideoFrame()
```

#### Local Screenshot Callback
Description: Video frame callback after calling `screenshot(true)`
Syntax:
```cpp
void onLocalScreenshot(std::shared_ptr<VideoFrame>& frame)
```
| Interface | Description |
| ---- | ---- |
| frame | Video frame callback, refer to [VideoFrame](#video-frame) |

#### Network Status Callback
Description: Callback for current network status
Syntax:
```cpp
void onNetworkQuality(int rtt)
```
| Parameter | Description  |
| ------------ | ------------ |
| rtt | Network latency |

#### Idle Timeout Callback
Description: Callback for cloud machine instance idle timeout
Syntax:
```cpp
void onIdeTimeout()
```

#### Cloud Machine Error Callback
Description: Callback for cloud machine errors
Syntax:
```cpp
void onError(int error, const std::string& msg)
```
| Parameter | Description |
| ---- | ---- |
| error | Error code |
| msg | Error description |

#### Camera Status Callback
Description: Callback for cloud machine camera status
Syntax:
```cpp
void onCameraChanged(bool isFront, bool isOpen)
```
| Parameter | Description |
| ---- | ---- |
| isFront | Whether it is the front camera |
| isOpen | Whether the camera is open |

#### Video Frame Callback
Description: Video frame callback
Syntax:
```cpp
void onFrame(std::shared_ptr<VideoFrame>& frame)
```
| Parameter | Description |
| ---- | ---- |
| frame | Refer to [VideoFrame](#video-frame) |

#### Batch Stream Pull Result Callback
Description: Batch stream pull result callback
Syntax:
```cpp
void onBatchPodStartResult(int error, const std::string& msg, const std::vector<std::string>& podList)
```
| Parameter | Description |
| ---- | ---- |
| error | Batch stream pull result |
| msg | Batch stream pull error message |
| podList | List of cloud machines in batch stream pull |

#### Batch Stream Pull Error Callback
Description: Batch stream pull error callback
Syntax:
```cpp
void onError(int error, const std::string& msg)
```
| Parameter | Description |
| ---- | ---- |
| error | Batch stream pull result |
| msg | Batch stream pull error message |

#### Video Frame Width
Description: Video frame width in pixels
Syntax:
```cpp
uint32_t width()
```

#### Video Frame Height
Description: Video frame height in pixels
Syntax:
```cpp
uint32_t height()
```

#### Video Frame Rotation Angle
Description: Video frame rotation angle (0°, 90°, 180°, 270°)
Syntax:
```cpp
uint32_t rotation()
```

#### Video Frame Data
Description: Video frame data
Syntax:
```cpp
uint8_t* buffer()
```

#### Video Frame Size
Description: Video frame size
Syntax:
```cpp
uint32_t size()
```

