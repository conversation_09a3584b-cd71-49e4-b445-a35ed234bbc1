---
title: API Documentation
---

This document describes the interfaces provided by the Cloud Phone PC Client SDK.

## API Overview

### Engine (ArmcloudEngine)
| Interface Name | Description |
| ------------- | ----------- |
| [instance](#get-engine-instance) | Get engine instance (singleton pattern) |
| [init](#initialize-engine) | Initialize engine |
| [destory](#destroy-engine) | Destroy engine |
| [setSdkHost](#set-server-address) | Set server address |
| [createPhoneClient](#create-cloud-phone-session) | Create a cloud phone session for controlling the cloud phone |
| [createBatchControlVideo](#create-batch-streaming-controller) | Create a batch streaming controller |
| [getVideoDeviceList](#get-camera-list) | Get camera list |
| [getAudioDeviceList](#get-microphone-list) | Get microphone list |

### Group Control (GroupControl)
| Interface Name | Description |
| ------------- | ----------- |
| [destory](#destroy) | Destroy |
| [startEventSync](#start-group-control) | Start group control |
| [stopEventSync](#stop-group-control) | Stop group control |
| [setEventSyncMaster](#set-master-control) | Set master control |
| [sendInputText](#send-text-to-specified-cloud-phone-input-box) | Send text to the specified cloud phone input box |
| [startVideoCapture](#start-camera-capture) | Start camera capture |
| [stopVideoCapture](#stop-capture) | Stop capture |
| [getCameraCount](#get-camera-count) | Get camera count |
| [getCameraInfo](#get-camera-info) | Get camera information |

### Small Stream (BatchControlVideo)
| Interface Name | Description |
| ------------- | ----------- |
| [start](#start-streaming) | Start streaming |
| [stop](#stop-streaming) | Stop streaming |
| [stop](#batch-stop-streaming) | Batch stop streaming |
| [stopAll](#stop-all-streaming) | Stop all streaming |
| [contains](#contains) | Check if included |
| [subscribe](#start-subscription) | Start subscription |
| [unsubscribe](#unsubscribe) | Unsubscribe |
| [setVideoSink](#set-video-renderer) | Set video renderer |
| [setBatchControlObserver](#set-callback) | Set callback |

### Large Stream (PhoneClient)
| Interface Name | Description |
| ------------- | ----------- |
| [start](#start-streaming) | Start streaming |
| [stop](#stop-streaming) | Stop streaming |
| [setVideoSink](#set-video-renderer) | Set video renderer |
| [setSessionObserver](#set-cloud-phone-event-callback) | Set callback |
| [sendSwipe](#send-scroll-event) | Send scroll event |
| [sendKeyCode](#send-key-event) | Send keyboard key event (send a complete DOWN+UP event) |
| [sendKeyCode](#send-key-event) | Send keyboard key event (optionally send a single-state event) |
| [volumeUp](#increase-volume) | Increase cloud phone device volume |
| [volumeDown](#decrease-volume) | Decrease cloud phone device volume |
| [sendTouchEvent](#send-touch-event) | Send touch event |
| [setVideoLevel](#change-streaming-quality) | Change streaming quality |
| [screenshot](#take-screenshot) | Take a screenshot |
| [sendInputText](#send-input-text) | Send input text |
| [enableAudio](#enable-audio) | Enable audio |
| [enableVideo](#enable-video) | Enable video |
| [enableBlow](#enable-blow) | Enable blow feature |
| [shake](#shake) | Shake feature |
| [startVideoCapture](#start-camera-capture) | Start camera capture |
| [stopVideoCapture](#stop-capture) | Stop capture |
| [publishStream](#publish-video-stream) | Publish video stream |
| [unPublishStream](#stop-publishing) | Stop publishing |
| [sendCommand](#send-adb-command) | Send ADB command |

## Detailed Interface Description

### Get Engine Instance
**Description:** Get the ArmcloudEngine engine instance.

**Syntax:**
```cpp
static ArmcloudEngine* instance();
```
| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| appId | string | Yes | Set log file path |
| logPath | wstring | No | Log file path |
| isInitEngine | bool | No | Whether to initialize the engine in advance |

### Initialize Engine
**Description:** Initialize the ArmcloudEngine engine.

**Syntax:**
```cpp
bool init(const std::string& appId, const std::wstring& logPath = L"", bool isInitEngine = false);
```
| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| appId | string | Yes | Set log file path |
| logPath | wstring | No | Log file path |
| isInitEngine | bool | No | Whether to initialize the engine in advance |

### Destroy Engine
**Description:** Destroy the ArmcloudEngine engine.

**Syntax:**
```cpp
void destory();
```

### Set Server Address
**Description:** Set the server address.

**Syntax:**
```cpp
void setSdkHost(const std::string& host);
```

| Parameter | Type | Required | Description |
| --------- | ---- | -------- | ----------- |
| host | string | Yes | Set server address |

#### Create Cloud Phone Session

Description: Creates a cloud phone session.

Syntax:
```cpp
PhoneClient* createPhoneClient();
```

#### Create Batch Stream Controller
Description: Creates a batch stream controller.

Syntax:
```cpp
BatchControlVideo* createBatchControlVideo();
```

#### Get Camera List
Description: Retrieves the list of cameras.

Syntax:
```cpp
std::vector<DeviceInfo> getVideoDeviceList();
```

#### Get Microphone List
Description: Retrieves the list of microphones.

Syntax:
```cpp
std::vector<DeviceInfo> getAudioDeviceList();
```

#### Destroy
Description: Destroys the instance.

Syntax:
```cpp
void destory();
```

#### Start Group Control
Description: Starts group control.

Syntax:
```cpp
void startEventSync(const BatchPhoneConfig& config);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| config | BatchPhoneConfig | Yes | Group control configuration |

#### Stop Group Control
Description: Stops group control.

Syntax:
```cpp
void stopEventSync();
```

#### Set Master Control
Description: Sets the master control.

Syntax:
```cpp
void setEventSyncMaster(const std::string& podId);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Master cloud phone ID |

#### Send Text to Cloud Phone Input Box
Description: Sends text to the specified cloud phone input box.

Syntax:
```cpp
void sendInputText(const std::string& podId, const std::string& content);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Master cloud phone ID |
| content | string | Yes | Text content to send, must be UTF-8 encoded |

#### Start Streaming
Description: Starts streaming.

Syntax:
```cpp
void start(const BatchPhoneConfig& config);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| config | BatchPhoneConfig | Yes | Batch streaming configuration |

#### Stop Streaming
Description: Stops streaming.

Syntax:
```cpp
void stop(const std::string& podId);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Cloud phone ID to stop streaming |

#### Stop Streaming in Batch
Description: Stops streaming for multiple devices.

Syntax:
```cpp
void stop(const std::vector<std::string>& podList);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podList | string[] | Yes | List of cloud phone IDs to stop streaming |

#### Stop All Streaming
Description: Stops all streaming.

Syntax:
```cpp
void stopAll();
```

#### Contains
Description: Checks if a cloud phone is included.

Syntax:
```cpp
bool contains(const std::string& podId);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Cloud phone ID to check |

#### Start Subscription
Description: Starts a subscription.

Syntax:
```cpp
void subscribe(const std::string& podId);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Cloud phone ID to subscribe to video stream |

#### Unsubscribe
Description: Cancels a subscription.

Syntax:
```cpp
void unsubscribe(const std::string& podId);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Cloud phone ID to unsubscribe from video stream |

#### Set Video Renderer
Description: Sets the video renderer.

Syntax:
```cpp
void setVideoSink(const std::string& podId, VideoRenderSink* sink);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| podId | string | Yes | Cloud phone ID to set renderer |
| sink | VideoRenderSink | Yes | Video renderer |

#### Set Callback
Description: Sets the callback.

Syntax:
```cpp
void setBatchControlObserver(BatchControlObserver* observer);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| observer | BatchControlObserver | Yes | Callback observer |

#### Start Streaming
Description: Starts streaming.

Syntax:
```cpp
void start(PhoneConfig& config);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| config | PhoneConfig | Yes | Cloud phone connection configuration |

#### Stop Streaming
Description: Stops streaming.

Syntax:
```cpp
void stop();
```

#### Set Video Renderer
Description: Sets the video renderer.

Syntax:
```cpp
void setVideoSink(VideoRenderSink* sink);
```

| Parameter | Type | Required | Description |
| ---- | ---- | ---- | ---- |
| sink | VideoRenderSink* | Yes | Video renderer |

#### Set Cloud Phone Event Callback
**Description:** Call `setSessionObserver()` to set the cloud phone event callback.

**Syntax:**
```cpp
void setSessionObserver(SessionObserver* observer)
```

| Parameter | Type             | Required | Description |
|-----------|----------------|----------|-------------|
| observer  | SessionObserver* | Yes      | Event callback |

#### Send Scroll Event
**Description:** Send a scroll event.

**Syntax:**
```cpp
void sendSwipe(int action, int x, int y, int width, int height, int swipe)
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| action    | int  | Yes      | Event type |
| x         | int  | Yes      | X-coordinate |
| y         | int  | Yes      | Y-coordinate |
| width     | int  | Yes      | Width of the event area |
| height    | int  | Yes      | Height of the event area |
| swipe     | int  | Yes      | Swipe value |

#### Send Key Event (Full Down + Up)
**Description:** Sends a complete `DOWN+UP` keyboard event to the cloud phone instance. Recommended for simulating system function keys like Home, Back, and Menu.

**Syntax:**
```cpp
void sendKeyCode(int code)
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| code      | int  | Yes      | Key code <br> 3: HOME <br> 4: BACK <br> 82: MENU <br> 187: TASK |

#### Send Key Event (Single State)
**Description:** Sends a keyboard event to the cloud phone instance, allowing selection of single-state (DOWN or UP) events. Recommended for high-precision key events like DELETE.

**Syntax:**
```cpp
void sendKeyCode(int action, int code)
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| action    | int  | Yes      | Key action <br> 0: DOWN <br> 1: UP |
| code      | int  | Yes      | Key code <br> 3: HOME <br> 4: BACK <br> 82: MENU <br> 187: TASK |

#### Increase Volume
**Description:** Increases the cloud phone device volume.

**Syntax:**
```cpp
void volumeUp()
```

#### Decrease Volume
**Description:** Decreases the cloud phone device volume.

**Syntax:**
```cpp
void volumeDown()
```

#### Send Mouse Click Event
**Description:** Sends a mouse click event.

**Syntax:**
```cpp
void sendTouchEvent(int action, int x, int y, int width, int height)
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| action    | int  | Yes      | Click action <br> 0: DOWN <br> 1: UP <br> 2: MOVE |
| x         | int  | Yes      | X-coordinate |
| y         | int  | Yes      | Y-coordinate |
| width     | int  | Yes      | Local screen width |
| height    | int  | Yes      | Local screen height |

#### Adjust Stream Quality
**Description:** Sets the video resolution, FPS, and bitrate for cloud phone streaming.

**Syntax:**
```cpp
void setVideoLevel(int resolution, int fps, int bitrate)
```

| Parameter  | Type | Required | Description |
|------------|------|----------|-------------|
| resolution | int  | Yes      | Video resolution settings (e.g., 07: 144x256, 20: 2880x1080) |
| fps        | int  | Yes      | Frame rate settings (e.g., 1: 20fps, 9: 2fps) |
| bitrate    | int  | Yes      | Bitrate settings (e.g., 01: 1Mbps, 15: 600kbps) |

#### Screenshot
**Description:** Captures a screenshot of the current cloud phone display.

**Syntax:**
```cpp
void screenshot(bool local)
```

| Parameter | Type  | Required | Description |
|-----------|------|----------|-------------|
| local     | bool | No       | Save location <br> true: Local <br> false: Cloud phone |

#### Send Input Text
**Description:** Sends text to replace the content in the cloud phone's input field (works only when the input field is focused).

**Syntax:**
```cpp
void sendInputText(const std::string& text)
```

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| text      | string | Yes      | Text to send (UTF-8 encoded) |

#### Enable Audio
**Description:** Enables or disables audio.

**Syntax:**
```cpp
void enableAudio(bool enable)
```

| Parameter | Type  | Required | Description |
|-----------|------|----------|-------------|
| enable    | bool | Yes      | State <br> true: Enable <br> false: Disable |

#### Enable Video
**Description:** Enables or disables video.

**Syntax:**
```cpp
void enableVideo(bool enable)
```

| Parameter | Type  | Required | Description |
|-----------|------|----------|-------------|
| enable    | bool | Yes      | State <br> true: Enable <br> false: Disable |

#### Enable Blow Detection
**Description:** Enables or disables the "Blow Detection" feature.

**Syntax:**
```cpp
void enableBlow(bool enable)
```

| Parameter | Type  | Required | Description |
|-----------|------|----------|-------------|
| enable    | bool | Yes      | State <br> true: Enable <br> false: Disable |

#### Shake
**Description:** Simulates a shake gesture.

**Syntax:**
```cpp
void shake()
```

#### Start Video Capture
**Description:** Starts video capture from a camera.

**Syntax:**
```cpp
void startVideoCapture(int cameraId)
```

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| cameraId  | int  | Yes      | Camera ID |

#### Stop Video Capture
**Description:** Stops video capture.

**Syntax:**
```cpp
void stopVideoCapture()
```

#### Publish Video Stream
**Description:** Publishes a video stream.

**Syntax:**
```cpp
void publishStream()
```

#### Unpublish Video Stream
**Description:** Stops publishing a video stream.

**Syntax:**
```cpp
void unPublishStream()
```

#### Send ADB Command
**Description:** Sends an ADB command to the cloud phone.

**Syntax:**
```cpp
void sendCommand(const std::string& cmd)
```

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| cmd       | string | Yes      | ADB command |

