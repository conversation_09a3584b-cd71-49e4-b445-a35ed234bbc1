---
title: Example Setup
---

### Example Demo
[Click to download](/armcloud_pc_demo.zip)


### System Requirements
Recommended: Windows 10, Visual Studio 2022, C++14 or higher

Minimum Compatibility: Windows 7, Visual Studio 2015, C++11

### Download SDK
[Click to download](/armcloud_pc_sdk_v1.0.3.zip)

### Quick Start
#### Create Project
Create a Windows desktop application

![](1.png)

#### Set Dependencies
Set the header file dependency directory

![](2.png)

#### Link Libraries
1. Set the Linker > General > Additional Library Directories

![](3.png)

2. Set the Linker > Input > Additional Dependencies

![](4.png)

#### Copy DLL Files
Copy the corresponding DLL files from the bin directory

![](5.png)

Copy them to

![](6.png)

#### Debug and Run
