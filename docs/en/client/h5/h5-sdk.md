---
title: API description
---


## API interface description
This document describes the interfaces provided by the Cloud Mobile Web H5 SDK.

## API overview

### initialize

##### Initialize user information parameters and cloud mobile phone link parameters.

```js
const engine = new ArmcloudEngine(params)
```
##### params Field descriptions are as follows:

| Keyword | Value Type | Required | Description |
| ------------ | ------------ | ------------ | ------------ |
| deviceInfo | object | Yes | Information required to connect to the cloud phone |
| viewId | string | Yes | The ID of the div element where the page is mounted |
| callbacks | object | No | Collection of callback functions. See the detailed description of callback functions for details.|

##### The `deviceInfo` field is described as follows:

| Keyword | Value Type | Required | Description |
| ------------ | ------------ | ------------ | ------------ |
| appId | string | Yes | Returned result from backend service when applying for an instance |
| roomCode | string | Yes | Returned result from backend service when applying for an instance |
| roomToken | string | Yes | Returned result from backend service when applying for an instance |
| clientId | string | Yes | Room number |
| userId | string | Yes | User ID |
| autoRecoveryTime | number | No | Inactivity recovery time, range 1s~7200s, default value: 300 |
| isFullScreen | number | No | Full screen mode 0: adaptive scaling (not full screen); 1: fill screen (full screen), default value: 1 |
| mediaType | number | No | Media stream type 1: audio only 2: video only 3: both audio and video, default value: 3 |
| rotateType | number | No | Screen rotation 0: portrait, 1: landscape, default value: 0 |
| keyboard | string | No | Keyboard type 'local': local keyboard, 'pad': cloud keyboard, default value: 'pad' |

### Cloud Device Control Interface

| Method Name  |  Method Description |
| ------------ | ------------ |
| [isSupported](./h5-sdk.md#browser-support-check)  | Check if the browser supports RTC services  |
| [start](./h5-sdk.md#join-room)  | Join a room  |
| [stop](./h5-sdk.md#leave-room)  | Leave a room  |
| [setKeyboardStyle](./h5-sdk.md#switch-local-cloud-machine-keyboard-input-type)  | Switch between local/cloud keyboard input types  |
| [setStreamConfig](./h5-sdk.md#set-resolution-bitrate-and-frame-rate)  | Set resolution, bitrate, and frame rate  |
| [resumeAllSubscribedStream](./h5-sdk.md#resume-receiving-remote-media-streams)  | Resume receiving media streams from the remote  |
| [pauseAllSubscribedStream](./h5-sdk.md#pause-receiving-remote-media-streams)  | Pause receiving media streams from the remote  |
| [saveScreenShotToLocal](./h5-sdk.md#screenshot-save-to-local)  | Save a screenshot locally  |
| [saveScreenShotToRemote](./h5-sdk.md#screenshot-save-to-cloud-machine)  | Save a screenshot to the cloud device  |
| [setPhoneRotation](./h5-sdk.md#set-portrait-landscape-mode-playback)  | Set screen orientation for playback  |
| [setGPS](./h5-sdk.md#custom-gps-information)  | Customize GPS information  |
| [setAutoRecycleTime](./h5-sdk.md#set-inactivity-recycle-time)  | Set the no-operation recovery time  |
| [getAutoRecycleTime](./h5-sdk.md#get-inactivity-recycle-time)  | Get the no-operation recovery time  |
| [sendCommand](./h5-sdk.md#send-key-command)  | Send key commands  |
| [increaseVolume](./h5-sdk.md#increase-cloud-machine-volume)  | Increase cloud device volume  |
| [decreaseVolume](./h5-sdk.md#decrease-cloud-machine-volume)  | Decrease cloud device volume  |
| [sendInputClipper](./h5-sdk.md#send-string-to-cloud-phone-s-clipboard)  | Send a string to the cloud device clipboard  |
| [sendInputString](./h5-sdk.md#send-string-to-cloud-phone-s-input-field)  | Send a string to the cloud device input field  |
| [saveCloudClipboard](./h5-sdk.md#enable-disable-cloud-machine-clipboard-content-callback)  | Receive cloud device clipboard content callback  |
| [sendShake](./h5-sdk.md#send-shake-command)  | Send a "shake" command  |
| [setMicrophone](./h5-sdk.md#enable-disable-microphone)  | Enable or disable the microphone  |
| [setCamera](./h5-sdk.md#enable-disable-camera)  | Enable or disable the camera  |


### Cloud Machine Control Interface

#### Browser Support Check
##### Method:
```
engine.isSupported()
```
Type:
```
() => Promise<boolean>
// true: Supported, false: Not supported
```
##### Code Example:
```js
// Check if the browser supports RTC service listening
const isSupported = await armCloud.value.isSupported();
if (!isSupported) {
  showNotify({
    type: "warning",
    message: "This browser does not support RTC service"
  });
  return false;
}
```

#### Join Room
##### Parameters:
- **isGroupControl**: Whether to enable group control
  - Type: boolean
  - Value: false
- **pads**: Room numbers to be controlled in a group
  - Type: Array
  - Default Value: []

##### Method:
```
engine.start(isGroupControl, pads)
```

#### Leave Room
##### Method:
```
engine.stop()
```
**Note**: The `stop()` method destroys the engine and the DOM nodes created during the process. If there's a need to switch cloud machines, `stop()` must be called before each `new ArmcloudEngine()`.

#### Switch Local/Cloud Machine Keyboard Input Type
##### Method:
```
engine.setKeyboardStyle(keyboard: string)
```
##### Parameters:
- **keyboard**: 
  - Type: string
  - Value: `local` for local input method, `pad` for cloud machine virtual input method.

##### Code Example (modify as needed):
```js
engine.setKeyboardStyle('pad');
```

#### Set Resolution, Bitrate, and Frame Rate
##### Method:
```
engine.setStreamConfig(definitionConfig: CustomDefinition)
```
##### Parameters:
- **definitionConfig**: 
  - Type: object
  - Values:

| Field Name      | Description                                                                                                        |
|------------------|--------------------------------------------------------------------------------------------------------------------|
| **definitionId** | Resolution <br> 7: 144 * 256;<br>8: 216 * 384;<br>9: 288 * 512;<br>10: 360 * 640;<br>11: 480 * 848;<br>12: 540 * 960;<br>13: 600 * 1024;<br>14: 480 * 1280;<br>15: 720 * 1280;<br>16: 720 * 1920;<br>17: 1080 * 1920;<br>18: 1440 * 1920;<br>19: 1600 * 2560;<br>20: 2880 * 1080 |
| **framerateId**  | Frame rate <br>1: 20fps;<br>2: 25fps;<br>3: 30fps;<br>4: 60fps;<br>5: 1fps;<br>6: 5fps;<br>7: 10fps;<br>8: 15fps;<br>9: 2fps  |
| **bitrateId**    | Bitrate <br>1: 1Mbps;<br>2: 1.5Mbps;<br>3: 2Mbps;<br>4: 2.5Mbps;<br>5: 3Mbps;<br>6: 3.5Mbps;<br>7: 4Mbps;<br>8: 5Mbps;<br>9: 6Mbps;<br>10: 8Mbps;<br>11: 10Mbps;<br>12: 12Mbps;<br>13: 200kbps;<br>14: 400kbps;<br>15: 600kbps  |

##### Code Example (modify as needed):
```js
const definitionConfig = ref({
  definitionId: 12,
  framerateId: 2,
  bitrateId: 3
});
engine.setStreamConfig(definitionConfig.value);
```

#### Resume Receiving Remote Media Streams
##### Method:
```
engine.resumeAllSubscribedStream(mediaType: number)
```
##### Parameters:
- **mediaType**: 
  - Type: number
  - Values:
    - 1: Resume audio stream;
    - 2: Resume video stream;
    - 3: Resume both audio and video streams;

##### Code Example (modify as needed):
```js
// Resume audio stream
engine.resumeAllSubscribedStream(1);
```

#### Pause Receiving Remote Media Streams
##### Method:
```
engine.pauseAllSubscribedStream(mediaType: number)
```
##### Parameters:
- **mediaType**: 
  - Type: number
  - Values:
    - 1: Pause audio stream;
    - 2: Pause video stream;
    - 3: Pause both audio and video streams;

##### Code Example (modify as needed):
```js
// Pause audio stream
engine.pauseAllSubscribedStream(1);
```

#### Screenshot Save to Local
##### Method:
```
engine.saveScreenShotToLocal()
```
##### Return Value:
Type: ```Promise<ImageData>```

##### Code Example (modify as needed):
```js
engine.saveScreenShotToLocal().then(res => {
  const imageData = res;
  // Create canvas element
  const canvas: HTMLCanvasElement = document.createElement("canvas");
  canvas.width = imageData.width;
  canvas.height = imageData.height;
  const ctx = canvas.getContext("2d");

  // Draw image data on canvas
  ctx.putImageData(imageData, 0, 0);
  // Convert Canvas to image and save to local
  const link = document.createElement("a");
  // Use Canvas's toDataURL() method to convert Canvas to base64 encoded image URL
  link.href = canvas.toDataURL();
  // Set the filename for the download
  link.download = "image.png";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
});
```

#### Screenshot Save to Cloud Machine
##### Method:
```
engine.saveScreenShotToRemote()
```
##### Code Example:
```js
engine.saveScreenShotToRemote();
```

#### Set Portrait/Landscape Mode Playback
##### Method:
```
engine.setPhoneRotation(type: number)
```
##### Parameters:
- **type**: 
  - Type: number
  - Values:
    - 0: Portrait mode;
    - 1: Landscape mode;

##### Code Example (modify as needed):
```js
// Set to landscape mode
engine.setPhoneRotation(1);
```

### Custom GPS Information
#### Method:
```
engine.setGPS(longitude: number, latitude: number)
```
#### Parameters
- **longitude**: Longitude
  - Type: number

- **latitude**: Latitude
  - Type: number

#### Code Example (modify as necessary):
```js
// Set longitude and latitude coordinates
engine.setGPS(113, 28);
```

### Set Inactivity Recycle Time
#### Method:
```
engine.setAutoRecycleTime(second: number)
```
#### Parameters
- **second**:
  - Type: number
  - Default: 300s, Range: 1s~7200s

#### Code Example (modify as necessary):
```js
engine.setAutoRecycleTime(300);
```

### Get Inactivity Recycle Time
#### Method:
```
engine.getAutoRecycleTime()
```
#### Return Value:
- Type: ```Promise<number>```

#### Code Example:
```js
const time = await engine.getAutoRecycleTime();
console.log(time);
```

### Send Key Command
#### Method:
```
engine.sendCommand(command: string)
```
#### Parameters
- **command**:
  - Type: string
  - Values: 
    - back: back command
    - home: home command
    - menu: menu command

#### Code Example (modify as necessary):
```js
// Send home key command
engine.sendCommand("home");
```

### Increase Cloud Machine Volume
#### Method:
```
engine.increaseVolume()
```
#### Code Example:
```js
engine.increaseVolume();
```

### Decrease Cloud Machine Volume
#### Method:
```
engine.decreaseVolume()
```
#### Code Example:
```js
engine.decreaseVolume();
```

### Send String to Cloud Phone's Clipboard
#### Method:
```
engine.sendInputClipper(text: string)
```

#### Code Example:
```js
// This operation will only paste the copied content into the cloud machine's clipboard. 
// To use it, you need to long-press to paste in the cloud machine's input field.
const text = 'hello world';
engine.sendInputClipper(text);
```

### Send String to Cloud Phone's Input Field
#### Method:
```
engine.sendInputString(text: string)
```

#### Code Example:
```js
// This operation will automatically send the copied content to the input field when it is focused, 
// without needing to long-press to paste.
const text = 'hello world';
engine.sendInputString(text);

// Note: This operation will only send the text content to the input field 
// and will not paste the content into the cloud machine's clipboard. 
// If needed, call engine.sendInputClipper(text) manually.
```

### Enable/Disable Cloud Machine Clipboard Content Callback
#### Method:
```
engine.saveCloudClipboard(flag: boolean)
```
#### Parameters
- **flag**:
  - Type: boolean
  - Values:
    - true: receive cloud machine clipboard content callback
    - false: do not receive cloud machine clipboard content callback

#### Code Example:
```js
// This operation will automatically send the copied content to the input field when it is focused, 
// without needing to long-press to paste.
const flag = true;
engine.saveCloudClipboard(flag);
```

### Send Shake Command
#### Method:
```
engine.sendShake(time: number)
```
#### Parameters
- **time**:
  - Type: number
  - Value: milliseconds
  - Default: 1500

#### Code Example:
```js
// A long duration may cause multiple screenshots
engine.sendShake(1500);
```

### Enable/Disable Microphone
#### Method:
```
engine.setMicrophone(enable: boolean)
```
#### Parameters
- **enable**:
  - Type: boolean
  - Default: true

#### Code Example:
```js
engine.setMicrophone(true);
```

### Enable/Disable Camera
#### Method:
```
engine.setCamera(enable: boolean)
```
#### Parameters
- **enable**:
  - Type: boolean
  - Default: true

#### Code Example:
```js
engine.setCamera(true);
```