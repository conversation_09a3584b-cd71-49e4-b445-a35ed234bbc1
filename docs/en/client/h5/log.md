---
title: Change log
---


| Version  | Update Content  | Update Date  |
| -------- | --------------- | ------------ |
| V1.1.2   | 1. Added camera and microphone control features. | 2024.08.13 |
| V1.1.1   | 1. Added group control feature. | 2024.08.05 |
| V1.1.0   | 1. Added logging. <br> 2. Fixed landscape mode issue. | 2024.07.29 |
| V1.0.9   | 1. Added logging. | 2024.07.25 |
| V1.0.8   | 1. Added WebSocket reconnection mechanism and callback. <br> 2. Added loading progress-related callbacks. | 2024.07.24 |
| V1.0.7   | 1. Optimized microphone functionality. <br> 2. Optimized reporting feature. | 2024.07.18 |
| V1.0.6   | 1. Added shake feature. <br> 2. Added microphone sound input. <br> 3. Fixed green screen issue. <br> 4. Added log reporting feature. <br> 5. Added option to maintain channel connection without audio/video. <br> 6. Fixed issue with Firefox not opening. | 2024.07.18 |
| V1.0.3   | 1. Handled incorrect message format error. <br> 2. Added SDK version number field: armCloud.version. | 2024.07.09 |
| V1.0.2   | 1. Optimized logic for successful loading of the first frame of video. <br> 2. Adjusted automatic playback failure logic; default mute playback. | 2024.07.04 |
| V1.0.1   | 1. Added onRenderedFirstFrame() callback for successful rendering of the first video frame. <br> 2. On console platform, dragging event remains selected after releasing the mouse. <br> 3. P2P latency and packet loss rate callback. <br> 4. When deviceInfo is 3, sound does not play for cloud machine video. <br> 5. Mute and unmute do not take effect. | 2024.07.03 |
| V1.0.0   | 1. Handled landscape and portrait mode for game screen. <br> 2. Added clipboard-related APIs (sendInputClipper, onOutputClipper). <br> 3. Fixed black screen issue when refreshing H5 page. <br> 4. Fixed issue where the back button needs to be clicked twice in certain scenes. <br> 5. Optimized mouse wheel event. <br> 6. Changed menu event to pop up the background application list. <br> 7. Fixed issue with saving screenshots to local. <br> 8. Added configuration for callback initialization to receive clipboard content. <br> 9. Added API for receiving clipboard content callback: armCloud.saveCloudClipboard(true). | 2024.06.26 |