---
title: Callback Functions
---

# Callback Functions

Callback functions are interfaces triggered by the SDK in specific scenarios, and you can choose to implement them based on your specific business needs.

### Callback Function List

**Unified Status Codes:** `code: 0` for success, `-1` for failure.

| Callback Function Name | Callback Parameters | Description |
| ---------------------- | ------------------- | ----------- |
| **onInit** | `onInit({code, msg})`<br>**code**: Initialization result, `0`: success, `-1`: failure<br>**msg**: Reason for initialization failure | Initialization callback |
| **onConnectSuccess** | No parameters | Triggered when the `start()` interface is called successfully |
| **onErrorMessage** | `onErrorMessage({code})`<br>**code**: `0` RTC channel interrupted, `1` error retrieving statistics, `2` packet loss rate | Triggered when playback encounters an exception |
| **onConnectFail** | `onConnectFail({code})`<br>**code**: Error code, see error code documentation for details | Triggered when the `start()` interface fails |
| **onAutoRecoveryTime** | No parameters | Triggered when the inactivity time expires |
| **onAutoplayFailed** | `onAutoplayFailed(event)`<br>**event**: Player event<br>{<br>**userId**: string, // User ID of the stream that failed to autoplay (omitted for local stream failures)<br>**kind**: string, // Media type of autoplay failure: `"video"` or `"audio"`<br>**streamIndex**: number, // Video stream property: `0`: main stream; `1`: screen stream<br>**mediaType**: number, // Type of remote media stream: `1`: audio; `2`: video; `3`: audio and video<br>} | Triggered when autoplaying audio or video streams fails |
| **onRunInformation** | `onRunInformation(stats)`<br>**stats**: Remote media stream statistics, updated every 2 seconds.<br>{<br>**userId**: string, // User ID of the participant<br>**audioStats**: object, // Remote audio stream information<br>**videoStats**: object, // Remote video stream information<br>**isScreen**: boolean, // Indicates whether the media stream belongs to a screen stream<br>} | Callback for current running information |
| **onChangeResolution** | `onChangeResolution(width, height)`<br>**width**: Current resolution width<br>**height**: Current resolution height | Triggered when the resolution changes |
| **onTransparentMsg** | `onTransparentMsg(type, msg)`<br>**type**: Reserved field, currently unused, default is `0`.<br>**msg**: Message content, string. | Triggered when a message is received from the cloud app |
| **onOutputClipper** | `onOutputClipper(msg)`<br>**msg**: Copied message content, string. | Triggered when content is copied in the cloud machine |
| **onRenderedFirstFrame** | No parameters | Triggered when the first frame of video rendering is successful |
| **onAudioError** | `onAudioError({code, msg})`<br>**msg**: `API_NOT_AVAILABLE`: The current browser does not support microphone functionality | Triggered when obtaining audio-related permissions fails |
| **onAudioInit** | `onAudioInit({code})` | Triggered when audio permissions are successfully obtained, and microphone sound is being collected |
| **onAudioPause** | `onAudioPause({code})` | Callback for successfully pausing audio collection |
| **onProgress** | `onProgress({code, msg})`<br>{<br>**"WS_CONNECT"**: {**code**: 100, **msg**: "WS starting connection"},<br>**"WS_SUCCESS"**: {**code**: 101, **msg**: "WS connection successful"},<br>**"WS_CLOSE"**: {**code**: 102, **msg**: "WS connection closed"},<br>**"WS_ERROR"**: {**code**: 103, **msg**: "WS connection error"},<br>**"OWN_JOIN_ROOM"**: {**code**: 200, **msg**: "Received join room information"},<br>**"RECEIVE_OFFER"**: {**code**: 201, **msg**: "Offer information set successfully"},<br>**"RECEIVE_OFFER_ERR"**: {**code**: 202, **msg**: "Failed to set offer information"},<br>**"SEND_ANSWER"**: {**code**: 203, **msg**: "Sending answer information"},<br>**"SEND_ANSWER_ERR"**: {**code**: 204, **msg**: "Failed to send answer information"},<br>**"RECEIVE_ICE"**: {**code**: 205, **msg**: "ICE information added successfully"},<br>**"RECEIVE_ICE_ERR"**: {**code**: 206, **msg**: "Failed to add ICE information"},<br>**"SEND_ICE"**: {**code**: 207, **msg**: "Sending ICE information"},<br>**"RTC_CONNECTING"**: {**code**: 300, **msg**: "RTC is connecting"},<br>**"RTC_CONNECTED"**: {**code**: 301, **msg**: "RTC connection successful"},<br>**"RTC_DISCONNECTED"**: {**code**: 302, **msg**: "RTC disconnected"},<br>**"RTC_CLOSE"**: {**code**: 303, **msg**: "RTC connection closed"},<br>**"RTC_FAILED"**: {**code**: 304, **msg**: "RTC connection failed"},<br>**"RTC_TRACK_VIDEO"**: {**code**: 305, **msg**: "RTC received VIDEO stream"},<br>**"RTC_TRACK_VIDEO_LOAD"**: {**code**: 306, **msg**: "RTC loaded successfully in VIDEO after receiving VIDEO stream"},<br>**"RTC_CHANNEL_OPEN"**: {**code**: 307, **msg**: "RTC message channel connection successful"},<br>**"RTC_CHANNEL_ERR"**: {**code**: 308, **msg**: "RTC message channel connection failed"},<br>**"VIDEO_FIRST_FRAME"**: {**code**: 309, **msg**: "VIDEO loaded successfully when UI information from the cloud machine has not been received"},<br>**"VIDEO_FIRST_FRAME"**: {**code**: 310, **msg**: "VIDEO first frame rendered successfully"}} | Loading progress callback |
| **onSocketCallback** | `onSocketCallback({code})`<br>**code**: `0` for connection successful, `1` for connection closed, `-1` for connection failed | WebSocket related callback |
| **onUserLeaveOrJoin** | `onUserLeaveOrJoin({type, userInfo})`<br>**type**: `join` for joining room, `leave` for leaving room<br>**userInfo**: `{userId: "", extraInfo: ""}` | Callback for user joining or leaving the room |
| **onGroupControlError** | `onGroupControlError({code, msg})`<br>**code**: `TOKEN_ERR` token retrieval failed<br>`INVALID_TOKEN`: Used an expired or invalid token when joining the room<br>`JOIN_ROOM_FAILED`: Room join failed<br>`REPEAT_JOIN`: Repeatedly joining the room<br>`ROOM_FORBIDDEN`: Room is banned, causing join failure<br>`USER_FORBIDDEN`: User is banned, causing join failure<br>**msg**: Error message | Callback for group control errors |