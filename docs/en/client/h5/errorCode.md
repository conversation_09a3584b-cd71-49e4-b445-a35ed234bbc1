---
title: Error Code
---



| Name                  | Value                       | Description                                                                                     |
|-----------------------|-----------------------------|-------------------------------------------------------------------------------------------------|
| INVALID_ENGINE        | 'INVALID_ENGINE'            | The parameter passed to destroy<PERSON><PERSON><PERSON> is not a valid engine object.                            |
| INVALID_PARAMS        | 'INVALID_PARAMS'            | General error code. The parameters passed when calling the method are invalid. Please refer to the method documentation for the correct parameters. |
| INVALID_TOKEN         | 'INVALID_TOKEN'             | The Token used when calling start to join the room has expired or is invalid. Please obtain a new Token and call start again to join the room. |
| JOIN_ROOM_FAILED      | 'JOIN_ROOM_FAILED'          | Failed to call start to join the room; see the specific error reason in the message.          |
| REPEAT_JOIN           | 'REPEAT_JOIN'               | Repeatedly joining the room. Triggered when start is called again after already joining the room. |
| ROOM_FORBIDDEN        | 'ROOM_FORBIDDEN'            | Failed to call start to join the room because the room is forbidden.                           |
| USER_FORBIDDEN        | 'USER_FORBIDDEN'            | Failed to call start to join the room because the local user is forbidden.                     |
| DUPLICATE_LOGIN       | 'DUPLICATE_LOGIN'           | A user with the same user ID joined the room, causing the current user to be kicked out. Triggered via onErrorMessage callback. |
| RTM_DUPLICATE_LOGIN   | 'RTM_DUPLICATE_LOGIN'       | A user with the same user ID logged in, causing the locally logged-in user to be kicked out. Triggered via onErrorMessage callback. |
| RTM_TOKEN_ERROR       | 'RTM_TOKEN_ERROR'           | The Token used when reconnecting to the real-time signaling server is invalid. Triggered via onErrorMessage callback. Please use a new Token to log in again. |
| TOKEN_EXPIRED         | 'TOKEN_EXPIRED'             | The Token expired after joining the room. Triggered via onErrorMessage callback. Please obtain a new Token and call start to rejoin the room. |
| RECONNECT_FAILED      | 'RECONNECT_FAILED'          | The SDK failed to reconnect with the server and will not automatically retry. Triggered via onErrorMessage callback. You need to leave the room and rejoin, or contact technical support. |
| KICKED_OUT            | 'KICKED_OUT'                | The server called OpenAPI to kick the current user out of the room. Triggered via onErrorMessage callback. |
| ROOM_DISMISS          | 'ROOM_DISMISS'              | The server called OpenAPI to dissolve the room, causing all users to be removed from the room. Triggered via onErrorMessage callback. |
