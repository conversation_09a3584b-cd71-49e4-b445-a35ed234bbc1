---
title: Example Build
---


# Example Build
Environmental requirements
```
IOS 11.0 and above
Android Browser: Requires a WebRTC-enabled browser kernel
PC Web browser: Chrome 58 + browser with Safari 11 and above
```
# Quick Access
Method 1: After downloading the arm cloudH5S DK package file, unzip it and import it
[Click to Download (armcloudH5SDK.zip)](/armcloudH5SDK.zip)
```js
import { ArmcloudEngine } from "@/lib/index.es.js"
```
Method 2: Load through NPM.
```sh
Installation:
# npm
npm i armcloud-rtc
# yarn
yarn add armcloud-rtc
# pnpm
pnpm add armcloud-rtc
```

```js
//use
import { ArmcloudEngine } from "armcloud-rtc"
const engine = new ArmcloudEngine({
//.. initialization parameter
})
```
Explain
1.Create a container on the page
```html
<div class="phoneBox" id="phoneBox" style="background-color: grey;overflow:hidden;width: 100%;height: 100%;text-align: center;"></div>
```
2. Obtain instance information through the PaaS interface to obtain instance connection information.
3. After obtaining the instance information data, * * const armCloud = new ArmcloudEngine ({//.. initialization parameter }) * * is initialized.
4. After successful initialization, link the device through the * * armCloud. Start () * *.
5. You need to stop linking to the cloud phone and call the * * armCloud. Stop () * * to disconnect the link.

## Sample Demo
[Click to Download (armcloudH5SDK-demo.zip)](/armcloudH5SDK-demo.zip)

<p style='color:red'>baseUrl: Please set the corresponding address</p>

- Domestic: https://openapi.armcloud.net 
- Overseas: https://openapi-hk.armcloud.net 

<p style="color:red">
  token: Obtain the token by calling the server-side API
  <a href="/en/server/OpenAPI.html#sdk-token-issuance">/openapi/open/token/stsToken</a>
</p>

- Note: Please use the domestic OpenAPI address for domestic requests and the overseas OpenAPI address for overseas requests.


## Sample code
```html
// Taking Vue project as an example
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { showNotify, showDialog } from "vant";
import "vant/es/notify/style";
import "vant/es/dialog/style";
import { ArmcloudEngine } from "armcloud-rtc";

const armCloud: any = ref(null);

const handleStart = () => {
    // RTC initialization
    const params = {
        baseUrl: 'https://xxxx.net', // SDK request domain, default https://openapi.armcloud.net
        token: '', // Required, token obtained from server by calling /openapi/open/token/stsToken
        retryCount: 2, // WebSocket reconnection attempts, default 2
        retryTime: 2000, // WebSocket reconnection interval, default 2000ms
        enableMicrophone: true, // Whether to enable the microphone
        enableCamera: true, // Whether to enable the camera
        deviceInfo: {
            padCode: 'AC22030020000', // Required, room ID
            userId: 'xxxx-xxxx-xxxx-xxx', // Required, unique ID from your product, such as user ID
            videoStream: { // Optional, specific configuration, refer to API details for resolution, bitrate, frame rate
                resolution: 12, // Optional, resolution, default 12 (540 * 960)
                frameRate: 2, // Optional, frame rate, default 2 (25fps)
                bitrate: 3 // Optional, bitrate, default 3 (2Mbps)
            },
            autoRecoveryTime: 300, // Optional, auto-recovery time with no operation, range 1s~7200s, default: 300
            mediaType: 3, // Optional, media stream type 1: audio only, 2: video only, 3: both audio and video, default: 3
            rotateType: 0, // Optional, screen orientation 0: portrait, 1: landscape, default: 0
            keyboard: 'pad', // Optional, current keyboard 'local': local keyboard, 'pad': cloud keyboard, default: 'pad'
            saveCloudClipboard: true, // Optional, whether to receive cloud clipboard callback true: receive, false: do not receive, default: true
        },
        viewId: "phoneBox", // Required, container ID
        callbacks: { // Optional, callback collection
            // Initialization callback
            onInit: ({ code, msg }) => {
                console.log("init:", code, msg);
     
                // Perform subsequent operations after successful initialization
                if (code === 0) {
                    // Check if the browser supports RTC service
                    const isSupported = await armCloud.value.isSupported();
                    if (!isSupported) {
                        showNotify({
                            type: "warning",
                            message: "This browser does not support RTC service"
                        });
                        return false;
                    }
                    // Join the room
                    armCloud.value.start();
                    
                }
            },
            // Connection successful callback
            onConnectSuccess: () => {
                showNotify({
                    type: "success",
                    message: "Successfully joined the room"
                });
                // Get SDK version
                const version = armCloud.value.version;
                console.log('Current SDK version:', version);
            },
            // Connection failure callback
            onConnectFail: ({ code, msg }) => {
                console.log("fail:", code, msg);
                showNotify({
                    type: "danger",
                    message: msg
                });
            },
            // Auto-recovery callback
            onAutoRecoveryTime: () => {
                console.log("Triggered auto-recovery callback");
                // After triggering auto-recovery, the SDK internally executes armCloud.value.stop(), leaving the room. Rejoining is required.
                showDialog({
                    message: "Auto-recovery triggered, stream paused; Click confirm to resume streaming"
                }).then(() => {
                    // on close
                    // Rejoin the room
                    armCloud.value.start();
                });
            },
            // Autoplay failure callback
            onAutoplayFailed: e => {
                console.log("Autoplay failed", e);
                if (e.kind === "video") {
                    showDialog({
                        message: "Autoplay video failed; Click confirm to play manually"
                    }).then(() => {
                        // on close
                        armCloud.value.startPlay();
                    });
                }
                if (e.kind === "audio") {
                    showDialog({
                        message: "Autoplay audio failed; Click confirm to play manually"
                    }).then(() => {
                        // on close
                        armCloud.value.startPlay();
                    });
                }
            },
            // Current runtime information callback
            onRunInformation: (info) => {
                console.log("Current network conditions", info);
            },
            // Triggered when resolution changes
            onChangeResolution: (width, height) => {
                console.log("Current resolution", width, height);
            },
            // Triggered when receiving a message passed from the cloud app
            onTransparentMsg: (type, msg) => {
                console.log("Transparent message", type, msg);
            },
            // Triggered when playback encounters an exception
            onErrorMessage: (event) => {
                console.log("Exception", event);
            },
            // Cloud machine text content copy callback
            onOutputClipper: (message) => {
                // If the configuration saveCloudClipboard: false is passed during initialization, callback messages cannot be received
                console.log("Copied text content", message);
            },
            // Video first frame rendered successfully
            onRenderedFirstFrame: () => {
                console.log("Video first frame rendered successfully");
            }
        }
    };
    armCloud.value = new ArmcloudEngine(params);
}

/** Leave the room */
const handleStop = () => {
    armCloud.value.stop();
}
</script>

<template>
  <div>
    <div
      id="phoneBox"
      class="phone-box"
      style="background-color: grey; overflow: hidden; width: 100%; height: 80%; text-align: center"
    />
    <div class="flex">
      <div @click="handleStart">Join Room</div>
      <div @click="handleStop">Leave Room</div>
    </div>
  </div>
</template>

```