---
title: Callback Function
---

## Listening to interfaces

| interface name                                                                | interface description |
|-------------------------------------------------------------------------------| ------------ |
| [ICloudCoreManagerStatusListener](#initialize-listener)                       | InitializeListener |
| [IPlayerListener](#pull-stream-playback-status-listening)                     | PullStreamPlayStateListener |
| [IStreamListener](#video-streaming-status-listening)                          | VideoStreamStatusListener |
| [StreamProfileChangeCallBack](#sharpness-switching-successful-listening)      | ClaritySwitchSuccessListener |
| [SetAutoRecycleTimeCallback](#no-action-recycling-service-duration-listening) | NoAutoRecycleServiceTimeListener |
| [ScreenShotCallBack](#screenshot-to-local-listener)                           | Screenshot to local listener |
| [LocationEventListener](#positioning-listener)                                | LocationEventListener |
| [IMessageReceiver](#message-passthrough-listener)                             | Message Passthrough Listener |
| [IUploadFileCallBack](#file-upload-listener)                                  | File upload monitoring |
| [IClipBoardListener](#cloud-clipboard-listening)                              | Cloud Clipboard Listener |
| [IExecuteAdbCommandCallback](#Execute-adb-command-result-monitoring)          | Execute adb command result monitoring |
| [IInjectVideoListener](#video-stream-injection-monitoring)                    | Video stream injection monitoring |

### Listening to interfaces

#### Initialize listener

**Interface:** ICloudCoreManagerStatusListener

**method:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onPrepared() | None | Initialization success callback |

#### Pull stream playback status listening

**Interface:** IPlayerListener

**method:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onPlaySuccess(videoStreamProfileId: Int) | videoStreamProfileId: the clarity of the current cloud phone screen, the callback is triggered when the first frame is rendered to the screen | Play Success |
| onError(code: Int, msg: String) | code: Error code; msg: Error content | Error messages |
| onWarning(code: Int, msg: String) | code: warning code; msg: warning content | warning message |
| onNetworkChanged(int type) | type: -1: Network connection type is unknown; 0: Network connection has been disconnected; 1: Network type is LAN; 2: Network type is Wi-Fi (including hotspot); 3: Network type is 2G mobile; 4: Network type is 3G mobile; 5: Network type is 4G mobile; 6: Network type is 5G mobile | Network type and status switch callback |
| onServiceInit(extras: Map<String, Any>?)  | extras: reserved parameters, used to passthrough some extra parameters | pre-join room callback, used to get and initialize each functional service, such as setting up various event listeners |
| networkQualityRtt(rtt: Int) | rtt: the time it takes for data to go back and forth between the client and the server | After joining a room and publishing or subscribing to a stream, report upstream and downstream network quality information for local users and subscribed remote users at a frequency of every 2 seconds |
| onMultiCloudPhoneJoin(padCode: String) | padCode: CloudPhoneIdentifier | CloudPhoneJoinGroupControlCallback in GroupControl state |
| onMultiCloudPhoneLeave(padCode: String) | padCode: CloudPhoneIdentifier | CloudPhoneLeaveGroupControlCallback in GroupControl state |

#### Video Streaming Status Listening

**Interface:** IStreamListener

**method:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onFirstAudioFrame(uid: String) | uid: remote instance video stream ID | subscribe to video stream to receive audio first frame callback |
| onRemoteAudio(byteBuffer: ByteBuffer)  | byteBuffer：Audio streaming data | Remote audio stream data |
| onFirstRemoteVideoFrame(uid: String, width: Int, height: Int) | uid: Remote Instance Video Stream ID <br> width: Width of the cloud machine frame <br> height: Height of the cloud machine frame | Subscribing to a video stream to receive the first frame of the video Callbacks |
| onVideoSizeChanged(uid: String, width: Int, height: Int) | uid: Remote Instance Video Stream ID <br> width: Width of the cloud machine screen <br> height: Height of the cloud machine screen | Callback when the width or height of the cloud machine screen changes |
| onStreamPaused() | None | call pause(), callback after pausing playback |
| onStreamConnectionStateChanged(state: Int) | state@ConnectionState: VideoStreamConnectionState | VideoStreamConnectionStateChangedCallback |
| onScreenRotation(rotationType: String) | rotationType: TYPE_LANDSCAPE(HORIZONTAL), TYPE_PORTRAIT(VERTICAL) | Callbacks for when a rotation occurs on the screen of the cloud machine |

#### Sharpness Switching Successful Listening

**Interface:** StreamProfileChangeCallBack

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onVideoStreamProfileChange(isSuccess: Boolean, from: String, current: String) | isSuccess: (whether or not the clarity was successfully switched) from: (the clarity before the switch) current: (the current clarity) | Clarity Switching Callbacks |

#### No Action Recycling Service Duration Listening

**Interface:** SetAutoRecycleTimeCallback

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onResult(autoRecycleTime: Int) | autoRecycleTime: no-operation recycling service duration Unit s | Callback this method after the no-operation duration reaches the set duration |

#### Screenshot to local listener

**Interface:** ScreenShotCallBack

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onScreenShot(bitmap: Bitmap) | bitmap: Bitmap of the screen of the intercepted cloud machine | Callback after calling screenshot to local |

#### Positioning listener

**Interface:** LocationEventListener

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onReceivedRemoteLocationRequest(requestOptions: RequestOptions) | options: location request options | Callback for receiving a location request for a cloud instance in manual location mode |
| onRemoteLocationRequestEnded() | None | In manual location mode, the cloud instance location request ended |
| onSentLocalLocation(locationInfo: LocationInfo) | locationInfo: LocationInfo | Callback after sending local device location information to the cloud instance in auto-location mode |
| onRemoteLocationUpdated(locationInfo: LocationInfo) | locationInfo: LocationInfo | Callback after cloud instance location update |

#### Message passthrough listener

**Interface:** IMessageReceiver

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onReceiveMessage(message: String) | message: the body of the passthrough message | passthrough message from the cloud machine |

#### File Upload Listener

**Interface:** IUploadFileCallBack

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onUpdateStart() | none | UploadStart |
| onUpdateByteChange(totalByte: Long, updateByte: Long) | totalByte: total file size updateByte: current upload size Unit: bytes | file upload size changed |
| onUpdateProgressChange(progress: Int) | progress: upload progress | File current upload progress change 0 ~ 100 |
| onUpdateSuccess(mapData: Map<String, String>) | mapData: reserved data | File upload success |
| onUpdateFail(e: Throwable?) | e: Error Message | File Upload Fail |
| onUpdateEnd() | None | End of file upload |
| onError(code: Int, msg: String) | code: Error code msg: Error message | File Upload Failed Error message other than file upload error |

#### Cloud Clipboard Listening

**Interface:** ICLipBoardListener

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onClipBoardMessageReceived(data: String) | data: Clipboard data synchronized by cloud machine | Clipboard data synchronized by cloud machine |

#### Execute adb command result monitoring

**Interface:** IExecuteAdbCommandCallback

**Methods:**

| Method Name  | Parameters  | Description |
| ------------ |-----------------------------------------------------------------------| ------------ |
| onExecuteAdbCommandResult(isSuccess: Boolean, content: String)  | isSuccess：Whether the execution is successful content： Execution Results | Execute adb command result monitoring |

#### Video stream injection monitoring

**Interface:** IInjectVideoListener

**Methods:**

| Method Name | Parameters | Description |
| ------------ | ------------ | ------------ |
| onInjectCallBack(code: String, content: String) | code: Code msg content: Injection result information | Video stream injection result monitoring |
| onVideoInjectInfo(@VideoInjectState state: Int, videoPath: String?) | state: Current injection state videoPath: Current injection video path | Current video injection information |

**VideoInjectState:**

| Field Name | Description |
| ------------ | ------------ |
| STATE_LIVE | Currently injecting |
| STATE_OFFLINE | Not injecting |
