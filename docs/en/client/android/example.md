---
title: Example Construction
order: 1
---

## Environment Requirements

```text
    Android 5.0 or higher version Android device or emulator
    IDE: Android Studio (latest version recommended)
    JDK version 1.8 or above
```

## Add Maven Repository Address

1.Configure the Maven repository address in the repositories section of the build.gradle file located in the project root directory, refer to the following example:

```groovy
    repositories {
          maven {
            url "https://maven.vmos.cn"
        }
    }
```

2.Introduce ArmCloudSDK in the project's build.gradle file:

```groovy
implementation "net.armcloud.armcloudsdk:armcloudsdkv3:1.1.2"
```

3.Set the Java version to 1.8, refer to the following example:

```groovy
android {
    // ...
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
```

## Permission Declaration

Declare the permissions required by the SDK in the AndroidManifest.xml file according to the actual scenario, refer to the following example:

```xml
<!-- Used to access the internet; internet access is required for network location -->
<uses-permission android:name="android.permission.INTERNET"/>
<!-- WiFi network status; usage scenario: monitoring changes in the user's mobile network status -->
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<!-- Used for network location -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
<!-- Used to access GPS location -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<!-- Used to collect audio information -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<!-- Used to collect video information -->
<uses-permission android:name="android.permission.CAMERA" />
<!-- Used for vibration control -->
<uses-permission android:name="android.permission.VIBRATE" />
<!--File read and write permissions for uploading files-->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<!--File management permissions (Android 11 (API level 30) and above) for uploading files-->
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
```

## Quick Start

#### Initialize ArmCloudEngine

Call the prepare method to initialize ArmCloudEngine:

```java
        ArmCloudEngine.prepare(this, object : ICloudCoreManagerStatusListener {
            override fun onPrepared() {
               Log.i("ArmCloudEngine", "Initialization completed")
            }
        })
```

#### Configure PhonePlayConfig

Configure the parameters required to start the cloud phone:

```java
        val builder: PhonePlayConfig.Builder = PhonePlayConfig.Builder()
        builder.context(this)
            .userId(dto.userId) // Required parameter: Custom client user ID
            .padCode(dto.padCode) // Required parameter: Cloud phone instance ID
            .token(dto.token) // Required parameter: Temporary authentication token
            .clientType(dto.clientType) // Required parameter: Client type
            .container(container) // Required parameter: Container to hold the display
            .enableMultiControl(isMultiControl) // Optional parameter: Whether to enable group control
            .setPadCodes(mChosePadCodes) // Optional parameter for the collection of group control device numbers
            .rotation(rotation) // Optional parameter for screen orientation, default is portrait
            .videoStreamProfileId(videoStreamProfileId) // Optional parameter for clarity level ID, default is high definition
            .enableGyroscopeSensor(enableGyroscopeSensor) // Optional parameter to turn on the gyroscope switch, default is false
            .enableVibrator(enableVibrator) // Optional parameter to enable local vibration switch, default is false
            .enableLocationService(enableLocationService) // Optional parameter to enable local location service switch, default is false
            .enableLocalKeyboard(enableLocalKeyboard) // Optional parameter to enable local keyboard switch, default is false
            .enableClipboardCloudPhoneSync(isEnable) // Optional parameter to enable cloud clipboard synchronization to the real device, default is true
            .enableClipboardLocalPhoneSync(isEnable) // Optional parameter to enable clipboard synchronization from the real device to the cloud device, default is true
            .enableCamera(isEnable) // Optional parameter to enable camera permissions, default is true
            .enableMic(isEnable) // Optional parameter to enable microphone permissions, default is false
            .streamType(streamType) // Optional parameter to specify the type of audio and video stream to pull when starting the cloud phone, default is to pull audio and video streams
            .videoRenderMode(renderMode) // Optional parameter to specify the video stream rendering mode. Default is proportional scaling centered mode.
            .videoRotationMode(videoRotationMode) // Optional parameter to specify the video rotation mode. Default is non-SDK handling of rotation.
            .autoRecycleTime(autoRecycleTime) // Optional parameter to specify the idle recycle time in seconds. Default is 300s.
        val phonePlayConfig: PhonePlayConfig = builder.build()
```

#### Start Cloud Phone

Call the start interface to launch the cloud phone:

```java
ArmCloudEngine.start(phonePlayConfig, IPlayerListener playerListener)
```
