---
title: Error Code
order: 3
---
| Name | Values | Description |
| ------------ | ------------ | ------------ |
| ERROR_START_GENERAL | 10000 | Failed to start the cloud phone. Reason: General error, please check the error message. Suggestion: Please check the config parameters for starting the cloud phone.  |
| ERROR_START_JOIN_ROOM | 10001 | Failed to start cloud phone. Reason: Failed to join room |
| ERROR_SERVICE | 10002 | SDK server-side error, view error message |
| ERROR_EMPTY_POD_CODE | 10003 | The cloud machine ID is empty |
| ERROR_MULTI_START_JOIN_ROOM | 10004 | Failed to join group control room |
| ERROR_FILE_PATH_EMPTY | 10020 | File path is empty |
| ERROR_FILE_UN_EXIST | 10021 | File does not exist |
| ERROR_FILE_UN_FILE | 10022 | Non-file type |
| ERROR_FILE_OVERSIZE | 10023 | File too large |
| ERROR_DISCONNECT | 10100 | Server disconnected |
| ERROR_SOCKET | 4000 | WebSocket error. Cause: Please check the error message |
| ERROR_SOCKET_DISCONNECT | 4001 | WebSocket connection disconnected |
| ERROR_RENDER | 4010 | View rendering failed. Cause: See error message |
| ERROR_MEDIA_NONE_VIDEO | 5000 | No video encoding capability |
| ERROR_CODE_INVALID_TOKEN | -1000 | The Token used to enter the room is invalid or expired. Requires the user to retrieve the token |
| WARNING_CODE_JOIN_ROOM_FAILED | -2001 | Room entry failed. When you enter a room for the first time or reconnect to a room due to poor network conditions, the room failed due to a server error, and the SDK will automatically retry to enter the room.  |
| WARNING_CODE_PUBLISH_STREAM_FAILED | -2002 | Failed to publish an audio/video stream. When you publish an audio/video stream in the room you are in, the publication fails due to a server error.The SDK automatically retries the publication |
| WARNING_CODE_SUBSCRIBE_STREAM_FAILED404 | -2003 | Subscription to audio/video stream failed. The subscription failed because the subscribed audio/video stream could not be found in the current room. the SDK will automatically retry the subscription, and if the subscription still fails, we recommend that you exit and retry |
| WARNING_CODE_SUBSCRIBE_STREAM_FAILED5XX | -2004 | Failed to subscribe to an audio/video stream. When you subscribe to an audio/video stream in your room, the subscription fails due to a server error.The SDK automatically retries the subscription.  |
| WARNING_CODE_OLD_ROOM_BEEN_REPLACED | -2016 | A room with the same roomId already exists, and the newly created room instance has replaced the old room instance.  |
| WARNING_CODE_NO_CAMERA_PERMISSION | -5001 | Camera permission is abnormal, the current application does not have access to camera permissions.  |
| WARNING_CODE_INVALID_SAMI_APPKEY_OR_TOKEN | -7002 | Audio Technology SDK authentication failed. Contact technical support.  |
| WARNING_CODE_GET_ROOM_FAILED | -2000 | Failed to get room info warning |
| WARNING_CODE_ROOM_ID_ALREADY_EXIST | -2015 | A room with the same roomId already exists.  |
| WARNING_CODE_NO_MICROPHONE_PERMISSION | -5002 | Microphone permission is abnormal, the current application has not acquired microphone permission.  |
| WARNING_CODE_RECODING_DEVICE_START_FAILED | -5003 | Failed to start the audio capture device. Failed to start the audio capture device, the current device may be occupied by another application.  |
| WARNING_CODE_PLAYOUT_DEVICE_START_FAILED | -5004 | Audio Playback Device Startup Failure Warning. May be due to insufficient system resources, or incorrect parameters. |
| WARNING_CODE_NO_RECORDING_DEVICE | -5005 | No available audio capture device. Failed to start audio capture device, please insert an available audio capture device.  |
| WARNING_CODE_NO_PLAYOUT_DEVICE | -5006 | No available audio playback device. Failed to start audio playback device, please insert an available audio playback device.  |
| WARNING_CODE_RECORDING_SILENCE | -5007 | The current audio device is not capturing valid sound data, please check to replace the audio capture device.  |
