---
title: Interface Description
---

# API interface description

This document describes the interfaces provided by the Cloud Phone Android client SDK. <br>
You can also refer to the [sample project](/ArmCloudSdkDemo_android.zip) for a more complete project implementation.

## API Overview

### Initialization

| method name               | method description |
|-------------------------------------------------------| ------------ |
| [prepare](./android-sdk.md#initializing-armcloudengine) | Initialize ArmCloudEngine |

### Status

| variable name | variable description |
| ------------ | ------------ |
| [engineState](./android-sdk.md#get-engine-status) | Get current engine state |

### Life cycle

| method name | method description |
| ------------ | ------------ |
| [start](./android-sdk.md#starting-the-cloud-machine) | Start cloud machine |
| [resume](./android-sdk.md#resume-pulling-streams-from-the-cloud-machine) | Resume pulling streams from the cloud machine |
| [pause](./android-sdk.md#suspend-pulling-streams-from-the-cloud-machine) | Pause pulling streams from the cloud machine |
| [stop](./android-sdk.md#stop-cloud-machine) | Stop cloud machine |
| [onRequestPermissionsResult](./android-sdk.md#permission-processing) | PermissionAutomation |

### Functional control

| method name                          | method description |
|--------------------------------------------------------------------------------------| ------------ |
| [screenShot](./android-sdk.md#screenshot)                | Screenshot |
| [videoStreamProfileId](./android-sdk.md#setting-the-cloud-machine-clarity)     | Set Cloud Camera Clarity |
| [rotate](./android-sdk.md#rotate-the-orientation-of-the-local-screen)      | Rotate native screen orientation |
| [muteAudio](./android-sdk.md#whether-to-disable-audio)           | Whether to disable audio |
| [muteVideo](./android-sdk.md#whether-to-disable-video)           | Whether to disable video |
| [enableLocalKeyboard](./android-sdk.md#whether-to-enable-the-local-keyboard)     | Whether to enable local keyboard |
| [enableLocationService](./android-sdk.md#whether-to-enable-the-positioning-function)   | Whether to enable location service |
| [enableGyroscopeSensor](./android-sdk.md#whether-to-enable-the-gyroscope-function)   | Whether to enable gyroscope functionality |
| [enableAccelerometerSensor](./android-sdk.md#whether-to-enable-gravity-sensing)   | Whether to enable gravity sensor |
| [enableVibrator](./android-sdk.md#whether-to-enable-the-vibration-function)        | Whether to enable vibrator |
| [enableCamera](./android-sdk.md#camera-access)            | Camera permissions |
| [enableMic](./android-sdk.md#microphone-privileges)           | Microphone permissions |
| [setLocationServiceMode](./android-sdk.md#setting-the-positioning-mode)         | SetLocationMode |
| [getLocationServer](./android-sdk.md#get-location-services)          | GetLocationServer |
| [getClipBoardServiceManager](./android-sdk.md#get-clipboard-controller)      | GetClipBoardController |
| [switchCamera](./android-sdk.md#switching-front-and-rear-cameras)         | Switch Front and Rear Cameras |
| [sendKeyEvent](./android-sdk.md#send-analog-key-events)           | Send analog key event |
| [autoRecycleTime](./android-sdk.md#setting-the-no-operation-recycling-service-time)    | Set no-operation-recycle-service-time Default 300s |
| [getAutoRecycleTime](./android-sdk.md#get-no-operation-recovery-service-time)          | GetAutoRecycleTime |
| [streamType](./android-sdk.md#setting-the-pull-stream-type)            | Set pull stream type: pull video/audio |
| [updateVideoRenderMode](./android-sdk.md#updated-video-stream-rendering-mode)        | UpdateVideoRenderMode |
| [setVideoRotationMode](./android-sdk.md#setting-the-video-rotation-mode)        | SetVideoRotationMode |
| [getStatus](./android-sdk.md#get-current-playback-status)          | Get current playback status |
| [getMessageChannel](./android-sdk.md#get-message-passthrough-controller)     | GetMessagePassthroughController |
| [getUploadManager](./android-sdk.md#get-file-upload-controller)            | GetUploadManager |
| [getLocalInputManager](./android-sdk.md#getting-local-keyboards-and-cloud-machine-application-controllers) | Get Local Keyboard and Cloud App Controller |
| [startCaptureVideo](./android-sdk.md#start-local-video-capture) | Start local video capture |
| [startCaptureAudio](./android-sdk.md#start-local-audio-capture) | Start local audio capture |
| [stopCaptureVideo](./android-sdk.md#stop-local-video-capture) | Stop local video capture |
| [stopCaptureAudio](./android-sdk.md#stop-local-audio-capture) | Stop local audio capture |
| [startInjectVideoStream](./android-sdk.md#start-video-injection) | Start video injection |
| [stopInjectVideoStream](./android-sdk.md#stop-video-injection) | Stop video injection |
| [getVideoInjectState](./android-sdk.md#get-video-injection-status) | Get video injection status |

### Setting up the listener

| method name                                      | method description |
|------------------------------------------------------------------------------------------------------------------------------| ------------ |
| [setStreamProfileChangeListener](./android-sdk.md#setting-the-clarity-switch-callback-listener)          | Set the definition switching callback listener |
| [setAutoRecycleTimeCallback](./android-sdk.md#setting-the-callback-listener-for-the-no-operation-recycling-service-duration) | Set the callback listener for the no-operation recycling service duration |
| [setScreenShotCallBack](./android-sdk.md#setting-up-the-local-screenshot-callback-listener)            | Set local screenshot callback listener |
| [setLocationEventListener](./android-sdk.md#setting-up-the-positioning-callback-listener)            | Set the positioning callback listener |
| [setExecuteAdbCommandCallback](./android-sdk.md#Set-a-callback-for-the-result-of-executing-an-Adb-command)       | Set a callback for the result of executing an Adb command |
| [setInjectVideoListener](./android-sdk.md#set-video-injection-callback-listener) | Set video injection callback listener |

## API details

### Initialization

#### Initializing ArmCloudEngine

**method:**

```kotlin
ArmCloudEngine.prepare(mContext: Application, cloudCoreManagerStatusListener: ICloudCoreManagerStatusListener)
```

**parameter:**
mContext : application context object
cloudCoreManagerStatusListener: Initialization Callbacks

**Example code:**

```kotlin
class MyApp : Application() {

override fun onCreate() { 
  super.onCreate()
    ArmCloudEngine.prepare(this, object : ICloudCoreManagerStatusListener {
    override fun onPrepared() {
      Log.i("ArmCloudEngine", "Initialization complete")
    }
    })
  } 
}
```

### Status

#### Get Engine Status

**Variable name:**

```kotlin
ArmCloudEngine.engineState
```

**Variable Description:**
Type: int@EngineState
Description: Current engine status
EngineState.

| field name | description |
| ------------ | ------------ |
| STATE_AVAILABLE | Engines available |
| STATE_UN_AVAILABLE | Engine not available |

Note: Before calling the start method, make sure that the engine state is available.

### Life cycle

#### Starting the Cloud Machine

**method:**

```kotlin
ArmCloudEngine.start(config PhonePlayConfig, playerListener IPlayerListener)
```

**parameter:**
config : initial configuration information
playerListener: pull stream playback status callback listener

The config field is described below:

| Field Name | Field Type | Required or not | Field Description |
| ------------ | ------------ | ------------ | ------------ |
| context | FragmentActivity | Yes | current Activity context |
| token | String | Yes | Request room join credentials |
| userId | String | Yes | userid |
| clientType | String | Yes | clientType |
| padCode | String | Yes | Cloud Push Streaming Code |
| container | ViewGroup | Yes | Screen Display Container |
| enableMultiControl | boolean | No | Whether to enable group control |
| padCodes | List | No | Group Control Cloud Machine Number Collection |
| videoStreamProfileId | int | No | sharpness default hd |
| enableGyroscopeSensor | boolean | No | Whether to enable sensor functionality |
| enableAccelerometerSensor | boolean | No | Whether to enable gravity sensor function |
| enableVibrator | boolean | No | Whether to enable vibrator |
| enableLocationService | boolean | No | Whether or not to enable the location service feature |
| enableLocalKeyboard | boolean | No | Whether to enable local keyboard functionality |
| enableClipboardCloudPhoneSync | boolean | No | Whether or not to enable cloud phone clipboard synchronization to the real phone |
| enableClipboardLocalPhoneSync | boolean | No | Whether to enable clipboard synchronization from real phone to cloud |
| enableCamera | boolean | No | Whether to enable camera privileges |
| enableMic | boolean | No | Whether to enable microphone privileges |
| autoRecycleTime | int | No | Timeout autoStopServiceTime Default 300s |
| streamType | String@StreamType | No | Pull Stream Type Default Pull Audio/Video |
| remoteLocationMock | LocationInfo | No | Location Information |
| rotation | String@ScreenRotationType | No | Native Screen Rotation Orientation Default portrait |
| videoRenderMode | int@VideoRenderMode | No | Video Stream Render Mode Default Adaptive |
| videoRotationMode | int@VideoRotationMode | No | ScreenRotationMode Default external processing rotation |
| streamListener | IStreamListener | No | Video Streaming Listener |

**Field type information:**
StreamType:

| field name | description |
| ------------ | ------------ |
| TYPE_VIDEO | Video |
| TYPE_AUDIO | Audio |
| TYPE_BOTH | Audio And Video |

ScreenRotationType:

| field name | description |
| ------------ | ------------ |
| TYPE_LANDSCAPE | Horizontal screen |
| TYPE_PORTRAIT | Vertical screen |

VideoRenderMode:

| field name | description |
| ------------ | ------------ |
| VIDEO_RENDER_MODE_COVER | The short side of the screen holds up the container |
| VIDEO_RENDER_MODE_FIT | The screen is isometrically scaled and centered for display on Container |
| VIDEO_RENDER_MODE_FILL | Stretches the frame non-isometrically and fills the Container |

VideoRotationMode:

| field name | description |
| ------------ | ------------ |
| EXTERNAL | EXTERNAL External Rotation, default; in this mode, user needs to listen to IStreamListener#onRotation(int) callback to adjust the page orientation externally to synchronize the screen orientation with the instance of the cloud phone |
| INTERNAL | INTERNAL, internal rotation; in this mode, the SDK internally handles the orientation of the screen, and there is no need to do any external processing of the screen orientation |

**Streaming ConnectionState :**

| Field Names | Values | Descriptions |
| ------------ | ------------ | ------------ |
| CONNECTION_STATE_DISCONNECTED | 1 | If the connection is disconnected for more than 12 seconds, the SDK will try to reconnect automatically. |
| CONNECTION_STATE_CONNECTING | 2 |First request to establish a connection. Connecting. |
| CONNECTION_STATE_CONNECTED | 3 | The first connection was successful. |
| CONNECTION_STATE_RECONNECTING | 4 | The following situations are covered: When connecting for the first time, the connection fails within 10 seconds; After the connection is successful, the connection is disconnected for 10 seconds. Automatic reconnection is in progress.|
| CONNECTION_STATE_RECONNECTED | 5 | After the connection was disconnected, the reconnection was successful. |
| CONNECTION_STATE_LOST | 6 |The device is in the CONNECTION_STATE_DISCONNECTED state for more than 10 seconds and the reconnection attempt fails. The SDK will continue to try to reconnect. |
| CONNECTION_STATE_FAILED | 7 | Connection failed. The server status is abnormal. The SDK will not reconnect automatically. Please log in again or contact technical support.  |

**P2P ConnectionState :**

| Field Names | Values | Descriptions |
| ------------ | ------------ | ------------ |
| STATE_NEW | 10 |  Initialization |
| STATE_CHECKING | 11 |  Connection in progress |
| STATE_CONNECTED | 3 |  Successful connection |
| STATE_FAILED | 7 |  Connection Failed |
| STATE_COMPLETED | 14 |  Connection completed.  |
| STATE_DISCONNECTED | 15 |  Connection Disconnected |
| STATE_CLOSED | 16 |  Connection closed |

**Example code:**

```kotlin
val builder: PhonePlayConfig.Builder = PhonePlayConfig.Builder()
builder.context(this)
  .userId(dto.userId) //required parameter Customized client user IDs
  .padCode(dto.padCode) // mandatory parameter, cloud phone instance ID
  .token(dto.token) // mandatory parameter, temporary authentication token
  .clientType(dto.clientType) //required parameter clientType
  .container(container) // mandatory parameter, the container to hold the frame, parameter description: layout needs to be a FrameLayout or a subclass of FrameLayout.
  .enableMultiControl(isMultiControl) // Optional parameter Whether to enable MultiControl
  .setPadCodes(mChosePadCodes) // Optional parameter Group control device number set
  .rotation(rotation) // optional parameter screen vertical/horizontal Default vertical/vertical
  .videoStreamProfileId(
    videoStreamProfileId.definition,
    videoStreamProfileId.frameRate,
    videoStreamProfileId.bitrate
  ) // Optional parameter, Sharpness ID Default HD
  .enableGyroscopeSensor(enableGyroscopeSensor) // optional parameter turn on gyroscope switch default false
  .enableAccelerometerSensor(enableAccelerometerSensor) // optional parameter to turn on gravity sensing default false
  .enableVibrator(enableVibrator) // optional parameter to turn on local vibration switch default false
  .enableLocationService(enableLocationService) // optional parameter switch on local location function default false
  .enableLocalKeyboard(enableLocalKeyboard) // optional parameter enable local keyboard switch default false
  .enableClipboardCloudPhoneSync(enableCloudSync) // Optional parameter Turn on cloud phone clipboard sync to real phone Default true
  .enableClipboardLocalPhoneSync(enableLocalSync) // Optional parameter Enable clipboard synchronization from real phone to cloud phone Default true
  .enableCamera(enableCamera) // Optional parameter Enable camera permissions Default true
  .enableMic(enableMic) // Optional parameter Enable microphone privileges Default false
  .streamType(streamType) // Optional parameter Specifies the type of audio/video stream to pull when launching the cloud phone Default pulls audio/video streams
  .videoRenderMode(renderMode) // Optional parameter Specify the video stream rendering mode Default isoscale centered mode
  .videoRotationMode(videoRotationMode) // Optional parameter Specifies the video rotation mode.
  .autoRecycleTime(autoRecyclerTime.toInt())
  // Get audio/video stream information callback listener
  .streamListener(object : IStreamListener {
    override fun onFirstAudioFrame(uid: String) {
    }

    override fun onFirstRemoteVideoFrame(uid: String, width: Int, height: Int) {
    }
    
    override fun onRemoteAudio(byteBuffer: ByteBuffer) {
    }

    override fun onVideoSizeChanged(uid: String, width: Int, height: Int) {
    }

    override fun onStreamPaused() {
    }

    override fun onStreamConnectionStateChanged(state: Int, reason: Int) {
    }

    override fun onScreenRotation(rotationType: Int) {
    }
  })
  
ArmCloudEngine.start(builder.build(), object : IPlayerListener {
  override fun onPlaySuccess(videoStreamProfileId: Int) {
    initListener()
  }

  override fun onError(code: Int, msg: String) {
  }

  override fun onWarning(code: Int, msg: String) {
  }

  override fun onNetworkChanged(var1: Int) {
  }

  override fun onServiceInit(extras: Map<String, Any>?) {
  }

  override fun networkQualityRtt(rtt: Int) {
  }

  override fun onMultiCloudPhoneJoin(padCode: String) {
  }

  override fun onMultiCloudPhoneLeave(padCode: String) {
  }
})
```

#### Resume pulling streams from the cloud machine

**method:**

```kotlin
ArmCloudEngine.resume()
```

**Code example:**

```kotlin
override fun onResume() {
  super.onResume()
  ArmCloudEngine.resume()
}
```

#### Suspend pulling streams from the cloud machine

**method:**

```kotlin
ArmCloudEngine.pause()
```

**Code example:**

```kotlin
override fun onPause() {
  super.onPause()
  ArmCloudEngine.pause()
}
```

#### Stop Cloud Machine

**method:**

```kotlin
ArmCloudEngine.stop()
```

**Code example:**

```kotlin
override fun onDestroy() {
  super.onDestroy()
  ArmCloudEngine.stop()
}
```

#### Permission Processing

**method:**

```kotlin
ArmCloudEngine.onRequestPermissionsResult(requestCode, permissions, grantResults)
```

**Code example:**

```kotlin
override fun onRequestPermissionsResult(
  requestCode: Int,
  permissions: Array<String?>,
  grantResults: IntArray
) {
  super.onRequestPermissionsResult(requestCode, permissions, grantResults)
  ArmCloudEngine.onRequestPermissionsResult(requestCode, permissions, grantResults)
}
```

### Functional control

#### Screenshot

**method:**

```kotlin
ArmCloudEngine.screenShot(@ScreenShotType screenShotType: Int)
```

**parameter:**
screenShotType: Screenshot type

| field name | description |
| ------------ | ------------ |
| TYPE_LOCAL | Screenshot to Local Machine |
| TYPE_CLOUD | Screenshot to Cloud Machine |

#### Setting the Cloud Machine Clarity

**Method 1:**

```kotlin
ArmCloudEngine.videoStreamProfileId(videoDefinitionEnum: VideoDefinitionEnum)
```

**parameter:**
VideoDefinitionEnum :

| field name | description |
| ------------ | ------------ |
| DEFINITION_B | Blu-Ray |
| DEFINITION_S | Ultra-HD |
| DEFINITION_H | High Definition |
| DEFINITION_L | SD |

**Method 2:**

```kotlin
ArmCloudEngine.videoStreamProfileId(definition: String, frameRateId: String, bitrateId: String)
```

**parameter:**
definition: Clarity
frameRateId : frame rate
bitrateId ： code rate

| field name | description |
| ------------ | ------------ |
| Sharpness | 07: 144&times;256<br> 08: 216&times;384<br> 09: 288&times;512<br> 10: 360&times;640<br> 11: 480&times;848<br>12: 540&times;960<br>13: 600&times;1024<br>14: 480&times;1280<br>15: 720&times;1280<br>16: 720&times;1920<br>17: 1080&times;1920<br>18: 1440&times;1920<br>19: 1600&times;2560<br>20: 2880&times;1080 |
| Frame Rate | 1: 20fps<br>2: 25fps<br>3: 30fps<br>4: 60fps<br>5: 1fps<br>6: 5fps<br>7: 10fps<br>8: 15fps<br>9: 2fps |
| Code Rate | 01: 1Mbps<br>02: 1.5Mbps<br>03: 2Mbps<br>04: 2.5Mbps<br>05: 3Mbps<br>06: 3.5Mbps<br>07: 4Mbps<br>08: 5Mbps<br>09: 6Mbps<br>10: 8Mbps<br>11: 10Mbps<br>12: 12Mbps<br>13: 200kbps<br>14: 400kbps<br>15: 600kbps |

#### Rotate the orientation of the local screen

**method:**

```kotlin
ArmCloudEngine.rotate(ScreenRotationType.TYPE_LANDSCAPE)
```

**parameter:**
Same as PhonePlayConfig to set the direction of rotation of the camera.

#### Whether to disable audio

**method:**

```kotlin
ArmCloudEngine.muteAudio(isMetuAudio)
```

**parameter:**
isMetuAudio : whether to disable audio
true: Disable
false: Enable

#### Whether to disable video

**method:**

```kotlin
ArmCloudEngine.muteVideo(isMetuVideo)
```

**parameter:**
isMetuVideo : whether to disable video
true: Disable
false: Enable

#### Whether to enable the local keyboard

**method:**

```kotlin
ArmCloudEngine.enableLocalKeyboard(isEnable)
```

**parameter:**
isEnable : whether to enable the local keyboard
true: Enable
false: Disable

#### Whether to enable the positioning function

**method:**

```kotlin
ArmCloudEngine.enableLocationService(isEnable)
```

**parameter:**
isEnable : whether to enable the positioning function
true: Enable
false: Disable

#### Whether to enable the gyroscope function

**method:**

```kotlin
ArmCloudEngine.enableGyroscopeSensor(isEnable)
```

**parameter:**
isEnable : Whether the sensor function is enabled
true: Enable
false: Disable

#### Whether to enable gravity sensing

**method:**

```kotlin
ArmCloudEngine.enableAccelerometerSensor(isEnable)
```

**parameter:**
isEnable : Whether to enable gravity sensing or not
true: Enable
false: Disable

#### Whether to enable the vibration function

**method:**

```kotlin
ArmCloudEngine.enableVibrator(isEnable)
```

**parameter:**
isEnable : Whether the vibration function is enabled or not
true: Enable
false: Disable

#### Camera Access

**method:**

```kotlin
ArmCloudEngine.enableCamera(isEnable)
```

**parameter:**
isEnable : Whether to enable camera privileges
true: Enable
false: Disable

#### microphone privileges

**method:**

```kotlin
ArmCloudEngine.enableMic(isEnable)
```

**parameter:**
isEnable : whether to enable microphone privileges
true: Enable
false: Disable

#### Setting the positioning mode

**method:**

```kotlin
ArmCloudEngine.setLocationServiceMode(mode@LocationMode)
```

**parameter:**
mode: Positioning mode

| field name | description |
| ------------ | ------------ |
| MODE_AUTO | Auto Mode: SDK Auto Acquisition Positioning |
| MODE_MANUAL | Manual Mode: set setLocationEventListener(locationEventListener: LocationEventListener) listener Manually pass in location via remoteLocationMock |

#### Get Location Services

**method:**

```kotlin
ArmCloudEngine.getLocationServer()
```

**Return value:**
Type: ILocationService
Description: Location Services

**ILocationService**

| Methods | Parameters | Descriptions |
| ------------ | ------------ | ------------ |
| enableLocationService(isEnable: Boolean) | isEnable: whether to enable | whether to enable location |
| remoteLocationMock( @FloatRange(from = -90.0, to = 90.0) latitude: Double, @FloatRange(from = -180.0, to = 180.0) longitude: Double) | latitude: latitude & lt;br> longitude: Longitude | Set location information |
|remoteLocationMock( @FloatRange(from = -90.0, to = 90.0) latitude: Double, @FloatRange(from = -180.0, to = 180.0) longitude: Double, altitude. Double = 0.0, bearing: Float = 0.0f, accuracy: Float = 0.0f, speed: Float = 0.0f, time: Long = System.currentTimeMillis(), elapsedRealtimeNanos: Long = SystemClock.uptimeMillis(), satellites: Int = 0) | latitude: latitude <br> longitude: longitude <br> altitude: altitude <br> bearing: azimuth <br> accuracy: Horizontal accuracy radius in meters <br> speed: Speed at this location in meters per second <br> time: Unix epoch time of the fix at this location <br> elapsedRealtimeNanos: Time of the fix in real-time nanoseconds elapsed since the system was booted. <br> satellites: number of satellites | set location info |

#### Get Clipboard Controller

**method:**

```kotlin
ArmCloudEngine.getClipBoardServiceManager()
```

**Return value:**
Type: ICLipBoardServiceManager
Description: Clipboard Service

**IClipBoardServiceManager**

| Methods | Parameters | Descriptions |
| ------------ | ------------ | ------------ |
| setBoardSyncClipListener(iClipBoardListener) | iClipBoardListener@IClipBoardListener: CloudMachineClipboardDataListening | set CloudMachineClipboardDataListening |
| sendClipBoardMessage(data: String) | data: text data | manually send clipboard data |
| enableClipboardLocalPhoneSync(isEnable: Boolean) | isEnable: whether to enable | Whether to enable ClipboardLocalPhoneSync cloud machine |
| enableClipboardCloudPhoneSync(isEnable: Boolean) | isEnable: whether to enable | Whether to enable clipboard cloud phone sync to real phone |

#### Switching Front and Rear Cameras

**method:**

```kotlin
ArmCloudEngine.switchCamera(isBack)
```

**parameter:**
isBack: whether the camera is back
true: Post
false: front

#### Send analog key events

**method:**

```kotlin
ArmCloudEngine.sendKeyEvent(systemKeyStrokeEnum SystemKeyStrokeEnum)
```

**parameter:**
systemKeyStrokeEnum: Keystroke enumeration

| Name of the enumeration | Description |
| ------------ | ------------ |
| BACK | Return button |
| HOME | Home |
| JOB | Taskbar |
| MENU | Menus |
| VOLUME_ADD | Volume up |
| VOLUME_LESS | Volume reduction |

#### Setting the no-operation recycling service time

**method:**

```kotlin
ArmCloudEngine.autoRecycleTime(recycleTime)
```

**parameter:**
recycleTime: no-operation recycle service time in s

#### Get no-operation recovery service time

**method:**

```kotlin
ArmCloudEngine.getAutoRecycleTime()
```

**Return value:**
Type: int
Description: No-operation recovery service time Unit s

#### Setting the pull stream type

**method:**

```kotlin
ArmCloudEngine.streamType(streamType)
```

**parameter:**
Same as PhonePlayConfig to set the pull stream type

#### Updated video stream rendering mode

**method:**

```kotlin
ArmCloudEngine.updateVideoRenderMode(renderMode)
```

**parameter:**
Same as PhonePlayConfig to set the video stream rendering mode

#### Setting the Video Rotation Mode

**method:**

```kotlin
ArmCloudEngine.setVideoRotationMode(rotationMode)
```

**parameter:**
Same as PhonePlayConfig to set the video rotation mode

#### Get current playback status

**method:**

```kotlin
ArmCloudEngine.getStatus()
```

**Return value:**
Type: int@CloudPhoneState
Description: Current playback status
CloudPhoneState .

| field name | description |
| ------------ | ------------ |
| STATE_UN_INIT | 0x0001 Uninitialized |
| STATE_INIT_SUCCESS | 0x0002 Initialization successful |
| STATE_START | 0x0003 Calling a pull stream |
| STATE_RUN | 0x0004 Successful stream pulling |
| STATE_STOP | 0x0005 Stop pulling stream |

#### Get Message Passthrough Controller

**method:**

```kotlin
ArmCloudEngine.getMessageChannel()
```

**Return value:**
Type: IMessageChannel
Description: Message Passthrough Controller

**IMessageChannel**
**Methods:**

| Methods | Parameters | Descriptions |
| ------------ | ------------ | ------------ |
| setMessageListener(listener: IMessageReceiver) | listener@IMessageReceiver: IMessageReceiver: message pass-through listener | Set up a message pass-through listener to call back messages passed from the cloud to the real machine |
| sendMessage(type: Int, data: String, binderService: String, packageName: String) | type: Message type <br> data: Message body <br> binderService: Remote Service name <br> packageName: Package name | Sends a passthrough message to the specified service in the cloud |

#### Get File Upload Controller

**method:**

```kotlin
ArmCloudEngine.getUploadManager(application)
```

**Parameters:**
application : global context

**Return value:**
Type: IUploadFileManager
Description: File Upload Controller

**IUploadFileManager**
**Methods:**

| Methods | Parameters | Descriptions |
| ------------ | ------------ | ------------ |
| uploadFile(lifecycleOwner: LifecycleOwner, padCode: String, token: String, path: String, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack ) | lifecycleOwner: lifecycle owner <br> padCode: cloud machine ID <br> token: temporary authentication token <br> path: file path <br> uploadFilePath@UploadFilePath: path to the cloud machine where the file will be saved <br> uploadFileCallBack@IUploadFileCallBack: file upload listener | File upload with lifecycle |
| uploadFile(lifecycleOwner: LifecycleOwner, padCode: String, token: String, file: File, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack ) | lifecycleOwner: lifecycle owner <br> padCode: cloud machine ID <br> token: temporary authentication token <br> file: file to be uploaded <br> uploadFilePath@UploadFilePath: path to the cloud machine where the file will be saved <br> uploadFileCallBack@IUploadFileCallBack: file upload listener | File upload with lifecycle |
| uploadFile(padCode: String, token: String, path: String, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack ) | padCode: Cloud Machine ID <br> token: temporary authentication token <br> path: file path <br> uploadFilePath@UploadFilePath: path to the cloud machine where the file will be saved <br> uploadFileCallBack@IUploadFileCallBack: File upload listener | File upload |
| uploadFile(padCode: String, token: String, file: File, uploadFilePath: String, uploadFileCallBack: IUploadFileCallBack ) | padCode: cloud machine ID <br> token: temporary authentication token <br> file: file to be uploaded <br> uploadFilePath@UploadFilePath: path to the cloud machine where the file is stored <br> uploadFileCallBack@IUploadFileCallBack: file upload listener | File upload |
| cancelUpload(path: String) | path: File path | CancelUpload |

**UploadFilePath:**

| field name | description |
| ------------ | ------------ |
| PATH_DCIM | DCIM directory |
| PATH_DOCUMENTS | Documents directory |
| PATH_DOWNLOAD | Download directory |
| PATH_MOVIES | Movies directory |
| PATH_MUSIC | Music directory |
| PATH_PICTURES | Pictures directory |

#### Getting Local Keyboards and Cloud Machine Application Controllers

**method:**

```kotlin
ArmCloudEngine.getLocalInputManager()
```

**Return value:**
Type: ILocalInputManager
Description: Get local keyboard and cloud application controller

**ILocalInputManager**
**Methods:**

| Methods | Parameters | Descriptions |
| ------------ | ------------ | ------------ |
| sendInputText(text: String) | text: MessageContent | Send Message to Cloud Machine Input Box |

#### Start Local Video Capture

**method:**

```kotlin
ArmCloudEngine.startCaptureVideo()
```

#### Start Local Audio Capture

**method:**

```kotlin
ArmCloudEngine.startCaptureAudio()
```

#### Stop Local Video Capture

**method:**

```kotlin
ArmCloudEngine.stopCaptureVideo()
```

#### Stop Local Audio Capture

**method:**

```kotlin
ArmCloudEngine.stopCaptureAudio()
```

#### Start Video Injection

**method:**

```kotlin
ArmCloudEngine.startInjectVideoStream(path: String, isLoop: Boolean, fileName: String?)
```

**Parameters:**

| Parameter Name | Type | Required | Description |
| ------------ | ------------ | ------------ | ------------ |
| path | String | Yes | Video path: <br> url / video file path in cloud machine |
| isLoop | Boolean | Yes | Whether to loop |
| fileName | String | No | File name: <br> required when path is url type |

**Video Requirements:**

| Name | Requirements |
| ------------ | ------------ |
| Supported video formats | H.264, H.265 encoding |
| Video resolution | Width and height must be multiples of 2 |

#### Stop Video Injection

**method:**

```kotlin
ArmCloudEngine.stopInjectVideoStream()
```

#### Get Video Injection Status

**method:**

```kotlin
ArmCloudEngine.getVideoInjectState()
```

**Description：**
The video injection status callback is obtained through @IInjectVideoListener.

### Event listener

#### Setting the Clarity Switch Callback Listener

**method:**

```kotlin
setStreamProfileChangeListener(streamProfileChangeCallBack StreamProfileChangeCallBack)
```

**parameter:**
streamProfileChangeCallBack ： Sharpness Change Callback Interface

**Example code:**

```kotlin
ArmCloudEngine.setStreamProfileChangeListener(object:StreamProfileChangeCallBack{
  override fun onVideoStreamProfileChange(
    isSuccess: Boolean,
    from: String,
    current: String
  ) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Clarity switch succeeded from: $from to: $current",
      Toast.LENGTH_SHORT
    ).show()
    }
  }
})
```

#### Setting the callback listener for the no-operation recycling service duration

**method:**

```kotlin
setAutoRecycleTimeCallback(autoRecycleTimeCallback SetAutoRecycleTimeCallback)
```

**parameter:**
autoRecycleTimeCallback ： No operation to recycle the service time callback

**Example code:**

```kotlin
ArmCloudEngine.setAutoRecycleTimeCallback(object : SetAutoRecycleTimeCallback {
  override fun onResult(autoRecycleTime: Int) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "No operation recycle service callback Timeout: $autoRecycleTime",
      Toast.LENGTH_SHORT
    ).show()
    }
  }
})
```

#### Setting up the local screenshot callback listener

**method:**

```kotlin
setScreenShotCallBack(screenShotCallBack: ScreenShotCallBack)
```

**parameter:**
ScreenShotCallBack ： Local screenshot listener

**Example code:**

```kotlin
ArmCloudEngine.setScreenShotCallBack(object : ScreenShotCallBack {
  override fun onScreenShot(bitmap: Bitmap) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Screenshot locally fetched: ${bitmap.byteCount}",
      Toast.LENGTH_SHORT
    ).show()
    }
  }
})
```

#### Setting up the positioning callback listener

**method:**

```kotlin
setLocationEventListener(locationEventListener: LocationEventListener)
```

**parameter:**
locationEventListener ： Location Listener

**Example code:**

```kotlin
ArmCloudEngine.setLocationEventListener(object: LocationEventListener {

  override fun onReceivedRemoteLocationRequest(requestOptions: RequestOptions) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Cloud machine request to turn on localization",
      Toast.LENGTH_SHORT
    ).show()
    }
  }

  override fun onRemoteLocationRequestEnded() {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Cloud machine requesting to stop localization",
      Toast.LENGTH_SHORT
    ).show()
    }
  }

  override fun onSentLocalLocation(locationInfo: LocationInfo) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Local Send Locator",
      Toast.LENGTH_SHORT
    ).show()
    }
  }

  override fun onRemoteLocationUpdated(locationInfo: LocationInfo) {
    runOnUiThread {
    Toast.makeText(
      this@MainActivity,
      "Cloud Machine Location Update",
      Toast.LENGTH_SHORT
    ).show()
    }
  }
})
```

#### Set a callback for the result of executing an Adb command

**method：**

```kotlin
setExecuteAdbCommandCallback(iExecuteAdbCommandCallback: IExecuteAdbCommandCallback)
```

**parameter：**
iExecuteAdbCommandCallback ： Set a callback for the result of executing an Adb command

**Example code：**

```kotlin
ArmCloudEngine.setExecuteAdbCommandCallback(object : IExecuteAdbCommandCallback {
  override fun onExecuteAdbCommandResult(isSuccess: Boolean, content: String) {
    ToastUtils.showShort("The adb command is executed successfully isSuccess: $isSuccess resultMsg: $content")
    Log.e(
    TAG,
    "onExecuteAdbCommandResult isSuccess ===> $isSuccess  content ===> $content"
    )
  }
})
```

#### Set Video Injection Callback Listener

**method:**

```kotlin
setInjectVideoListener(iInjectVideoListener: IInjectVideoListener)
```

**parameter:**
iInjectVideoListener : Video injection callback

**Example code:**

```kotlin
ArmCloudEngine.setInjectVideoListener(object : IInjectVideoListener {
  override fun onInjectCallBack(code: String, content: String) {
    Log.e(
      TAG,
      "onInjectCallBack code: $code  content: $content"
    )
    ToastUtils.showShort(content)
  }

  override fun onVideoInjectInfo(@VideoInjectState state: Int, videoPath: String?) {
    Log.e(
      TAG,
      "videoInjectState state: $state"
    )
    ToastUtils.showShort("videoInjectState state: $state  videoPath: $videoPath")
  }
})
```
