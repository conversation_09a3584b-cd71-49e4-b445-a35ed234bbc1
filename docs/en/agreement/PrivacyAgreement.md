---
title: Cloud Mobile SDK Privacy Policy
---


**Release Date:** June 1, 2024  
**Effective Date:** June 1, 2024  

As the provider of the cloud phone SDK, Hunan <PERSON>an Technology Information Co., Ltd. (hereinafter referred to as "Xiaosuan Technology" or "we") highly respects and is committed to protecting the security of your personal information.  
This privacy statement refers to the cloud phone SDK products and/or services, which include providing SDK packages and API services for developers to integrate with cloud phone services.  
The cloud phone SDK provides developers with quick access to comprehensive cloud phone technology solutions. By calling the client SDK interface, developers can achieve various cloud phone-related business scenarios. After integrating the cloud phone SDK into their applications and products (including apps, mini-programs, websites, etc., collectively referred to as “applications” or “developer applications”), the cloud phone SDK may collect and process data from end users (hereinafter referred to as “you”). In the above scenarios, developers act as "personal information processors," determining the purpose and manner of user data processing. We, while providing developers with the ability to implement specific business functions of the cloud phone SDK, only collect data on behalf of the developers and process the data according to the developers' authorization and instructions.  
We hope to clearly, accurately, and completely explain in this **Privacy Policy** how we collect, process, and protect all the information you provide when using developer applications integrated with the cloud phone SDK.  

**Special Statement:**  
This privacy policy cannot replace the privacy policy of the developer applications.  
Developers should disclose their privacy policy to you regarding their applications, stating how they collect, process, and protect your personal information.  
If you seek access to your data or attempt to correct, modify, or delete inaccurate data, or if you do not wish to continue using the applications integrated with the cloud phone SDK, please contact the respective developer (the personal information processor) directly.


## This **Privacy Policy** will help you understand the following:  
1. How we collect and use personal information  
2. How we share, transfer, and publicly disclose personal information  
3. How we store and protect personal information  
4. Your rights  
5. How this **Privacy Policy** is updated  
6. How to contact us  

### I. How We Collect and Use Personal Information

**(1)** To assist developers in providing corresponding functions and services to end users  
If you use developer applications that integrate the cloud phone SDK, the cloud phone SDK will programmatically collect the following information on behalf of the developers:

| SDK Name | Function Type | Personal Information Type | Purpose |
| --- | --- | --- | --- |
| Cloud Phone SDK | **Necessary Information** (Basic information required for your cooperation with XiaoSuan Technology) <br/> Provides APP data flow services and identifies application product service analysis | Personal device information: Device brand, device model, operating system, operating system API version, system language, screen resolution, user agent, CPU information (frequency, model, architecture) <br/> IP address <br/> Application information: Application release channel, application package name | Data flow service, identify application product service |
| Cloud Phone SDK | **Optional Information** <br/> Provides APP data flow services and identifies application product service analysis | Sensor information: Accelerometer, magnetometer, gyroscope, gravity sensor <br/> Personal location information: Wireless network name, latitude and longitude information <br/> Other information: Access to camera, recording, clipboard | Sensor information, used to simulate mobile functions and local experience behavior with user consent; <br/> Personal location information and other information for data flow services |

We do not require you to actively submit personal information. The information we collect cannot independently identify a specific natural person's identity, and due to the technical characteristics of this SDK, it objectively cannot obtain any information that can independently identify a specific natural person during its operation.

**Cloud Phone SDK Permissions List:**

| SDK Name | Permission Name | Permission Function | Usage Scenarios and Purpose |
| --- | --- | --- | --- |
| Android SDK | CAMERA <br/> Take photos | Use camera | Complete audio and video publishing, synchronize data between cloud and mobile device |
| Android SDK | MICROPHONE <br/> Microphone | Access microphone | Complete audio and video publishing, synchronize data between cloud and mobile device |
| Android SDK | READ_EXTERNAL_STORAGE <br/> Read external storage | Access external storage | Complete audio and video publishing, synchronize data between cloud and mobile device |
| Android SDK | WRITE_EXTERNAL_STORAGE <br/> Write external storage | Write to external storage | Complete audio and video publishing, synchronize data between cloud and mobile device |
| Android SDK | ACCESS_FINE_LOCATION <br/> Access fine location | Access precise geographical location | Simulate local position in cloud device |
| Android SDK | ACCESS_COARSE_LOCATION <br/> Access coarse location | Access approximate geographical location | Simulate local position in cloud device |

**(2)** Guarantee service security, optimization, and improvement purposes  
To help developers provide the above functions and services to end users, as well as to more accurately locate and resolve the issues faced by developers and end users when using XiaoSuan Cloud Phone SDK products and services, improve and optimize the dual experience of XiaoSuan Cloud Phone SDK products and services on both developer and end user sides, accurately locate and resolve issues faced by end users when using XiaoSuan Cloud Phone SDK services, improve and optimize the service experience of XiaoSuan Cloud Phone SDK, enhance the security of XiaoSuan Cloud Phone SDK services, prevent, detect, investigate fraud, security threats, illegal actions, or actions that violate our agreements, policies, or rules, and protect the legitimate rights and interests of developers, end users, us, our affiliates, partners, and the public, we will collect end users' device information, location information, log information, and other information related to the login environment.

**(3)** Anonymization of personal information  
Without publicly disclosing or providing end users' personal information, XiaoSuan Technology has the right to mine, analyze, and utilize (including commercial use) the user database that has been anonymized. We also have the right to compile statistics on product/service usage and share anonymized statistical information with the public/third parties.

**(4)** Exceptions for obtaining prior authorization consent  
Please note that in the following circumstances, collecting and using personal information does not require prior authorization consent from the end user:  
1. Directly related to national security or national defense security;  
2. Necessary for entering into or performing a contract in which the individual is a party;  
3. Necessary for fulfilling statutory duties or obligations;  
4. Necessary for responding to public health emergencies or to protect the life, health, and property safety of individuals in emergencies;  
5. Directly related to criminal investigations, prosecutions, trials, and execution of judgments;  
6. Necessary to maintain the life, property, and other major legal rights of the end user or other individuals but difficult to obtain their consent;  
7. Within a reasonable scope as stipulated by laws and regulations, collecting personal information that end users have voluntarily disclosed to the public or have already been legally disclosed;  
8. Within a reasonable scope as stipulated by laws and regulations, collecting personal information of end users from legally disclosed information, such as legitimate news reports and government information disclosures;  
9. Processing personal information within a reasonable scope for public interest activities such as news reporting and public opinion supervision;  
10. Necessary for academic research institutions to conduct statistical or academic research based on public interest, and when providing the results of academic research or descriptions, anonymizing the personal information included in the results;  
11. Other circumstances as stipulated by laws and regulations.  
End users are reminded that when we intend to use information for purposes not stated in this privacy policy, we will seek the consent of the end user in advance.


### II. How We Share, Transfer, and Disclose Personal Information

**(1)** Sharing  
Unless you have separately agreed in advance or it meets other legal and regulatory requirements, we will not share your personal information with any third parties outside of XiaoSuan Technology, except for information that has been processed in a way that cannot identify a specific individual and cannot be restored.  
For companies, organizations, and individuals with whom we share personal information, we will investigate their data security environment, sign strict confidentiality agreements with them, and require them to handle personal information in accordance with our instructions, this privacy policy, and any other relevant confidentiality and security measures.

1. Under the following circumstances, with the end user’s authorized consent, we may share personal information: only to achieve the purposes stated in this privacy policy, some of our services will be provided by authorized partners. We may share certain personal information of end users with partners to provide better customer service and user experience. We will only share personal information of end users for legitimate, lawful, necessary, specific, and explicit purposes and only share personal information relevant to providing services. Our partners do not have the right to use the shared personal information for any other purposes.  
Currently, our authorized partners include the following types:  
   (1) Service platforms or service providers. XiaoSuan's various products have integrated a wealth of third-party services. When end users choose to use such third-party services, they authorize us to provide that information to third-party service platforms or service providers so that they can provide services to end users based on the relevant information.  
   (2) Software/hardware/system service providers. When third-party software/hardware/system products or services are combined with XiaoSuan Technology's products or services to provide services to end users, with the end user’s authorization, we will provide the necessary personal information of the end users to third-party software/hardware/system service providers to enable end users to use the services or for us to analyze product and service usage to enhance the end user’s experience.

2. For companies, organizations, and individuals with whom we share personal information, we will investigate their data security environment, sign strict confidentiality agreements with them, and require them to handle personal information in accordance with our instructions, this privacy policy, and any other relevant confidentiality and security measures.

**(2)** Transfer  
We will not transfer end users' personal information to any companies, organizations, and individuals outside of our affiliates, except in the following situations:  
   - Obtaining the explicit authorization or consent of end users in advance;  
   - Meeting the requirements of laws and regulations, legal procedures, or mandatory government requests or judicial rulings;  
   - If we or our affiliates are involved in mergers, divisions, liquidations, acquisitions, or sales of assets or businesses, the end user's personal information may be transferred as part of such transactions. We will ensure the confidentiality of such information during the transfer and do our best to ensure that the new company or organization holding the end user's personal information continues to be bound by this privacy policy; otherwise, we will require the company or organization to seek authorization and consent from the end user again.

**(3)** Public Disclosure  
We will only publicly disclose end users' personal information in the following circumstances:  
   - Obtaining separate consent from the end user;  
   - Based on legal regulations, legal procedures, litigation, or mandatory requirements from government authorities.

**(4)** Exceptions for obtaining prior authorization consent for sharing, transferring, and publicly disclosing personal information  
In the following circumstances, sharing, transferring, and publicly disclosing personal information do not require prior authorization consent from the end user:  
1. Directly related to national security or national defense security;  
2. Necessary for entering into or performing a contract in which the individual is a party;  
3. Necessary for fulfilling statutory duties or obligations;  
4. Directly related to public safety, public health, and significant public interest. For example: necessary for responding to public health emergencies or to protect the life, health, and property safety of individuals in emergencies;  
5. Directly related to criminal investigations, prosecutions, trials, and execution of judgments;  
6. Necessary to maintain the life, property, and other major legal rights of the personal information subject or other individuals but difficult to obtain their consent;  
7. Processing personal information within a reasonable scope as stipulated by laws and regulations for information voluntarily disclosed by individuals or other information that has been legally disclosed, such as legitimate news reports, government information disclosures, etc.;  
8. Other circumstances as stipulated by laws and regulations.


### III. How We Store and Protect Personal Information

We place great importance on information security and adhere to strict security standards, using security protection measures that meet industry standards to protect the information you provide. We employ various reasonable technical, operational, and management security measures to ensure the security of the information we collect, preventing unauthorized access, disclosure, use, modification, damage, or loss of information.

**(1)** Location of Information Storage  
In accordance with laws and regulations, we will store personal information collected and generated within the People's Republic of China domestically. Currently, we will not transfer the aforementioned information overseas. If developers transfer the information overseas, they will fulfill relevant compliance obligations.

**(2)** Storage Duration  
We will retain your information only for as long as necessary to provide services to developers. After exceeding the storage duration agreed upon with developers, or upon receiving relevant instructions from developers, we will delete or anonymize your personal information, except as otherwise required by laws and regulations.

**(3)** Security Measures  
1. We attach great importance to the security of user information and strive to take reasonable security measures (including technical and managerial) to protect your information, preventing your provided information from being improperly used or accessed, disclosed, used, modified, damaged, lost, or leaked without authorization.
2. We will use encryption technologies and anonymization methods that meet or exceed industry standards to protect your information and employ security mechanisms to prevent malicious attacks on your information.
3. We will establish dedicated security departments, security management systems, and data security processes to ensure the security of your information. We adopt strict data usage and access protocols to ensure that only authorized personnel can access your information and conduct timely security audits of data and technology.
4. Despite having taken the aforementioned reasonable and effective measures and having complied with the standards required by relevant laws, please understand that due to technological limitations and various potential malicious means, it is not possible to guarantee 100% information security in the internet industry, even with our best efforts to enhance security measures. We will do our best to ensure the safety of the information you provide to us. You acknowledge and understand that the system and communication networks you use to access our services may experience issues due to factors beyond our control. Therefore, we strongly recommend that you take proactive measures to protect the security of user information, including but not limited to using complex passwords, regularly changing passwords, and not disclosing your account password and other information to others.
5. We will develop emergency response plans and immediately initiate emergency plans in the event of a user information security incident, striving to prevent the impact and consequences of such security incidents from expanding. Once a user information security incident (such as a leak or loss) occurs, we will promptly inform you in accordance with legal requirements: the basic situation and possible impact of the security incident, the measures we have taken or will take, suggestions for you to autonomously prevent and mitigate risks, and remedial measures for you. We will timely notify you of the incident details via push notifications, emails, letters, or text messages. If it is difficult to notify each individual, we will take reasonable and effective ways to publish announcements. Meanwhile, we will report the handling of user information security incidents to relevant regulatory authorities as required.

6. We specifically remind you that the user information protection measures provided in this privacy policy apply only to this service. Once you leave the relevant pages of this service and browse or use other websites, products, services, and content resources, we have no ability or obligation to protect any information you submit outside of this service, regardless of whether you access, browse, or use the aforementioned software or websites through links or guidance from this service.

### IV. How We Handle Personal Information of Minors

XiaoSuan Technology places great importance on the protection of minors' information.  
The products and services of XiaoSuan Cloud Phone SDK are primarily aimed at adults. If the end user is a minor under the age of 14, please ensure that you carefully read the developer application’s privacy policy and this privacy policy together with your parent or other guardians before using the developer application that has integrated the XiaoSuan Cloud Phone SDK, and use the developer application or provide personal information only after obtaining the guardian's consent.  
If we discover that we have collected personal information of minors without obtaining verifiable parental consent, we will take measures to delete the relevant information as soon as possible.  
If at any time a guardian has reason to believe that we have collected personal information of minors without obtaining the guardian's consent, please contact us through official channels, and we will take measures to delete the relevant data as soon as possible.

### V. Your Rights

In accordance with relevant laws, regulations, and standards in China, as well as common practices in other countries and regions, we will do our best to coordinate, support, and safeguard end users' exercise of the following rights regarding their personal information:

**(1)** Right to Access, Correct, Supplement, Copy, and Cancel Account  
Considering that end users utilize XiaoSuan Cloud Phone SDK services through developer applications without registering or logging into a XiaoSuan Technology account, we request developers to commit to providing an easy-to-use method for end users to access, correct, supplement, copy their personal information, and cancel their developer application accounts. If an end user wishes to access, correct, supplement, copy their personal information, or cancel their developer application account, they should do so through the methods provided by the developer. If the developer fails to provide as promised, the end user can contact us using the methods in Section VII, “How to Contact Us,” and we will do our best to coordinate, support, and safeguard the end user's exercise of the aforementioned rights.

**(2)** Right to Delete  
To ensure that end users can delete their personal information, we request developers to commit to providing an easy-to-use method for this right. If an end user wishes to delete their personal information, they should do so through the methods provided by the developer. If the developer fails to provide as promised, the end user can contact us using the methods in Section VII to request the deletion of their personal information under the following circumstances:
1. If we violate laws, regulations, or agreements with end users while processing their personal information;
2. If our processing purposes have been achieved, are unachievable, or are no longer necessary for achieving the processing purposes;
3. If we cease to provide products or services, or the storage period has expired;
4. If the end user withdraws their consent;
5. Other circumstances stipulated by laws and administrative regulations.  
Please note that when an end user deletes information from our services, we may not immediately delete the corresponding information from backup systems but will delete it when the backup is updated. End users should understand that if the retention period stipulated by laws, administrative regulations, or this privacy policy has not yet expired, or if deletion of personal information is technically difficult, we will cease processing except for storage and necessary security protection measures.

**(3)** Right to Withdraw Consent  
Each business function requires certain basic personal information to be completed. For the collection and use of additional personal information, end users can grant or withdraw their consent at any time.  
End users can directly turn off the device system permissions we may invoke as stated in this privacy policy in their device system, or change the scope of consent or withdraw their authorization through other authorization settings provided by the developer application (if applicable).  
When an end user withdraws their consent, we can no longer provide the services corresponding to the withdrawn consent and will no longer use the end user's relevant personal information. However, the decision to withdraw consent will not affect the processing of personal information conducted based on their prior consent.

**(4)** Right to Portability  
To ensure that end users can transfer their personal information, we request developers to commit to providing an easy-to-use method for this right. If an end user wishes to transfer their personal information, they should do so through the methods provided by the developer. If the developer fails to provide as promised, the end user can contact us using the methods in Section VII to seek support for the exercise of this right.  
Under conditions stipulated by laws and regulations, and in compliance with directives and conditions set by national internet authorities, if technically feasible, end users can also request us to transfer their personal information to other entities designated by them.

**(5)** Right to Advance Notice of Service Suspension  
XiaoSuan Cloud Phone SDK wishes to accompany developers and end users continuously. If, for any special reason, the XiaoSuan Cloud Phone SDK product stops operation, we will notify developers and end users within a reasonable period through the main page of the product or service, internal messaging, emails, or other suitable means of communication, and will stop collecting personal information from end users while deleting or anonymizing the personal information collected as required by law.

**(6)** Right to Request Explanation  
End users have the right to request explanations regarding our personal information processing rules. End users can contact us through the methods in Section VII.

### VI. How This Privacy Policy is Updated  
**(1)** In order to provide better services to developers and users, this service will be updated and changed from time to time. We will timely revise this privacy policy; such revisions form part of this privacy policy and have the same effect as this privacy policy.  
**(2)** After this privacy policy is updated, we will publish the updated version on the relevant pages of this service to keep you informed of the latest version. If you continue to use this service, it is deemed that you agree to accept all the contents of the revised privacy policy. However, if the updated content requires obtaining additional user information, we will still seek your consent again in a prominent manner.

### VII. How to Contact Us  
If you have any questions, comments, or suggestions regarding the content of this privacy policy, you can contact us by sending an <NAME_EMAIL>. We will respond within 15 working days (the "Response Period").