---
title: Cloud Mobile Service Level Agreement
---


## Service Level Agreement (SLA)
Effective Date: June 1, 2024

This Service Level Agreement (hereinafter referred to as "SLA") outlines the availability level indicators and compensation scheme for the ARM cloud mobile phone service provided by Xiaosuan Cloud to its customers. Xiaosuan Cloud may revise the SLA agreement as necessary, but during your subscription period, Xiaosuan Cloud will not modify your SLA terms in a manner that substantially reduces the service level; if you renew your service, the version of the SLA applicable at the time of renewal will apply to your renewal period. Continuing to use the service means you agree to the SLA in effect at that time.

### 1. Definitions
**Service Period**: A service period is defined as a calendar month.  
**Total Time of Service Period**: Refers to the total days in each service period × 24 (hours) × 60 (minutes).  
**Service Time**: In a service period, the time remaining after deducting system maintenance time is considered service time. If Xiaosuan Cloud conducts system maintenance that may affect system stability, it will notify customers 72 hours in advance and will choose to perform the maintenance during a time that minimizes impact.  
**Service Unavailability**: This refers to the period during which the service cannot be requested due to reasons attributed to Xiaosuan Cloud, measured in unit time (every 5 minutes as a unit time).  
**Total Unavailability Time of the Service Period**: Refers to the sum of the unavailability time of the service within a service period. If the service is unavailable for more than one unit time until it is restored, the unavailability time will be counted as multiple units. If the inability to request service lasts less than 5 minutes, it will not be counted towards service unavailability time.  
**Monthly Service Fee**: The total amount paid by the customer to Xiaosuan Cloud for a single cloud mobile phone instance during a calendar month. If the customer pays for multiple months of service in a lump sum, the monthly service fee will be calculated based on the number of months purchased. Fees paid for third-party cloud mobile phone services purchased in the cloud marketplace do not count towards the monthly service fee.

### 2. Service Availability
#### 2.1 Calculation of Service Availability
Service Availability = ( (Total Time of Service Period - Total Unavailability Time of Service Period) / Total Time of Service Period ) × 100%

#### 2.2 Service Availability Commitment
For single-instance dimensions, Xiaosuan Cloud commits to providing cloud mobile phone services with an availability of no less than 99% during a service period. If the service does not meet the above service availability commitment, the customer may claim compensation in accordance with Article 3 of this agreement. The following exempt circumstances that lead to service unavailability do not count towards the total unavailability time:  
1) System maintenance conducted after prior notification to customers, including cuts, repairs, upgrades, and simulated failure drills;  
2) Errors arising from the customer’s business content violating regulations or other reasons leading to business content being banned;  
3) Any network or device failures or configuration adjustments not belonging to the cloud mobile phone; hacker attacks affecting the customer’s application or data;  
4) Loss or leakage of data, passwords, or other confidential information due to improper maintenance or confidentiality by the customer;  
5) Attacks or other improper actions affecting the customer's website or application;  
6) Issues arising from the customer independently upgrading the operating system; not following the service product usage documentation or recommendations;  
7) Problems arising from the customer's applications or installation activities;  
8) Negligence by the customer or actions authorized by the customer;  
9) Force majeure and unexpected events;  
10) Not following the cloud mobile phone product usage documentation or recommendations;  
11) Requests originating from non-mainland China IP addresses.

### 3. Compensation Scheme
#### 3.1 Compensation Standards
1) For a single cloud mobile phone, if the service availability is below 99%, compensation can be obtained according to the standards in the table below. The compensation method is limited to deducting the settlement fee, and the total compensation amount shall not exceed the single-instance monthly service fee paid by the customer for the month in which the service availability commitment was not met. If other failure circumstances not explicitly defined in this agreement occur, the compensation cycle is calculated in calendar days, with a maximum compensation amount equal to the daily settlement fee for the affected device.

| Service Availability | Compensation Duration |
| --- | --- |
| Below 99% but equal to or above 95% | 2 times the duration of downtime exceeding the 99% threshold |
| Below 95% | 3 times the duration of downtime exceeding the 95% threshold |

2) Compensation for network connectivity is based on daily service fees. If cumulative downtime exceeds 30 minutes in a day (including 30 minutes), Xiaosuan Cloud will waive 5% of the day's service fee; if cumulative downtime exceeds 1 hour (including 1 hour), Xiaosuan Cloud will waive 15% of the day's service fee; if cumulative downtime exceeds 2 hours, Xiaosuan Cloud will waive 30% of the day's service fee.

#### 3.2 Compensation Application Deadline
Customers may apply for compensation for periods where service availability was not met in the previous month after the 5th working day of each month. Compensation applications must be submitted within 2 months after the end of the relevant month when the cloud mobile phone did not meet availability. Applications submitted after the deadline will not be accepted.  
Upon your application for compensation, we will conduct corresponding verification. In the event of a dispute regarding the calculation of monthly service availability, both parties agree that Xiaosuan Cloud's backend records will prevail.