---
title: System service interface (aidl)
---


## AIDL access method

#### 1. Open AIDL in build.gradle under the project app directory

**Taking build.gradle.kts as an example**

```

android {

.... 

//Enable AIDl

buildFeatures {

aidl = true

}

}

```


#### 2. Create an AIDL file

**1. Create an aidl folder in app/src/main, download the aidl compressed file, and extract it to the aidl directory (full path is aidl.cloud.api.server)**


#### 3. Connect AIDL services in the app project

**Taking Kotlin code as an example**
	1. Create a service
	val mControl = object : ServiceConnection {
		override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
			LogUtils. d ("Create aidl")
			control = ControlInterface.Stub.asInterface(p1)
			MyAidlManager.setControlAidl(control)
		}

		override fun onServiceDisconnected(p0: ComponentName?) {
			LogUtils. d ("Disconnect")
			//Disconnect and reconnect
			GlobalScope.launch {
				delay(3000)
				startMyService()
			}
		}
	}


	val mRoot = object : ServiceConnection {

		override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
			LogUtils. d ("Create aidl")
			root = RootInterface.Stub.asInterface(p1)
			MyAidlManager.setRootAidl(root)
		}

		override fun onServiceDisconnected(p0: ComponentName?) {
			LogUtils. d ("Disconnect")
			//Disconnect and reconnect
			GlobalScope.launch {
				delay(3000)
				startMyService()
			}
		}
	}

	2. Set up a globally available Utils
	object MyAidlManager {
		var control : ControlInterface? = null
		var root : RootInterface? = null

		fun setControlAidl(control : ControlInterface?){
			this.control = control
		}

		fun setRootAidl(root : RootInterface?){
			this.root = root
		}
	}

	3. Bind AIDL services
	fun startMyService(){
		val intent = Intent("aidl.cloud.api.ControlServer")
		intent.setPackage("com.cloud.rtcgesture")
		bindService(intent, mControl, BIND_AUTO_CREATE)
		val intent2 = Intent("aidl.cloud.api.RootServer")
		intent2.setPackage("com.cloud.rtcgesture")
		bindService(intent2, mRoot, BIND_AUTO_CREATE)
	}

	4. Close the service
	override fun onDestroy() {
		super.onDestroy()
		unbindService(mControl)
		unbindService(mRoot)
	}

	5. Start the service
	startMyService()

#### 4. Add adaptation in AndroidManifest.xml under the APP directory

```
<manifest>
... 

	<queries>
		<package android:name="com.cloud.rtcgesture" />
	</queries>

</manifest>
```

#### 5. Interface method description

**ControlInterface** (Control Tool Class)

```
//IsOpen Open (true)/Close (false) Google Services
isOpenGms(boolean isOpen)
```

```
//Message transmission to the user end can only be transmitted after the user connects to the cloud machine
sendMessage(String message)
```


**RootInterface * * (root utility class)


```

//IsRoot on (true)/off (false) global root switch

allRoot(boolean isRoot)

```


```
//PackageName package name, isRoot on (true)/off (false) (requires global root to be turned on before root can be turned on) Root switch for specifying package name
//After the switch is turned on, the application needs to be restarted before it becomes invalid (in case of invalidity, it needs to be forcibly stopped in the application settings to take effect)
setRootPackageName(String packageName, boolean isRoot)
```

```
//PackageName package name, return true to open, false to close. Determine whether the specified package name has root enabled
isRoot(String packageName)

```


```
//Whether the global root is open, return true to open, false to close
isAllRoot()
```


#### 6. Real machine message transmission to cloud machine
**Add the Message Interface.aidl file to the aidl.cloud.aid.server directory**
Create a Service inheritance compiled aidl file called Message InterfaceStub()
At the Message InterfaceImplement the message method in the interface of Stub()


**Interface method description**

```
//Type field transmitted from the real machine, data field transmitted from the data machine
message(type: Int, data: String?)
```

####7. Obtain the download progress of tasks issued by the backend
**1. Creating the ApkDownloaderServer directory requires the same path as aidl and implementing the ApkDownloaderInterface aidl method**

```
//Task Start Download Package Name Package Name iconURL icon Address appName File Name taskId Task ID path Download path
apkDownloadStart(String packageName, String iconUrl, String appName, long taskId, String path); 
```

```
//Task download progress packageName package name progress
apkProgress(String packageName, int progress); 
```

```
//Task download completed packageName package name appName file name taskId task id
apkDownloaderEnd(String packageName, String appName, long taskId); 
```

```
//Task download failed packageName package name
apkDownloadFail(String packageName); 
```

Note: The progress is usually displayed by launcher integration, and the package name needs to be com. android. mxLauncher3. Other package names need to be obtained. The download progress in the background needs to be customized by contacting business or customer service

