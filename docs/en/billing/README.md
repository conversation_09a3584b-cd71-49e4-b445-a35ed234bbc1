## Billing Description
The billing includes board rental fees, bandwidth fees, and IP usage fees. The billing method is prepaid, with a minimum rental of one server (one ARM server includes 128 boards).

## Billing Formula
Monthly Cost = Number of Cloud Phone Servers * Number of Boards per Server * Price per Board + Bandwidth Fees + IP Fees

## Billing Items

| Billing Item | Billing Method | Configuration          | Billing Cycle | Description                                                                                                                             |
|--------------|----------------|------------------------|---------------|-----------------------------------------------------------------------------------------------------------------------------------------|
| Board        | Prepaid        | 8C32G, 256G Storage    | Monthly       | Prepaid model: pay first, use later. Each board can configure 1-8 cloud phones.                                                        |
| Bandwidth    | Postpaid       | Monthly 95th Percentile | Monthly       | Bandwidth usage is measured every 5 minutes, capturing the larger of inbound or outbound values. Daily, 288 data points are collected. The top 5% of values are discarded, and the next highest value becomes the monthly 95th percentile bandwidth. Monthly bandwidth cost = Monthly 95th percentile bandwidth * Unit price. Billing is settled monthly, with invoices issued within the first 4 working days of the following month, and charges deducted accordingly. |
| IP           | Prepaid        | Charged per number     | Monthly       | Each ARM server can configure multiple IPs. Monthly IP cost = Number of IPs * Unit price.                                              |
