---
title: Interface Documentation
---



## Introduction
This document provides you with specific API descriptions, syntax, parameter descriptions, and examples, etc. You can call the API to manage the resources of the cloud phone service.

## Update the record
### Update Log (Chronological Order)

---
#### **2025-01-08**
- Added [**Upgrade real ADI template**] interface

#### **2024-12-30**
- Updated **Modify SIM** interface

#### **2024-12-26**
- Added **Request stsToken based on padCode** interface

#### **2024-12-20**
- Updated **One Click New Machine** interface (supports incoming country code)
- Added **Backup Restore** callbacks

#### **2024-12-18**
- New **Callback Configuration** interface

#### **2024-12-05**
- Added **Modify Contacts** interface

#### **2024-11-22**
- Added **Instance Backup and Instance Data Restore** interfaces
- Added **One Click New Machine** interface

#### **2024-11-13**
- New **Modify Instance Android Attributes** interface

#### **2024-10-23**
- New **Enable/Disable Instance ADB** interface

#### **2024-09-27**
- New **Modify Instance Time Zone** interface
- New **Modify Instance Language** interface
- New **Modify Instance SIM Card Information** interface
- New **Set Instance Latitude and Longitude** interface
- New **Query Instance Agent Information** interface
- New **Instance Setting Agent** interface

#### **2024-09-23**
- New **Set Instance Bandwidth** interface

#### **2024-08-09**
- New **Instance IP Binding** interface
- New **Apply for RTC Shared Room Token** interface

#### **2024-07-23**
- New **Add Application Blacklist List** interface
- New **Setup Instance Blacklist** interface

#### **2024-07-22**
- New **Physical Machine Reboot** interface
- New **Batch Get Example Models** interface

#### **2024-07-18**
- New **Batch Request RTC Token** interface

#### **2024-07-08**
- Updated **Instance Operation Task Details** interface

#### **2024-07-04**
- Updated **Application Installation** interface
- Updated **Application Launch** interface
- Updated **Application Start/Stop** interface
- Updated **Application Stop** interface
- Updated **Application Restart** interface
- Updated **Application Uninstallation** interface
- Updated **Instance Reset** interface
- Updated **Example File Upload** interface
- Updated **Query Instance Application List** interface

#### **2024-07-01**
- Updated **File Upload** interface
- Updated **Instance Restart** interface
- Updated **Instance Details** interface
- Updated **Modify Instance Properties** interface
- Updated **Asynchronous Execution of ADB Commands** interface

#### **2024-06-21**
- Updated **Mirror Upload** interface
- Updated **Mirror Upgrade** interface

#### **2024-04-30**
- **Documentation Release**

## Calling instructions

### Pre-conditions

Obtain the Access Key ID and Secret Access Key (AK/SK) of the account for API request authentication. Please contact your technical contact to obtain the

### Public request parameters

The interface must include the following four parameters in Headers for authentication each time it is requested, otherwise the interface will not be able to request properly.

| Parameter Name | Type | Example Values | Parameter Description |
| --- | --- | --- | --- | --- |
| x-date | string | 20240301T093700Z | Timestamp of the sent request, using UTC time, accurate to seconds |
| x-host | string | openapi.armcloud.net | Interface Access Domain |
| authorization | string | HMAC-SHA256 Credential={AccessKey}/{ShortDate}/armcloud-paas/request, SignedHeaders=content-type;host;x- content-sha256;x-date, Signature={Signature} | Signature included in sent request |
| Content-Type | string | application/json | MIME type of the resource |

### Authorization signature mechanism

For each HTTPS protocol request, the identity of the access requestor is verified based on the signature information in the access. This is achieved by cryptographic verification of the AccessKey ID and AccessKey Secret (AK/SK) corresponding to the user account.
<font color="red">Note: In order to facilitate customer access, the official SDK has already implemented the countersign verification. </font>

#### Calling SDK signatures
>Attention
>For specific calling methods, please refer to ** Interface List ** Code Example


In the SDK provides a signature function or method, the developer only needs to provide the access key and other information in the request, the SDK will automatically calculate the request signature and add the signature result to the request. This method simplifies the signing process and reduces the development difficulty, especially for developers who are not familiar with the signature algorithm.

[Download SDK](https://console.armcloud.net/armcloud-sdk/armcloud-java-sdk-v1.0.0.jar)
##### java call example

#### Manual Signature

> Note
>
> Signatures require a series of processing of the request parameters, including steps such as sorting, splicing, and encryption. This approach provides greater flexibility and customisability and is suitable for situations where the developer has a deep understanding of the signature algorithm. However, manual signing requires developers to write additional code to implement the signing process, which may increase the difficulty of development and the possibility of errors, so we still recommend that you use the SDK to call the API and try to avoid writing your own signing code. If you need to understand the principle and specific process of signature calculation, you can refer to the following documents.

The manual signature mechanism requires the requestor to calculate the hash value of the request parameters, encrypt them and send them to the server together with the API request, the server will use the same mechanism to calculate the signature of the received request and compare it with the signature passed by the requestor, if the signature fails to pass the verification, the request will be rejected.

Obtain the Access Key ID and Secret Access Key (AK/SK) of the account for API request authentication. Please contact your technical contact to obtain the

##### Constructing canonical request strings (CanonicalRequest)
```java
 String canonicalStringBuilder=
 	 "host: "+*${host}*+"\n "+
 	 "x-date: "+*${xDate}*+"\n "+
 	 "content-type: "+*${contentType}*+"\n "+
 	 "signedHeaders: "+*${signedHeaders}*+"\n "+
 	 "x-content-sha256: "+*${xContentSha256}*;
```

| Fields | Interpretation |
| --- | --- |
| host | Request service domain. Fixed to: openapi.armcloud.net |
| x-date | Specifies the request UTC time, i.e., the value of X-Date in the public parameters of the request header, using the ISO 8601-compliant format: `YYYYYMMDD'T'HHMMSS'Z'`, e.g., `20201103T104027Z'|.
| content-type | media-type of the request or response body (application/json) |
| signedHeaders | Participate in the signature of the Header, and CanonicalHeaders contains the Header is a one-to-one correspondence, the purpose is to specify which Header to participate in the signature calculation, so as to ignore the request was added by the proxy additional Header, which host, x-date if there is a Header then Mandatory participation<br/>Pseudo-code is as follows:<br/>SignedHeaders=Lowercase(HeaderName0)+';'+Lowercase(HeaderName1)+"; "+... +Lowercase(HeaderNameN)<br/>Example:<br/>SignedHeaders=content-type;host;x-content-sha256;x-date |
| x-content-sha256 | hashSHA256(body) |

##### Constructing the string to be signed (StringToSign)

The signature string mainly contains metadata information about the request as well as the normalised request, and consists of the signature algorithm, the request date, the trust shape and the normalised request hash concatenation.

Construct the string to be signed with the following pseudo-code:

```java
StringToSign=
	Algorithm+'\n'+
	xDate+'\n'+
	CredentialScope+'\n'+
	hashSHA256(canonicalStringBuilder.getByte())
```

| Fields | Interpretation |
| ---------------- | ------------------------------------------------------------ |
| Algorithm | Refers to the algorithm for signing, currently only the HMAC-SHA256 signature algorithm is supported.          | Algorithm
| x-date | Specifies the request UTC time, i.e., the value of X-Date in the public parameters of the request header, using the ISO 8601-compliant format: `YYYYYMMDD'T'HHMMSS'Z'`, e.g., `20201103T104027Z'|.
| CredentialScope | refers to the trust state in the format: `${YYYYMMDD}/${service}/request`, where `${YYYYMMDD}` takes the date in X-Date, `${service}` is fixed to armcloud-paas, and `request` is a fixed value. <br>Refer to "Calculating CredentialScope" below |
| CanonicalRequest | means the result of **constructing a canonical request string**.                             |

###### Calculating CredentialScope

```
String credentialScope = shortXDate+"/"+service+"/request";
	shortXDate: short request time (x-date intercepts the first 8 digits Example: 20201103)
	service: service name (fixed armcloud-paas)
	"/request": fixed value
```

###### Signingkey example

Derived signature key generated by a sequence of HMAC hash operations

```
byte[]Signingkey=hmacSHA256(hmacSHA256(hmacSHA256(hmacSHA256(sk.getBytes(),shortXDate),service), "request");
```

| Fields | Interpretation |
| --- | --- |
| sk | client key |
| shortXDate | shortRequestDate | shortRequestDate
| Service | Service name is fixed to armcloud-paas | for now.

###### Signature example

```
signature=HexEncode(hmacSHA256(Signingkey,StringToSign))
```



### Data encryption and decryption example

#### Java AES GCM Decryption

```java
import javax.crypto.
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest.
import java.security.NoSuchAlgorithmException.
import java.security.SecureRandom;
import java.util.Base64;

public class AESUtils {
    private static final String AES = "AES";
    private static final String AES_CIPHER_ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_TAG_LENGTH = 16;
    private static final int GCM_IV_LENGTH = 12;

    /**
     * :: Generates a SecretKeySpec from a given string key
     */
    private static SecretKeySpec getKeyFromPassword(String password) throws NoSuchAlgorithmException {
        MessageDigest sha = MessageDigest.getInstance("SHA-256");
        byte[] key = sha.digest(password.getBytes());
        return new SecretKeySpec(key, AES);
    }

    /**
     * :: Generates a new Initialisation Vector (IV)
     */
    public static byte[] generateIv() {
        byte[] iv = new byte[GCM_IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * :: Encrypts a plain text using AES algorithm and returns both the cipher text and IV
     */
    public static String encrypt(String input, String key) {
        try {
            SecretKeySpec secretKeySpec = getKeyFromPassword(key);
            byte[] iv = generateIv();
            Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] cipherText = cipher.doFinal(input.getBytes());

            // Encode IV and cipher text to Base64 and concatenate them with a separator
            String ivString = Base64.getEncoder().encodeToString(iv);
            String cipherTextString = Base64.getEncoder().encodeToString(cipherText);
            return ivString + ":" + cipherTextString;
        } catch (Exception e) {
            log.error("encrypt error >>>input:{} key:{}", input, key, e);
            return null;
        }

    }

    /**
     * :: Decrypts an encrypted text using AES algorithm
     */
    public static String decrypt(String encryptedData, String key) {
        try {
            SecretKeySpec secretKeySpec = getKeyFromPassword(key);

            // Split the encrypted data into IV and cipher text
            String[] parts = encryptedData.split(":");
            String ivString = parts[0];
            String cipherTextString = parts[1];

            byte[] iv = Base64.getDecoder().decode(ivString);
            byte[] cipherText = Base64.getDecoder().decode(cipherTextString);

            Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] plainText = cipher.doFinal(cipherText);
            return new String(plainText);
        } catch (Exception e) {
            log.error("decrypt error >>>encryptedData:{} key:{}", encryptedData, key, e);
            return null;
        }

    }

    /**
     * Encodes the input byte array to a Base64 string
     */
    public static String encodeToString(byte[] input) {
        return Base64.getEncoder().encodeToString(input);
    }

    // Encodes the input string to a Base64 string
    public static String encodeToString(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    /**
     * :: Decodes the input Base64 string to a byte array
     */
    public static byte[] decodeToBytes(String input) {
        return Base64.getDecoder().decode(input);
    }

    /**
     * :: Decodes the input Base64 string to a regular string
     */
    public static String decodeToString(String input) {
        byte[] decodedBytes = Base64.getDecoder().decode(input);
        return new String(decodedBytes);
    }

    /**
     * :: Encodes the input byte array to a Base64 byte array
     */
    public static byte[] encodeToBytes(byte[] input) {
        return Base64.getEncoder().encode(input);
    }

    /**
     * :: Decodes the input Base64 byte array to a byte array
     */
    public static byte[] decodeToBytes(byte[] input) {
        return Base64.getDecoder().decode(input);
    }

    public static void main(String[] args) throws Exception {
        String key = "AC22030010001"; // any string as key
        // Decrypt the cipher text
        String decryptedText = decrypt("iMzQUI7SwzSD0kGJ:4FZ1fn1Jdd5Z4j2ehn/F3VSUVWBwLFQZH/HOCjLAI95r", key);
        System.out.println("Decrypted text: " + decryptedText);
    }
}

```



## Interface overview

### Instance management

| interface                                                                 | interface name | interface description |
|---------------------------------------------------------------------------| --- | --- | 
| [/pad/restart](./OpenAPI.md#/openapi/open/pad/restart)                             | Instance Restart | Performs a restart operation on a specified instance to resolve unresponsive, stuck, etc. problems |
| [/pad/reset](./OpenAPI.md#/openapi/open/pad/reset)                                  | Instance Reset | Perform a reset operation on the specified instance |
| [/pad/padProperties](./OpenAPI.md#/openapi/open/pad/reset)                | QueryInstanceProperties | Query property information for the specified instance |
| [/pad/updatePadProperties](./OpenAPI.md#/openapi/open/pad/reset)       | Modify Instance Properties | Modify property information for the specified instance |
| [/pad/padDetails](./OpenAPI.md#/openapi/open/pad/reset)                           | InstanceDetails | Query the details of the specified instance |
| [/room/dissolveRoom](./OpenAPI.md#/openapi/open/pad/reset)                           | StopPushFlow | Stop the specified instance of push flow |
| [/room/batchApplyToken](./OpenAPI.md#/openapi/open/pad/reset)                | Batch Apply RTC Token | Apply RTC Token for multiple instances |
| [/device/powerReset](./OpenAPI.md#/openapi/open/pad/reset)                   | Physical Machine Reboot | Perform a reboot operation on the specified physical machine |
| [/pad/updateTimeZone](./OpenAPI.md#/openapi/open/pad/reset)                | ModifyInstanceTimeZone | Modify the time zone for the specified instance |
| [/pad/updateLanguage](./OpenAPI.md#/openapi/open/pad/reset)                | ModifyInstanceLanguage | ModifySystemLanguage for a given instance |
| [/pad/updateSIM](./OpenAPI.md#/openapi/open/pad/reset)            | Modify Instance SIM Information | Modify SIM Information for a specified instance |
| [/pad/gpsInjectInfo](./OpenAPI.md#/openapi/open/pad/reset)     | Set instance latitude and longitude | Modify the current latitude and longitude for the given instance |
| [/network/proxy/set](./OpenAPI.md#/openapi/open/pad/reset)                     | Instance Set Proxy | Set Proxy Information for Specified Instance |
| [/network/proxy/info](./OpenAPI.md#/openapi/open/pad/reset)      | Query Instance Proxy Information | Query the specified instance proxy information |
| [/pad/updatePadAndroidProp](./OpenAPI.md#/openapi/open/pad/reset) | Modify Instance Android Attributes |Modify Instance Android Attributes |


[//]: # (| [/pad/data/backup]&#40;. /OpenAPI.md#**Instance Data Backup**&#41; | Instance Data Backup | Instance Data Backup |)
[//]: # (| [/pad/data/restore]&#40;. /OpenAPI.md#**Instance Data Restore**&#41; | Instance Data Restore |Instance Data Restore |)

### Instance manipulation

| interface | interface name | interface description |
| --- | --- | --- | 
| [/pad/asyncCmd](./OpenAPI.md#/openapi/open/pad/reset) | Execute ADB commands asynchronously | Execute adb commands (asynchronous tasks) in one or more cloud phone instances |
| [/pad/syncCmd](./OpenAPI.md#/openapi/open/pad/reset) | Synchronise ADB commands | Synchronise adb commands across one or more cloud phone instances |
| [/pad/screenshot](./OpenAPI.md#/openapi/open/pad/reset) | Local Screenshot | Perform a screenshot in the instance with the phone and save it locally in the cloud machine |
| [/pad/generatePreview](./OpenAPI.md#/openapi/open/pad/reset) | Generate Preview | Take a screenshot of the current cloud phone screen and get the address to download the screenshot file |
| [/file/cache](./OpenAPI.md#/openapi/open/pad/reset) | File Upload | Uploading files to the File Management Centre (asynchronous task) |
| [/file/list](./OpenAPI.md#/openapi/open/pad/reset) | File List | Query uploaded file list information |
| [/pad/v2/uploadFile](./OpenAPI.md#/openapi/open/pad/reset) | Instance File Upload | Push file(s) from File Management Centre to one or more Cloud Phone instances (asynchronous task) |
| [/pad/upgradeImage](./OpenAPI.md#/openapi/open/pad/reset) | Upgrade Image | Upgrade Instance Image (asynchronous task) |
| [/pad/modelInfo](./OpenAPI.md#/openapi/open/pad/reset) | Batch Fetch Instance Model Info | Batch Fetch the model info of the corresponding instance based on the instance number |
| [/appBlack/setUpBlackList](./OpenAPI.md#/openapi/open/pad/reset) | add app black list list | add app black list based on instance specifications |
| [/pad/triggeringBlacklist](./OpenAPI.md#/openapi/open/pad/reset) | SetInstanceBlacklist | SetInstanceBlacklist by Instance Specification |
| [/pad/setSpeed](./OpenAPI.md#/openapi/open/pad/reset) | SetInstanceBandwidth | SetInstanceBandwidth in bulk based on instance number |
| [/pad/adb](./OpenAPI.md#/openapi/open/pad/reset) | Enable/Disable Instance ADB | Enable or disable ADB for an instance based on the instance ID |
| [/openapi/open/pad/replaceRealAdiTemplate](./OpenAPI.md#upgradeRealAdiTemplate) | upgrade real ADI template | upgrade real ADI template |
### Resource management

| interface | interface name | interface description |
| --- | --- | --- | 
| [/pad/infos](./OpenAPI.md#/openapi/open/pad/reset) | Instance List Information | Query all ordered instances list information |
| [/group/infos](./OpenAPI.md#/openapi/open/pad/reset) | Instance Grouping List | Query All Instance Grouping Lists |

### Application management

| interface | interface name | interface description |
| --- | --- | --- | 
| [/app/cache](./OpenAPI.md#/openapi/open/pad/reset) | Application Upload | Upload application installation files to the Application Management Centre (asynchronous task) |
| [/app/list](./OpenAPI.md#/openapi/open/pad/reset) | app-list | Query the list of uploaded apps |
| [/app/detail](./OpenAPI.md#/openapi/open/pad/reset) | Application Details | Queries the details of the specified application and application version information |
| [/pad/listApp](./OpenAPI.md#/openapi/open/pad/reset) | Query Instance App List | Query App Installation on the specified instance |
| [/pad/installApp](./OpenAPI.md#/openapi/open/pad/reset) | App Installation | Deploys the specified app installation to the specified cloud instance (asynchronous task) |
| [/pad/uninstallApp](./OpenAPI.md#/openapi/open/pad/reset) | App Uninstall | Uninstall the specified app from the cloud instance (asynchronous task) |
| [/pad/startApp](./OpenAPI.md#/openapi/open/pad/reset) | App Launch | Launch the specified app in one or more cloud instances (asynchronous task) |
| [/pad/stopApp](./OpenAPI.md#/openapi/open/pad/reset) | App Stop | Shut down the specified app in one or more cloud instances (asynchronous task) |
| [/pad/restartApp](./OpenAPI.md#/openapi/open/pad/reset) | Application Restart | Restart the specified application in one or more cloud instances (asynchronous task) |

### Mirror management
| interface | interface name | interface description |
| --- | --- | --- | 
| [/image/uploadImage](./OpenAPI.md#/openapi/open/pad/reset) | Creating a Mirror Upload Task | Creating a Mirror Upload Task (asynchronous task) |

### Task management
| interface | interface name | interface description |
| --- | --- | --- | 
| [/task/fileTaskDetail](./OpenAPI.md#/openapi/open/pad/reset) | fileTaskDetail | Query the details of the execution result of the specified file task |
| [/task/padTaskDetail](./OpenAPI.md#/openapi/open/pad/reset) | InstanceOperationTaskDetail | Query the execution result details of the specified instance operation task |
| [/task/executeScriptInfo](./OpenAPI.md#/openapi/open/pad/reset) | Get Instance Execute Script Results | Get Instance Execute Script Results by Execute Script Task ID |
| [/task/screenshotInfo](./OpenAPI.md#/openapi/open/pad/reset) | Get instance screenshot results | Get instance screenshot results by screenshot task ID |
| [/task/appOperateInfo](./OpenAPI.md#/openapi/open/pad/reset) | appOperateExecutionResults | Get the results of an instance's appOperate by appOperateTask ID |
| [/task/padExecuteTaskInfo](./OpenAPI.md#/openapi/open/pad/reset) | InstanceRestartResetExecuteResults | Get InstanceRestartResetExecuteResults by task ID |
| [/task/imageUploadResult](./OpenAPI.md#/openapi/open/pad/reset) | Image Upload Task Result Query | Image Upload Task Result Query by Task ID |

### SDK Token
| interface | interface name | interface description |
| --- | --- | --- | 
| [/token/stsToken](./OpenAPI.md#/openapi/open/pad/reset) | issue sdk temporary token | issue sdk temporary token for authentication of users accessing the cloud phone service |

## List of OpenAPI interfaces

### Instance management

#### **Instance restart**
Performs a reboot operation on a specified instance to solve problems such as unresponsive and stuck systems.

**Interface address**

> /openapi/open/pad/restart

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| ------- | ------------- | -------- | -------- | -------- | 
|padCodes | String[] | yes | |
| ├─ | AC21020010001 | String | Yes | Instance Number|
|groupIds | | Integer[] | no | |
| ├─ | 1 | Integer | No | Instance Group ID|

**Response parameters**

| parameter name | example value | parameter type | parameter description|
| --- | --- | --- | --- | 
|code | 200 | Integer | Status Code|
|msg | success | String | response message|
|ts | ************* | Long | Timestamps|
|data | |Object[] | | |
|├─taskId | 1 | Integer | Task ID|
|├─padCode | AC21020010001 | String | Instance Number|
|├─vmStatus | 1 | Integer | Instance online status (0: offline; 1: online)|

**Example request**

```javascript
{
	"padCodes": [
		"AC22030022693"
	],
	"groupIds": [1]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data":[
				{
				"taskId": 1,
				"padCode": "AC21020010001".
				"vmStatus": 1
				}
			]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
10001 | Reboot Failed | Contact Administrator
110004 | Failed to execute reboot command | Reboot again later
110028 | Instance does not exist | Please check if the instance exists.

**Code example**
```java
// java call example
public class SDKExample {
    private final ArmCloudApiService armCloudApiService;

    public SDKExample() {
        ArmCloudConfig armCloudConfig = new ArmCloudConfig();
        armCloudConfig.setOpenUrl("https://openapi.armcloud.net");
        armCloudConfig.setService("armcloud-paas");
        armCloudConfig.setHost("openapi.armcloud.net");
        armCloudConfig.setAk("your access_key_id");
        armCloudConfig.setSk("your secret_access_key");
        armCloudApiService = new ArmCloudApiServiceImpl(armCloudConfig, new DefaultHttpExecutor());
    }

    @Test
    public void test() throws Exception {
        RestartRequest requestParam = new RestartRequest();
        List<String> padCodes = new ArrayList<>();
        padCodes.add("AC22010041147");
        requestParam.setPadCodes(padCodes);
        Result<PadTaskResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_RESTART, requestParam, new TypeReference<Result <PadTaskResponse>>() {});
    }
}
```
#### **Instance reset**
Performs a reset operation on the specified instance to clean up applications and files

**Interface address**

> /openapi/open/pad/reset

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| -------- | -------------- | --------- | -------- | ---------- |
| padCodes | | String[] | Yes | |
| ├─ | AC21020010001 | String | Yes | Instance number |
| groupIds | | Integer[] | no | |
| ├── 1 | Integer | No | Instance Group ID |


**Response parameters**

| parameter name | example value | parameter type | parameter description|
|--- | --- | --- | --- | --- |
|code | 200 | Integer | Status code |
|msg | success | String | response message |
|ts | ************* | Long | timestamp |
|data | |Object[] | |
|├─taskId | 1 | Integer | Task ID|
| ├─padCode | AC21020010001 | String | Instance number |
| ├─vmStatus | 1 | Integer | Instance online status (0: offline; 1: online) |

**Example request**

```javascript
{
	"padCodes": [
		"AC21020010001"
	],
	"groupIds": [1]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717559681604.
	"data": [
		{
			"taskId": 88,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 89.
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

|Error Code |Error Description |Recommendations|
|--- | --- | --- |
|10002 | Reset Failed | Contact Administrator|
|110005 | Failed to execute reset command | reset again later|

**Code example**
```java
// java call example
 ResetRequest requestParam = new ResetRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);
Result<PadTaskResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_RESET, requestParam, new TypeReference<Result& lt;PadTaskResponse>>() {});
```
#### **Query instance attributes**
Queries the attribute information of the specified instance, including system attribute information and setting information.

**Interface address**

> /openapi/open/pad/padProperties

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
|--- | --- | --- | --- | --- | 
|padCode | AC21020010001 | String | Yes | Instance number |

**Response parameters**

| Parameter Name          | Example Value         | Parameter Type | Parameter Description |
|------------------------|-----------------------|-----------------|-----------------------|
| code                   | 200                   | Integer         | Status Code           |
| msg                    | success               | String          | Response Message       |
| ts                     | *************         | Long            | Timestamp             |
| data                   |                       | Object          |                       |
| ├─padCode             | AC21020010001         | String          | Instance Number       |
| ├─modemPropertiesList  |                       | Object[]        | Modem Properties List  |
| ├─├─propertiesName    | IMEI                  | String          | Property Name         |
| ├─├─propertiesValue    | 412327621057784       | String          | Property Value        |
| ├─systemPropertiesList  |                       | Object[]        | System Properties List |
| ├─├─propertiesName    | ro.build.id           | String          | Property Name         |
| ├─├─propertiesValue    | QQ3A.200805.001       | String          | Property Value        |
| ├─settingPropertiesList |                       | Object[]        | Setting Properties List|
| ├─├─propertiesName    | ro.build.tags         | String          | Property Name         |
| ├─├─propertiesValue    | release-keys          | String          | Property Value        |
| ├─oaidPropertiesList   |                       | Object[]        | OAID Properties List  |
| ├─├─propertiesName    | oaid                  | String          | Property Name         |
| ├─├─propertiesValue    | 001                   | String          | Property Value        |


**Example request**

```javascript
{
	"padCode": "AC21020010001"
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"padCode": "AC21020010001".
		"modemPropertiesList": [
			{
				"propertiesName": "IMEI",
				"propertiesValue": "412327621057784"
			}
		],
		"systemPropertiesList": [
			{
				"propertiesName": "ro.build.id",
				"propertiesValue": "QQ3A.200805.001"
			}
		],
		"settingPropertiesList": [
			{
				"propertiesName": "ro.build.tags",
				"propertiesValue": "release-keys"
			}
		],
		"oaidPropertiesList": [
			{
				"propertiesName": "oaid",
				"propertiesValue": "001"
			}
		]
	}
}
```
**Error code**

|Error Code |Error Description |Action Suggestion |
| ----- | -------- | ------ |
|110028 | Instance does not exist | Please check if the instance is correct |

**Code example**
```java
// java call example
PadPropertiesRequest requestParam = new PadPropertiesRequest();
requestParam.setPadCode("AC22010041147");
Result<PadPropertiesResponse> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_GET_PROPERTIES, requestParam,new TypeReference<Result<PadPropertiesResponse>>() {});
```
#### **Modify instance attributes**
Modifies attribute information for the specified instance, including system attributes and settings.

**Interface address**

> /openapi/open/pad/updatePadProperties

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name              | Example Value         | Parameter Type | Required | Parameter Description       |
|-----------------------------|-----------------------|-----------------|----------|-----------------------------|
| padCodes                    |                       | String[]        | Yes      |                             |
| ├─                          | AC21020010001         | String          | Yes      | Instance Number             |
| modemPersistPropertiesList   |                       | Object[]        | No       | Modem Persistent Properties List |
| ├─ propertiesName           | IMEI                  | String          | No       | Property Name               |
| ├─ propertiesValue          | 412327621057784       | String          | No       | Property Value              |
| modemPropertiesList          |                       | Object[]        | No       | Modem Non-Persistent Properties List |
| ├─ propertiesName           | IMEI                  | String          | No       | Property Name               |
| ├─ propertiesValue          | 412327621057784       | String          | No       | Property Value              |
| systemPersistPropertiesList   |                       | Object[]        | No       | System Persistent Properties List |
| ├─ propertiesName           | ro.build.id           | String          | No       | Property Name               |
| ├─ propertiesValue          | QQ3A.200805.001       | String          | No       | Property Value              |
| systemPropertiesList         |                       | Object[]        | No       | System Non-Persistent Properties List |
| ├─ propertiesName           | ro.build.id           | String          | No       | Property Name               |
| ├─ propertiesValue          | QQ3A.200805.001       | String          | No       | Property Value              |
| settingPropertiesList        |                       | Object[]        | No       | Setting Properties List      |
| ├─ propertiesName           | ro.build.tags         | String          | No       | Property Name               |
| ├─ propertiesValue          | release-keys          | String          | No       | Property Value              |
| oaidPropertiesList           |                       | Object[]        | No       | OAID Properties List        |
| ├─ propertiesName           | oaid                  | String          | No       | Property Name               |
| ├─ propertiesValue          | 001                   | String          | No       | Property Value              |



**Response parameters**

| Parameter Name | Example Value       | Parameter Type | Parameter Description            |
|----------------|---------------------|----------------|----------------------------------|
| code           | 200                 | Integer        | Status Code                      |
| msg            | success             | String         | Response Message                 |
| ts             | *************       | Long           | Timestamp                        |
| data           |                     | Object[]       |                                  |
| ├─ taskId      | 1                   | Integer        | Task ID                         |
| ├─ padCode     | AC21020010001       | String         | Instance Number                  |
| ├─ vmStatus    | 1                   | Integer        | Instance Online Status (0: Offline; 1: Online) |



**Example request**
```javascript
{
	"padCodes": [
		"AC21020010001"
	],
	"modemPersistPropertiesList": [
			{
				"propertiesName": "IMEI",
				"propertiesValue": "412327621057784"
			}
		],
	"modemPropertiesList": [
			{
				"propertiesName": "IMEI",
				"propertiesValue": "412327621057784"
			}
		],
		"systemPersistPropertiesList": [
			{
				"propertiesName": "ro.build.id",
				"propertiesValue": "QQ3A.200805.001"
			}
		],
		"systemPropertiesList": [
			{
				"propertiesName": "ro.build.id",
				"propertiesValue": "QQ3A.200805.001"
			}
		],
		"settingPropertiesList": [
			{
				"propertiesName": "ro.build.tags",
				"propertiesValue": "release-keys"
			}
		],
		"oaidPropertiesList": [
			{
				"propertiesName": "oaid",
				"propertiesValue": "001"
			}
		]
}
```

**Example of response**

```json
{
	"code": 200,
	"msg": "success",
	"ts": 1717570916196,
	"data": [
		{
			"taskId": 36,
			"padCode": "AC22030010001",
			"vmStatus": 1
		}
	]
}
```
**Error code**

|Error Code |Error Description |Action Suggestion |
| --- | --- | --- | 
|110011 | Failed to execute modify attribute command | Please try again later |
|110028 | Instance does not exist | Please check if the instance is correct |
|110027 | There is a duplicate item in the instance number set | Please check if there are duplicate instances |

**Code example**
```java
// java call example
UpdatePadPropertiesRequest requestParam = new UpdatePadPropertiesRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);

List<PadPropertiesSub> subs = new ArrayList<>();
PadPropertiesSub sub = new PadPropertiesSub();
sub.setPropertiesName("oaid");
sub.setPropertiesValue("123456789");
subs.add(sub);
requestParam.setSystemPropertiesList(subs);
Result<List<GeneratePadResponse>> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_UPDATE_PROPERTIES, requestParam, new TypeReference<Result<List<GeneratePadResponse>>() {});
```
#### **Instance details**
Queries the attribute information of the specified instance, including system attribute information and setting information.

**Interface address**

> /openapi/open/pad/padDetails

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name   | Example Value        | Parameter Type | Required | Parameter Description         |
|------------------|----------------------|----------------|------|-------------------------------|
| page             | 1                    | Integer        | No     | Page Number                   |
| rows             | 10                   | Integer        | No     | Records per Page              |
| padCodes         |                      | String[]       | No   |                               |
| ├─               | AC21020010391        | String         | No   | Instance Number               |
| padIps           |                      | String[]       | NO   |                               |
| ├─               | ***********          | String         | NO   | Instance IP                   |
| vmStatus         | 1                    | String         | No   | Instance Online Status: 0 - Offline, 1 - Online |
| controlStatus    | 1                    | String         | No   | Control Status (Streaming Status): 1 - Uncontrolled, 2 - Controlled |
| faultStatus      | 14                   | String         | No   | Instance Running Status: 14 - Exception, Other - Normal |
| deviceStatus     | 0                    | String         | No   | Physical Machine Online Status: 0 - Offline, 1 - Online |
| groupId          | 2                    | Integer        | No   | Group ID                      |
| idcCode          | HNCS-C-01            | String         | No   | Data Center Code              |



**Response parameters**

| Parameter Name    | Example Value         | Parameter Type | Parameter Description                                  |
|-------------------|-----------------------|----------------|-------------------------------------------------------|
| code              | 200                   | Integer        | Status Code                                           |
| msg               | success               | String         | Response Message                                      |
| ts                | *************         | Long           | Timestamp                                             |
| data              |                       | Object         |                                                       |
| ├─ page           | 1                     | Integer        | Current Page                                         |
| ├─ rows           | 10                    | Integer        | Number per Page                                       |
| ├─ size           | 1                     | Integer        | Current Page Count                                    |
| ├─ total          | 1                     | Integer        | Total Record Count                                    |
| ├─ totalPage      | 1                     | Integer        | Total Page Count                                      |
| ├─ pageData       |                       | object[]       | List                                                  |
| ├─ ├─ padCode     | VP21020010391         | String         | Instance Number                                       |
| ├─ ├─ imageId     | android:v10           | String         | Image ID                                             |
| ├─ ├─ deviceLevel  | q2-6                  | String         | Instance Specification (q2-6 Six Core, q2-4 Four Core) |
| ├─ ├─ padStatus   | 12                    | Integer        | Instance Status (10 - Running, 11 - Restarting, 12 - Resetting, 13 - Upgrading, 14 - Exception, 15 - Not Ready, 16 - Upgrading) |
| ├─ ├─ deviceStatus | 1                     | Integer        | Physical Machine Status (0 - Offline, 1 - Online)    |
| ├─ ├─ online      | 1                     | Integer        | Instance Online Status (0 - Offline, 1 - Online)     |
| ├─ ├─ streamStatus | 0                    | Integer        | Instance Streaming Status (0 - Idle, 1 - Streaming)   |
| ├─ ├─ dataSize    | 234493726720          | Long           | Total Storage Capacity (Bytes)                        |
| ├─ ├─ dataSizeUsed | 179189956608         | Long           | Used Storage Capacity (Bytes)                         |
| ├─ ├─ dcInfo      |                       | Object         | Data Center Information                                |
| ├─ ├─ ├─ dcCode   | dc01                  | String         | Data Center Code                                      |
| ├─ ├─ ├─ dcName   | 长沙-01               | String         | Data Center Name                                      |
| ├─ ├─ ├─ area     | 长沙                   | String         | Area                                                 |
| ├─ ├─ ├─ ossEndpoint | https://xxx.armcloud.net | String     | OSS Public Interface Address                           |
| ├─ ├─ ├─ ossEndpointInternal | https://xxx.armcloud.net | String | OSS Internal Interface Address                         |
| ├─ ├─ ├─ ossFileEndpoint | https://xxx.armcloud.net | String | Access File Address                                   |
| ├─ ├─ ├─ ossScreenshotEndpoint | https://xxx.armcloud.net | String | Access Screenshot Address                              |


**Example request**

```javascript
{
	"page": 1,
	"rows": 10,
	"padCodes": ["AC21020010391"],
	"padIps":["***********"],
	"vmStatus": "1",
	"controlStatus": "1",
	"faultStatus": "14",
	"deviceStatus": "0"
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"page": 1,
		"rows": 10,
		"size": 1,
		"total": 1.
		"totalPage": 1,
		"pageData": [
			"padCode": "AC21020010391".
			"imageId": "andorid:v10",
			"deviceLevel": "q2-6",
			"dcInfo": {
				"dcCode": "dc01".
				"dcName": "Changsha-01".
				"area": "Changsha".
				"ossEndpoint": "https://xxx.armcloud.net",
				"ossEndpointInternal": "https://xxx.armcloud.net",
				"ossFileEndpoint": "https://xxx.armcloud.net",
				"ossScreenshotEndpoint": "https://xxx.armcloud.net",
			},
			"padStatus": 12.
			"deviceStatus": 1,
			"online": 1.
			"streamStatus": 0,
			"dataSize": 234493726720.
			"dataSizeUsed": 179189956608
		]
	}
}

```
**Code example**
```java
// java call example
PadDetailsRequest requestParam = new PadDetailsRequest();
List<String> padCodes = new ArrayList<>();
padCodes.add("AC22010041147");
requestParam.setPadCodes(padCodes);
Result<Page<PadDetailsResponse>> result = armCloudApiService.execute(ArmCloudApiEnum.PAD_DETAILS, requestParam,new TypeReference<Result<Page<PadDetailsResponse>>() {});
```
#### **Stop Push Flow**
Stop the specified instance from pushing the stream and disconnect the instance.

**Interface address**

> /rtc/open/room/dissolveRoom

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| ------- | ------------- | -------- | -------- | -------- |
|padCodes | String[] | yes | | |
| ├─ | AC11010000031 | String | Yes | Instance number |
| ├─ | AC22020020700 | String | Yes | Instance number |

**Response parameters**

| Parameter Name   | Example Value         | Parameter Type | Parameter Description          |
|------------------|-----------------------|----------------|--------------------------------|
| code             | 200                   | Integer        | Status Code                    |
| msg              | success               | String         | Response Message               |
| ts               | *************         | Long           | Timestamp                      |
| data             |                       | Object[]       |                                |
| ├─successList    |                       | Object[]       | Success Collection              |
| ├─├─padCode      | AC11010000031         | String         | Instance Number                 |
| ├─failList       |                       | Object[]       | Failure Collection              |
| ├─├─padCode      | AC22020020700         | String         | Instance Number                 |
| ├─├─errorCode    | 120005                | Integer        | Error Code                     |
| ├─├─errorMsg     | Instance does not exist| String         | Reason for Failure             |



**Example request**

```javascript
{
    "padCodes": ["AC11010000031", "AC22020020700"]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
     "data": {
	 	  "successList": [
              {
                  "padCode": "AC11010000031"
              }
          ],
          "failList": [
		  	 {
                  "padCode": "AC22020020700".
				  "errorCode": 120005.
				  "errorMsg": "Instance does not exist"
              }
		  ]
     }
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ----
120005 | Instance does not exist | Please check if the instance number is correct.
120004 | Abort Push Error, Command Service Exception | Try Again Later



#### **Bulk Request for RTC Connection Token**

Batch request RTC Token connection information for multiple instances under the current account, and the connection information is returned in groups based on Pad.

> No RTC Toekn message is returned when the Pad is unable to connect, the `msg` field shows the reason for the inability to connect.
>
> Currently, this interface only supports the production of ArmcloudRTC tokens, if there is an exception message such as `This interface does not support this feature at the moment`, please contact the relevant personnel to reconfigure the push flow information.
>
> The generated token does not support refreshing to extend the validity period for the time being. When it expires, you need to apply for a new token.
>
> Encrypted data needs to be decrypted using AES GCM mode with padCode key.

**Interface address**

> /rtc/open/room/batchApplyToken

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value        | Parameter Type | Required | Parameter Description                                                                                                   |
|--------------------|----------------------|----------------|----------|-------------------------------------------------------------------------------------------------------------------------|
| userId             | 202                  | String         | Yes      | Custom user ID defined by the business party. This field is used to generate room information. A user must be in the same room as the GameServer. The caller needs to ensure user-level uniqueness; using the same userId to generate different Tokens for connecting to the same GameServer will cause the previously established connection to disconnect. |
| expire             | 3600                 | Integer        | No       | Token validity period (unit: seconds. Default is 86400).                                                              |
| pads               |                      | Object[]       | Yes      | List of instances                                                                                                      |
| ├─padCode          | AC11010000031        | String         | Yes      | Instance Number                                                                                                        |
| ├─videoStream      |                      | Object         | No       | Streaming configuration                                                                                                 |
| ├─├─resolution     | 07                   | String         | No       | Resolution                                                                                                             |
| ├─├─frameRate      | 30                   | String         | No       | Frame Rate                                                                                                            |
| ├─├─bitrate        | 2000                 | String         | No       | Bitrate                                                                                                               |


**Response parameters**

| Parameter Name     | Example Value                         | Parameter Type | Parameter Description                                    |
|--------------------|---------------------------------------|----------------|----------------------------------------------------------|
| code               | 200                                   | Integer        | Status Code                                              |
| msg                | success                               | String         | Response Message                                         |
| ts                 | *************                        | Long           | Timestamp                                                 |
| data               |                                       | Object[]       |                                                          |
| ├─roomToken        | 001j7Tb2jAyAzR6UtLv3cgclCFhw6Q==    | String         | Token                                                     |
| ├─roomCode         | AC22030010181202                      | String         | Room Number                                              |
| ├─appId            | j7Tb2GcE9rN5oF6xP3A4qwer             | String         | Application ID                                           |
| ├─padCode          | AC22030010181                         | String         | Instance ID                                              |
| ├─signalServer     | LnBbVX4AD1CA4uyoN1kXp:P8H01PaGZDHEFD      | String         | Signaling Address (requires decryption using AES GCM mode)|
| ├─stuns            | pL25iYgaA12RNmdCYR/:SUJD21Bz4S6HE88GzVN    | String         | STUN Address (requires decryption using AES GCM mode)   |
| ├─turns            | doF22kA7Z6OiVP1br29:rA1R4d6Vyk9e          | String         | TURN Address (requires decryption using AES GCM mode)   |
| ├─msg              | connect pad fail                      | String         | Error Message                                            |




**Example request**

```javascript
{
	"userId": "202".
	"expire": 3600.
	"pads": [
		{
			"padCode": "AC22010010842".
			"videoStream ": {
				"resolution": "1",
				"frameRate": "30",
				"bitrate": "2000"
			}
		},
		{
			"padCode": "AC22030010181".
			"videoStream ": {
				"resolution": "1",
				"frameRate": "30",
				"bitrate": "2000"
			}
		}
	]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1721305008916.
	"data": [
		{
			"roomToken": null,
			"roomCode": "AC22010010842202".
			"appId": "j7Tb2GcE9rN5oF6xP3A4qwer", "label".
			"streamType": 2,
			"videoCodec": "",
			"reportSdkLog": false,
			"padCode": "AC22010010842".
			"msg": "connect pad fail",
			"signalServer": null,
			"stuns": null,
			"turns": null
		},
		{
			"roomToken": " 001j7Tb2GcE9rN5oF6xP3A4qwerNwDyCHIRtQrGxZABAAC1ZuzKkAEAABAAQUMyMjAzMDAxMDE4MTIwMgMAMjAyAQAEALVm7MqQAQAAIABpLvj5zX3dnyN/ 8UvRsLJnHWA4zR6UtLv3cgclCFhw6Q==",
			"roomCode": "AC22030010181202".
			"appId": "j7Tb2GcE9rN5oF6xP3A4qwer", "label".
			"streamType": 2,
			"videoCodec": "",
			"reportSdkLog": false,
			"padCode": "AC22030010181".
			"msg": null,
			"signalServer": "LnBbVX4AD1CA4uyoN1kXp:P8H01PaGZDHEFDsnU6nRCbOFzvL2smbG9HxKh+XP5WHC", "label".
			"stuns": "pL25iYgaA12RNmdCYR/:SUJD21Bz4S6HE88GzVN8rANlfL9925iaHW+ilJAaWldPpoBKqwoEq0Ggon0HhDc4a6v0pg=",
			"turns": "doF22kA7Z6OiVP1br29:rA1R4d6Vyk9efIFX6qPPMyKs7OhmxFA7xBr65P8NA/Rxb31Js6VOaO3Zrtd3h9uM/". mNYUy5mJOQ4j8TJ8DjfBFaEHVNOAcF5tzgbg8iksGhNONfv8hHw=="
		}
	]
}
```

**Error code**

| Error Codes | Error Descriptions | Operating Suggestions |
| ------ | -------------------- | ------------------------ |
| 120005 | Instance does not exist | Please check if the instance number is correct |
| 120007 | This interface does not support this feature at the moment | Contact the relevant personnel to change the push flow configuration |



#### **Physical machine reboot**

Power down and restart the physical machine.

**Interface address**

> /openapi/open/device/powerReset

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value     | Parameter Type | Required | Parameter Description                   |
|----------------|--------------------|----------------|----------|----------------------------------------|
| deviceIps      |                    | String[]       | Yes      |                                        |
| ├─             | ************       | String         | Yes      | Physical Device IP                     |
| type           | 2                  | String[]       | Yes      | Restart Type: 2 - Power Cycle Restart  |


**Response parameters**

| Parameter Name   | Example Value       | Parameter Type | Parameter Description         |
|-------------------|--------------------|----------------|-------------------------------|
| code              | 200                | Integer        | Status Code                   |
| msg               | success            | String         | Response Message              |
| ts                | *************      | Long           | Timestamp                     |
| data              |                    | Object[]       |                               |
| ├─ taskId        | 1                  | Integer        | Task ID                       |
| ├─ deviceIp      | ************       | String         | Physical Device IP            |
| ├─ errorMsg      | ""                 | String         | Reason for failure            |
| ├─ deviceOutCode | AC22030010000      | String         | Cloud Machine ID              |


**Example request**

```javascript
{
	"deviceIps": [
		"************"
	],
	"type":2
}
```
**Example of response**

```javascript
{
    "code": 200.
    "msg": "success",
    "ts": *************.
    "data": [
        {
            "taskId": 1,
            "deviceIp": "************",
            "errorMsg": null,
            "deviceOutCode": "AC22030010000"
        }
    ]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110030 | Failed to execute power restart command, parameter request is not compliant | Parameter request is not compliant
110032 | Physical IP does not exist | Please check if the physical device IP is correct.
110033 | Failed to execute the power failure restart command | Contact the appropriate person.



#### **Request for RTC shared room token**

Used to implement multiple instances in a room, the instances receive the room broadcast message for processing and implementation of obtaining the public flow

> Room number generation rule: timer + userId + paas userId
> e.g. terimer = pc, userId = 123, paas userId = qwer
>
> Room number: pc123qwer

**Interface address**

> /rtc/open/room/share/applyToken

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name      | Example Value                    | Parameter Type | Is Required | Parameter Description                          |
|---------------------|----------------------------------|----------------|-------------|------------------------------------------------|
| userId              | 202                              | String         | Yes         | Business user ID of the caller                |
| terminal            | pc                               | String         | Yes         | Terminal                                       |
| expire              | 3600                             | Integer        | No          | Token validity period in seconds. Default is 1 hour |
| pushPublicStream    | true                             | Boolean        | No          | Whether to push public stream (default is false) |
| pads                |                                  | Array          | Yes         | List of instances to join the shared room     |
| ├─padCode           | AC22010010842                    | String         | Yes         | Instance ID                                   |
| ├─├─videoStream     |                                  | Object         | No          | Streaming configuration                         |
| ├─├─├─videoStream   | 1                                | String         | No          |                                                |
| ├─├─├─frameRate     | 30                               | String         | No          |                                                |
| ├─├─├─bitrate       | 2000                             | String         | No          |                                                |

**Response Parameters**

| Parameter Name | Example Value                                  | Parameter Type | Parameter Description |
|----------------|------------------------------------------------|----------------|-----------------------|
| code           | 200                                            | Integer        | Status Code           |
| msg            | success                                        | String         | Response Message      |
| ts             | *************                                  | Long           | Timestamp             |
| data           |                                                | Object         |                       |
| ├─roomToken    | 00165b7AS149e52467a4016f050b8cQQBDjDJKuTAb       | String         | Room Token            |
| ├─roomCode     | android_12345                                  | String         | Room Number           |
| ├─appId        | 65b749e52467a4016f050b8c                       | String         | Application ID        |


**Example request**

```javascript
{
	"userId": "202".
	"terminal": "pc".
	"expire": 3600.
	"pushPublicStream": true,
	"pads": [
		{
			"padCode": "AC22010010842".
			"videoStream": {
				"resolution": "1",
				"frameRate": "30",
				"bitrate": "2000"
			}
		},
		{
			"padCode": "AC22030010181".
			"videoStream": {
				"resolution": "1",
				"frameRate": "30",
				"bitrate": "2000"
			}
		}
	]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": null,
	"data": {
		"roomToken": " 00165b7AS149e52467a4016f050b8cQQBDjDJKuTAbnZVwU52UOAENSTTExMDEwMDAwMDExAwAxMjMFAAAAAAAAXBTnZQEAXBTnZQIAXBTnZQMAXBTnZQQQAAAAAACAADCbuyT9crLX9MNUCWyFhsFXwb4nuFPxfgE7MqHjv4yQ =".
		"roomCode": "android_12345",
		"appId": "65b749e52467a4016f050b8c"
	}
}
```



#### **Modify instance time zone**

**Interface address**

> /openapi/open/pad/updateTimeZone

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value    | Parameter Type | Is Required | Parameter Description    |
|----------------|------------------|----------------|-------------|--------------------------|
| timeZone       | Asia/Shanghai    | String         | Yes         | UTC Standard Time        |
| padCodes       |                  | Array          | Yes         | List of Instances        |

**Response Parameters**

| Parameter Name | Example Value     | Parameter Type | Parameter Description                             |
|----------------|-------------------|----------------|--------------------------------------------------|
| code           | 200               | Integer        | Status Code                                      |
| msg            | success           | String         | Response Message                                  |
| ts             | *************     | Long           | Timestamp                                         |
| data           |                   | Object         |                                                  |
| ├─taskId       | 24                | Long           | Task ID                                          |
| ├─padCode      | AC22030010001     | String         | Room Number                                      |
| ├─vmStatus     | Instance Status    | Integer        | Instance Online Status (0: Offline; 1: Online)  |


**Example request**

```javascript
{
	"padCodes": [
		"AC32010140003"
	],
	"timeZone": "Asia/Shanghai"
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC32010140003",
			"vmStatus": 1
		}
	]
}
```



#### **Modify instance language**

**Interface address**

> /openapi/open/pad/updateLanguage

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value | Parameter Type | Is Required | Parameter Description |
|----------------|---------------|----------------|-------------|-----------------------|
| language       | zh            | String         | Yes         | Language              |
| country        | CN            | String         | No          | Country               |
| padCodes       |               | Array          | Yes         | List of Instances     |

**Response Parameters**

| Parameter Name | Example Value     | Parameter Type | Parameter Description                       |
|----------------|-------------------|----------------|--------------------------------------------|
| code           | 200               | Integer        | Status Code                                |
| msg            | success           | String         | Response Message                            |
| ts             | *************     | Long           | Timestamp                                   |
| data           |                   | Object         |                                            |
| ├─taskId       | 24                | Long           | Task ID                                    |
| ├─padCode      | AC22030010001     | String         | Room Number                                |
| ├─vmStatus     | Instance Status    | Integer        | Instance Online Status (0: Offline; 1: Online) |


**Example request**

```javascript
{
	"padCodes": [
		"AC32010140026"
	],
	"language": "zh",
	"country": ""
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC32010140026",
			"vmStatus": 1
		}
	]
}
```
#### **Upgrade of the real ADI template**
Replacing ADI templates for a given cloud machine

**Interface address**

> /openapi/open/pad/replaceRealAdiTemplate

> **Request Method**

> POST

**Request data type

> application/json

**Request Body Parameters**

| Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description |
| ------- |---------------|----------|------|---------|
|padCodes | | String[] | yes | |
| ├─ | AC21020010001 | String | Yes | Instance number |
|wipeData | false | Boolean | Yes | Whether or not to wipe data |
|realPhoneTemplateId | 186 | Long | Yes | cloud real phone template id |

**Response parameters**

|Parameter name | Example value | Parameter type | Parameter description|
|--- | --- | --- | --- | ---|
|code | 200 | Integer | Status Code|
|msg | success | String | Response message|
|ts | ************* | Long | Timestamp |
|data || Object[] ||
||taskId | 1 | Integer | TaskId|
|├─padCode | AC21020010001 | String | Instance number|
| ├─vmStatus | 1 | Integer | Instance online status (0: offline; 1: online) |

**Example request**

```javascript
{“padCodes”:[“AC32010250011”], “wipeData”:true, “realPhoneTemplateId”:186}
```
**Response example**

```javascript
{“code”:200, “msg”: “success”, “ts”:1736326542985, “data”:[{“taskId”:10074, “padCode”: “AC32010250011”, “errorMsg”:null}]}
```
**Error Code**

Error Code | Error Description | Operation Recommendation
--- | --- | ---
110028 | Instance does not exist | Contact the administrator.
110064 | The current instance does not meet the conditions for upgrading to a real machine, please check the instance | Check if the instance is a real machine.
110099 | ADI template does not exist, please check parameters | check ADI template information


#### **Modification of instance SIM card information**

**Interface address**

> /openapi/open/pad/updateSIM

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name      | Example Value         | Parameter Type | Is Required | Parameter Description                                       |
|---------------------|-----------------------|----------------|-------------|------------------------------------------------------------|
| imei                | 868034031518269       | String         | No          | IMEI Number                                                |
| imeisv              | 00                    | String         | No          | IMEI Version Number                                        |
| meid                | A0000082C65F6C        | String         | No          | Mobile Equipment Identifier (MEID)                         |
| operatorLongname    | CHINA MOBILE          | String         | No          | Full Name of the Operator                                   |
| operatorShortname   | CMSS                  | String         | No          | Short Name of the Operator                                  |
| operatorNumeric     | 455555                | String         | No          | Network Operator ID (MCCMNC)                               |
| spn                 | China                 | String         | No          | SIM Card Operator Name                                     |
| iccid               | 89860002191807255576  | String         | No          | SIM Card Number                                            |
| imsi                | ***************       | String         | No          | Prefix for SIM Card Operator Number: MCC (3 digits) + MNC (2 or 3 digits) |
| phonenum            | 18574771704           | String         | No          | Phone Number                                              |
| mcc       | 502                  | String   | No          | Network country                                |
| mnc       | 146                  | String   | No          | Mobile device network code                               |
| padCodes            |                       | Array          | Yes         | List of Instances                                          |

**Response Parameters**

| Parameter Name      | Example Value         | Parameter Type | Parameter Description                                      |
|---------------------|-----------------------|----------------|-----------------------------------------------------------|
| code                | 200                   | Integer        | Status Code                                               |
| msg                 | success               | String         | Response Message                                          |
| ts                  | *************         | Long           | Timestamp                                                |
| data                |                       | Object         |                                                           |
| ├─taskId           | 24                    | Long           | Task ID                                                  |
| ├─padCode          | AC22030010001         | String         | Room Number                                              |
| ├─vmStatus         | Instance Status       | Integer        | Instance Online Status (0: Offline; 1: Online)          |


**Example request**

```javascript
{
  "padCodes": ["AC32010230011"],
          "imei": "868034031518269",
          "imeisv": "00",
          "meid": "A0000082C65F6C",
          "operatorLongname": "CHINA MOBILE",
          "operatorShortnam": "CMSS",
          "operatorNumeric": "455555",
          "spn": "China",
          "iccid": "89860002191807255576",
          "imsi": "***************",
          "phonenum": "861234566",
          "netCountry": "US",
          "simCountry": "US",
          "type": "9",
          "mcc": "502",
          "mnc": "146",
          "tac": "871420",
          "cellid": "870091003",
          "narfcn": "99240",
          "physicalcellid": "6C"
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC32010140033",
			"vmStatus": 1
		}
	]
}
```



#### **Set instance latitude and longitude**

**Interface address**

> /openapi/open/pad/gpsInjectInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value | Parameter Type | Is Required | Parameter Description  |
|----------------|---------------|----------------|-------------|------------------------|
| longitude      | 116.397455    | Float          | Yes         | Latitude               |
| latitude       | 39.909187     | Float          | Yes         | Longitude              |
| padCodes       |               | Array          | Yes         | List of Instances      |

**Response Parameters**

| Parameter Name | Example Value      | Parameter Type | Parameter Description                                   |
|----------------|--------------------|----------------|--------------------------------------------------------|
| code           | 200                | Integer        | Status Code                                            |
| msg            | success            | String         | Response Message                                       |
| ts             | *************      | Long           | Timestamp                                             |
| data           |                    | Object         |                                                        |
| ├─taskId      | 24                 | Long           | Task ID                                               |
| ├─padCode     | AC22030010001      | String         | Room Number                                           |
| ├─vmStatus    | Instance Status     | Integer        | Instance Online Status (0: Offline; 1: Online)       |


**Example request**

```javascript
{
	"padCodes": [
		"AC32010030001"
	],
	"longitude": 116.397455.
	"latitude": 39.909187
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC32010030001",
			"vmStatus": 1
		}
	]
}
```

#### **Query instance agent information**

**Interface address**

> /openapi/open/network/proxy/info

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| -------- | ------ | -------- | -------- | -------- | 
| padCodes | | Array | Yes | List of instances |

**Response parameters**

| Parameter Name | Sample Value | Parameter Type | Parameter Description |
| ---------- | ------------- | -------- | -------------------------------- |
| code | 200 | Integer | status code |
| msg | success | String | response message |
| ts | ************* | Long | timestamp |
| data | | Object | |
| ├─taskId | 24 | Long | Task ID |
| ├─padCode | AC22030010001 | String | Room Number |
| ├─vmStatus | Instance Status | Integer | Instance Online Status (0: offline; 1: online) |

**Example request**

```javascript
{
  "padCodes": [
    "AC32010140012"
  ]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1726742932284.
	"data": [
		{
			"taskId": 3612,
			"padCode": "AC32010140012",
			"vmStatus": 1
		}
	]
}
```

#### **Modify Instance Android Attributes**

> props field description: this field is a key-value definition.

**Interface address**

> /openapi/open/pad/updatePadAndroidProp

**Request method**

> POST

**Request data type**

> application/json

**Request Body Parameters**

| Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description |
| ------------------------ | ------------- | -------- | -------- | ------------------------------- |
| padCode | AC32010210001 | String | no | instance id |
| restart | false | Boolean | No | Automatically restart when setup is complete (default false) |
| props | {} | Object | Yes | System Properties |
| ├─ro.product.vendor.name | OP52D1L1 | String | Yes | Property Settings |

**Response Parameters**

| Parameter Name | Example Value | Parameter Type | Parameter Description |
| --------- | ------------- | -------- | -------- |
| code | 200 | Integer | Status Code |
| msg | success | String | Response message |
| ts | ************* | Long | Timestamp |
| data | Object | String | |
| ├─taskId | 24 | Long | Task id |
| ├─padCode | AC32010210001 | String | instance id |

**Example request**

```
{
"padCode": "AC32010210001",
"props": {
"ro.product.vendor.name": "OP52D1L1"
}, "restart": false
"restart": false
}
```

**Response Example**

```
{
   "code": 200,
   "msg": "success",
   "ts": 1730192434383,
   "data": {
      "taskId": 11, 
      "padCode": "AC32010210001"
   }
}
```



#### **One Click New Machine**
> Note: One Click New Machine will erase all system data. Please use with caution!

One click new machine function, will be the current instance of data all clear, and reset Android properties!

- Virtual machine directly set Android properties, and then clear all the data.
- Cloud machine directly clear all the data (equivalent to reset)


**Interface address **

> /openapi/open/pad/replacePad

**Request Method** > POST

> POST

**Request data type** > application/json

> application/json

**Request Body Parameters

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description

| Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description |
| ------------------------ |---------------| -------- |-----------------| ------------------------------- |
| padCode | []            | Arr | Yes             | list of instance ids |
countryCode | SG  | String | no              | country code(Country code (look specifically at: https://chahuo.com/country-code-lookup.html)) |




**Response Parameters**

Parameter name | Example value | Parameter type | Parameter description
--- | ---------------| ---------| ---
code | 200 | Integer | Status code
msg | success | String | Response message
ts | 1721739857317 | Long | Timestamps
data | | Object |
├─taskId | 12818 | Long | TaskId
├─padCode | AC22030010124 | String | Instance Number
├─vmStatus | 2 | Intger | Instance Status

```json
{ "padCodes": [
  "AC32010030001"
]
}
```

**Example of response**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1732270378320,
  "data": {
    "taskId": 8405,
    "padCode": "AC32010030001",
    "vmStatus": 2
  }
}
```


#### **Modify Contacts**
> One of the fileUniqueId and info fields is required.

**Interface address **

> /openapi/open/pad/updateContacts

**Request Method** > POST

> POST

**Request data type** > application/json

> application/json

**Request Body Parameters

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | ---
padCodes | [] | Arr | 是 | padCodes
fileUniqueId | cfca25a2c62b00e065b417491b0cf07ffc | String | No | fileUniqueId
info | {} | Object | No | info
├─firstName | tom         | String   | No | firstName
├─phone | 13111111111         | String    | No| phone
├─email | <EMAIL>         | String    | No| email

**Response Parameters**

Parameter name | Example value | Parameter type | Parameter description
--- | ---------------| ---------| ---
code | 200 | Integer | Status code
msg | success | String | Response message
ts | 1721739857317 | Long | Timestamps
data | | Object |
├─taskId | 12818 | Long | TaskId
├─padCode | AC22030010124 | String | Instance Number

```json
{
  "fileUniqueId": "cfca25a2c62b00e065b417491b0cf07ffc",
  "info": {
    "firstName": "tom",
    "phone": "13111111111",
    "email": "<EMAIL>"
  },
  "padCodes": [
    "AC32010180326"
  ]
}
```

** Example of response **

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1730192434383,
  "data": {
    "taskId": 11,
    "padCode": "AC32010210001",
    "vmStatus": 0
  }
}
```

<!-- #### **Instance data backup**
1. Backup the data of the current instance to the cloud space, and then you can restore the data to other instances from the cloud space. The data backup method is full backup, and the contents of the backup data include the following

- Installed APP
- All data on the disk of the instance (gallery files, downloaded files, etc.)
- Change machine properties

2. Data Backup Name Rule: [backupNamePrefix_]padCode_yyyyMMddHHmmss,

For example backupNamePrefix is ****test**** and padCode is ****AC32010100001****. The full name of the backup name is ****test_AC32010100001_20241211174508****

**Interface address**

> /openapi/open/pad/data/backup

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value | Parameter Type | Is Required | Parameter Description  |
--- | --- | --- | --- | --- |
padCode | AC32010100001 | String | Yes | Instance Id
backupNamePrefix | 202411221615 | String | No | Data backup name prefix


**Response Parameters**

| Parameter Name | Example Value      | Parameter Type | Parameter Description                                   |
|----------------|--------------------|----------------|--------------------------------------------------------|
| code           | 200                | Integer        | Status Code                                            |
| msg            | success            | String         | Response Message                                       |
| ts             | *************      | Long           | Timestamp                                             |
| data           |                    | Object         |                                                        |
| ├─taskId      | 24                 | Long           | Task ID                                               |
| ├─padCode     | AC22030010124      | String         | Room Number                                           |

**Example request**

```json
{
  "padCodes": [
    "AC32010100001"
  ],
  "backupNamePrefix": "test"
}
```

**Example of response**

```json
{
  "code": 200,
  "msg": "success",
  "ts": 1733212452565,
  "data": [
    {
      "taskId": 2447,
      "padCode": "AC32010100001",
      "backupName": "test_AC32010100001_20241203155412"
    }
  ]
}
``` -->


<!-- #### **Instance Data Restore**
> Tips: Since the data is replaced in full, there is a possibility that the current instance may cause data loss. Please use with caution!

Restore the data backed up to CloudSpace to the specified instance, the restored data includes the following

- Installed APP
- All data on the instance disk (gallery files, downloaded files, etc.)
- change the properties of the machine


**interface address**

> /openapi/open/pad/data/restore

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value      | Parameter Type | Parameter Description|
--- | --- | --- | --- | 
padCodes | [] | Arr | 是 | List of instance ids
backupName | 202411221615 | String | 是 | Data Backup Name


**Response Parameters**

| Parameter Name | Example Value | Parameter Type | Is Required | Parameter Description  |
--- | --- | --- | --- | ---
code | 200 | Integer | status code
msg | success | String | response message
ts | 1721739857317 | Long | Timestamp
data |  | Object |
├─taskId | 12818 | Long | Task ID
├─padCode | AC22030010124 | String | Instance Id

**Example request**

```json
{
  "padCodes": [
    "AC32010030001"
  ],
  "backupName": "202411221615"
}
```

**Example of response**

```json
{
  "code": 200,
  "msg": "success",
  "ts": *************,
  "data": [
    {
      "taskId": 23,
      "padCode": "AC22030010124",
      "vmStatus": 1
    }
  ]
}
``` -->


#### **Instance of setting up a proxy**

**Interface address**

> /openapi/open/network/proxy/set

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| -------- | ----------- | -------- | -------- | -------- |
| account | 2222 | String | no | account |
| password | 2222 | String | no | password |
| ip | *********** | String | no | ip |
| port | 2222 | Integer | no | port |
| enable | true | Boolean | yes | enable |
| padCodes | | Array | Yes | List of instances |
| proxyType |   vpn          | String    | no       | Support parameters：proxy、vpn |
| proxyName |   socks5          | String    | no       | Support parameters：socks5、http-relay |


**Response parameters**

| Parameter Name | Sample Value | Parameter Type | Parameter Description |
| ---------- | ------------- | -------- | -------------------------------- |
| code | 200 | Integer | status code |
| msg | success | String | response message |
| ts | ************* | Long | timestamp |
| data | | Object | |
| ├─taskId | 24 | Long | Task ID |
| ├─padCode | AC22030010001 | String | Room Number |
| ├─vmStatus | Instance Status | Integer | Instance Online Status (0: offline; 1: online) |

**Example request**

```javascript
{
  "padCodes": [
    "AC32010140023"
  ],
  "account": "2222",
  "password": "2222",
  "ip": "***********",
  "port": 2222.
  "enable": true
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC32010140023",
			"vmStatus": 1
		}
	]
}
```

### Server Configuration

#### **Query the list of types supported by the server**

**interface address**

> /openapi/open/config/selectList

**request method**

> GET

**Request data type**

> parameterless



**response parameter**

|parameter name | example value           | Parameter type    | Parameter Description                   |
|--- |---------------|---------|------------------------|
|code | 200           | Integer |                        |
|msg | success       | String  ||
|ts | ************* | Long    ||
|data |               | Object  ||
|├─callbackName | "File Upload Tasks"        | String  | Callback Type Name                 |
|├─callbackType | "4"          | String  | Callback Type                   |
|├─id | 14         | Long    | ID of the callback type (subsequent new modifications need to pass in this parameter) |



**Example of a request**

**Response Example**

```javascript
{
    "code": 200,
        "data": [
        {
            "callbackName": "interface address test",
            "callbackType": "4",
            "id": 14
        },
        {
            "callbackName": "interface address local",
            "callbackType": "5",
            "id": 15
        },
        {
            "callbackName": "interface address",
            "callbackType": "6",
            "id": 16
        }
    ],
        "msg": "success",
        "ts": 1734500966127
}
```

#### **Query the current user-configured callback address**

**interface address**

> /openapi/open/config/selectCallback

**request method**

> GET

**Request data type**

> parameterless



**response parameter**

|parameter name | example value           | Parameter type    | Parameter Description |
|--- |---------------|---------|-----------------------|
|code | 200           | Integer |                       |
|msg | success       | String  ||
|ts | ************* | Long    ||
|data |        http://www.baidu.com       | String  | Configured Corresponding Address               |




**Example of a request**

**Response Example**

```javascript
{"code":200,"data":"http://www.baidu.com","msg":"success","ts":1734501602763}
```



#### **Add callback address configuration**

**interface address**

> /openapi/open/config/insertCallback

**request method**

> POST

**Request data type**

> application/json

**Request Query Parameters**


parameter name | example value              | Parameter type | Required or not | Parameter Description
--- |------------------| --- | --- | ---
callbackIdList | [1,2,3]          | Integer[] | be |A collection of callback type IDs (obtained from the type list interface of the query server support configuration)
├─callbackUrl | ***********/test | String | be |Receive task callback configuration URL



**response parameter**

|parameter name | example value           | Parameter type    | Parameter Description       |
|--- |---------------|---------|------------|
|code | 200           | Integer |            |
|msg | success       | String  ||
|ts | ************* | Long    ||
|data | 2             | String  | Configure the number of successful types |




**Example of a request**
```javascript
{"callbackIdList":[21,14],"callbackUrl":"localhost:8080/callback"}

```
**Sample response**

```javascript
{"code":200,"data":2,"msg":"success","ts":1734502541732}
```

#### **Modify the callback address configuration**

**interface address**

> /openapi/open/config/updateCallback

**request method**

> POST

**Request data type**

> application/json

**Request Query Parameters**


parameter name | example value              | Parameter type | Required or not | Parameter Description
--- |------------------| --- | --- | ---
callbackIdList | [1,2,3]          | Integer[] | be |A collection of callback type IDs (obtained from the type list interface of the query server support configuration)
├─callbackUrl | ***********/test | String | be |Receive task callback configuration URL



**response parameter**

|parameter name | example value           | Parameter type    | Parameter Description     |
|--- |---------------|---------|----------|
|code | 200           | Integer |          |
|msg | success       | String  ||
|ts | ************* | Long    ||
|data | 2             | String  |  |




**Example of a request**
```javascript
{"callbackIdList":[21,14],"callbackUrl":"localhost:8080/callback"}

```
**Response Example**

```javascript
{"code":200,"data":2,"msg":"success","ts":1734502541732}
```

#### **Delete the current user-configured callback address**

**interface address**

> /openapi/open/config/deleteCallback

**request method**

> POST

**Request data type**

> parameterless



**response parameter**

|parameter name | example value           | Parameter type    | Parameter Description     |
|--- |---------------|---------|----------|
|code | 200           | Integer |          |
|msg | success       | String  ||
|ts | ************* | Long    ||
|data | 4             | Long    | |




**Example of a request**

**Response Example**

```javascript
{"code":200,"data":22,"msg":"success","ts":1734503029282}
```




### Instance manipulation

#### **Asynchronous execution of ADB commands**
Execute commands asynchronously in one or more cloud phone instances

**Interface address**

> /openapi/open/pad/asyncCmd

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

|Parameter Name | Example Value | Parameter Type | Required or Not Required | Parameter Description|
| --- | --- | --- | --- | --- |
|padCodes | String[] | yes |
| ├─ | AC22020020793 | String | Yes | Instance Number|
|scriptContent | cd /root;ls | String | Yes | ADB commands, multiple commands separated by semicolons |

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22020020793 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```json
{
    "padCodes": [
        "AC22020020793"
    ],
    "scriptContent": "cd /root;ls"
}
```
**Example of response**

```json
{
	"code": 200.
	"msg": "success",
	"ts": 1717570297639.
	"data": [
		{
			"taskId": 14,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 15,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110003 | Failed to execute ADB command | Contact administrator
110012 | Command execution timeout | Please try again later.



#### **Synchronised execution of ADB commands**
Synchronise command execution across one or more cloud phone instances

> If no response is received for more than 5 seconds, a timeout exception is returned.

**Interface address**

> /openapi/open/pad/syncCmd

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCode | AC22020020793 | String | Yes | Instance ID
scriptContent | cd /root;ls | String | Yes | ADB commands, multiple commands separated by semicolon

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22020020793 | String | Instance Number
├─taskStatus | 3| Integer | Task Status (-1: total failure; -2: partial failure; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete)
├─taskResult | Success | String | Task Result

**Example request**

```javascript
{
    "padCode": "VP21020010231".
    "scriptContent": "cd /root/nbin;ls"
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data":[
				{
				"taskId": 1,
				"padCode": "AC22020020793".
				"taskStatus":3,
				"taskResult": "Success"
				}
			]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---|
110003 | Failed to execute ADB command | Please try again later.
110012 | Command execution timeout | Please try again later.

#### **local screenshot**
Instance screenshot.

**Interface URL**

> openapi/open/pad/screenshot

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| ---- | ---- | ---- | ---- | ---- |
|padCodes | String[] | yes | |
| ├─ | AC21020010231 | String | Yes | Instance number |
| rotation | 0 | Integer | Yes | Screenshot Screen Horizontal and Vertical Screen Rotation: 0: the direction of the screenshot is not processed, the default; 1: when the screenshot screen is rotated to vertical: a: the screenshot of the mobile phone's vertical screen is not processed. b: the screenshot of the mobile phone's horizontal screen, the screenshot is rotated 90 degrees clockwise. |
| broadcast | false | Boolean | No | Whether or not the event is broadcast (default false) |

**Response parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
code | 200 | Integer | Yes | Status Code
msg | success | String | Yes | Response Message
ts | ************* | Long | Yes | Timestamps
data | | Object[] | |
├─taskId | 1 | Integer | No | TaskId
├─padCode | AC21020010231 | String | No | Instance Number
├─vmStatus | 1 | Integer | Yes | instance online status (0: offline; 1: online)

**Example request**

```json
{
	"padCodes": [
		"AC21020010231"
	],
	"rotation": 0,
	"broadcast": false
}
```
**Example of response**

```json
{
	"code": 200.
	"msg": "success",
	"ts": 1717570337023.
	"data": [
		{
			"taskId": 16,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 17,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110001 | Screenshot Failed | Please try again.
110004 | Failed to execute reboot command | Reboot again later
110028 | Instance does not exist | Please check if the instance exists.



#### **Generation of preview image**
The specified instance gets the preview image.

**Interface address**

> /openapi/open/pad/generatePreview

**Mode of request**

> POST

**Request data type**

> application/json


**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| --------- | -------- | -------- | -------- | ----- |
|padCodes | String[] | yes | |
| ├─ | AC11010000031 | String | Yes | Instance Number
| rotation | 0 | Integer | Yes | Screenshot Screen Horizontal and Vertical Screen Rotation: 0: the direction of the screenshot is not processed, the default; 1: when the screenshot screen is rotated to vertical: a: the screenshot of the mobile phone's vertical screen is not processed. b: the screenshot of the mobile phone's horizontal screen, the screenshot is rotated 90 degrees clockwise. |
| broadcast | false | Boolean | No | Whether the event is broadcast (default false)

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─padCode | AC11010000031 | String | Instance Number
├─accessUrl | http://xxx.armcloud.png | String | access address

**Example request**
```javascript
{
    "padCodes": [
        "AC11010000031"
    ],
    "rotation": 0,
    "broadcast": false
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": [
		{
			"padCode": "AC11010000031".
			"accessUrl": "http://xxx.armcloud.png"
		}
	]
}
```

#### **File Upload**
Performing file upload operations

**Interface address**

> /file-center/open/file/cache

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value                              | Parameter Type | Is Required | Parameter Description                              |
|----------------|--------------------------------------------|----------------|-------------|---------------------------------------------------|
| fileUrl        | http://xxx.armcloud.apk                   | String         | Yes         | File download address                              |
| fileName       | test.apk             | String         | Yes         | File name                                         |
| fileMd5        | 32e1f345f209a7dc1cc704913ea436d3          | String         | Yes         | Expected MD5 of the file, used for download file verification (max length: 32) |

**Response Parameters**

| Parameter Name | Example Value                              | Parameter Type | Parameter Description                         |
|----------------|--------------------------------------------|----------------|----------------------------------------------|
| code           | 200                                        | Integer        | Status code                                  |
| msg            | success                                    | String         | Response message                             |
| ts             | *************                              | Long           | Timestamp                                    |
| data           |                                            | Object[]       |                                              |
| ├─taskId      | 1                                          | Integer        | Task ID                                      |
| ├─fileUniqueId | 6865b417b7257d782afd5ac8bee4d311         | String         | Unique identifier for the file               |


**Example request**

```javascript

{
    "fileUrl": "http://down.s.qq.com/download/11120898722/apk/10043132_com.tencent.fiftyone.yc.apk",
	"fileName": "Peachland Depths With Family Game Official Version.apk",
	"fileMd5": "c52585e13a67e13128d9963b2f20f69678a86ee8b5551ca593327d329719a5"
}

```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": {
		"taskId":1,
		"fileUniqueId": "6865b417b7257d782afd5ac8bee4d311"
	}
}
```


#### **List of documents**
Query the list of uploaded files

**Interface address**

> /file-center/open/file/list

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value                                               | Parameter Type | Is Required | Parameter Description                                  |
|----------------|------------------------------------------------------------|----------------|-------------|-------------------------------------------------------|
| page           | 1                                                          | Integer        | Yes         | Starting page, default is 1                           |
| rows           | 10                                                         | Integer        | Yes         | Number of records to query, default is 10            |

**Response Parameters**

| Parameter Name | Example Value                                               | Parameter Type | Parameter Description                         |
|----------------|------------------------------------------------------------|----------------|----------------------------------------------|
| code           | 200                                                        | Integer        | Status code                                  |
| msg            | success                                                    | String         | Response message                             |
| ts             | *************                                              | Long           | Timestamp                                    |
| data           |                                                            | Object         |                                              |
| ├─page        | 1                                                          | Integer        | Current page                                 |
| ├─rows        | 10                                                         | Integer        | Number of records per page                   |
| ├─size        | 2                                                          | Integer        | Number of records on the current page       |
| ├─total       | 2                                                          | Integer        | Total number of records                      |
| ├─totalPage   | 1                                                          | Integer        | Total number of pages                        |
| ├─pageData    |                                                            | Object[]       | List of records                             |
| ├─├─fileUniqueId | c417cdf30cd13437a60a494f2fcee616                      | String         | Unique identifier for the file              |
| ├─├─fileName  | 15b18072b01049dfa30da046aaf5b213.apk                     | String         | File name                                    |
| ├─├─fileMd5   | 49f526ec07f261ef6c22081fd61fb6b2836b84214ab6f4620e89d2f2d454253 | String | File content value                           |
| ├─├─fileSize  | 165936779                                                 | Integer        | File size (in bytes)                        |
| ├─├─originUrl  | https://xxx.armcloud.apk                                  | String         | Original file address                       |
| ├─├─createTime | 1713884498000                                            | Long           | Creation time                               |


**Example request**

```javascript

{
    "page": 1,
	"rows": 10
}

```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"page": 1,
		"rows": 10,
		"size": 2,
		"total": 2,
		"totalPage": 1,
		"pageData": [
			{
				"fileUniqueId": "c417cdf30cd13437a60a494f2fcee616",
				"fileName": "15b18072b01049dfa30da046aaf5b213.apk",
				"fileMd5": "49f526ec07f261ef6c22081fd61fb6b2836b84214ab6f4620e89d2f2d454253",
				"fileSize": 165936779.
				"originUrl": "https://xxx.armcloud.apk",
				"createTime": 1713884498000
			},
			{
				"uniqueId": "417996c783c70b946675b55a138b1bff",
				"name": "10043132_com.tencent.fiftyone.yc.apk",
				"sha256": "86f3dc7bf26219f56f3cdb51d787af3c165533ad1c70856a27c5d2a3a6b7647f",
				"size": 1091941761.
				"originUrl": "https://xxx.armcloud.apk",
				"createTime": 1713879614000
			}
		]
	}
}
```

#### **Instance file upload**
Push files from the File Management Centre to one or more cloud phone instances (asynchronous task)

**Interface address**

> /openapi/open/pad/v2/uploadFile

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22030023061 | String | Yes | Instance Number
autoInstall | 1 | Integer | No | Whether autoinstall is required 1 required, 0 not required. 0 is not required. Leave this field blank to indicate that it is not required. Only for apk-type files.
fileUniqueId | 1e5d3bf00576ee8f3d094908c0456722 | String | Yes | File id unique identifier.
customizeFilePath | /Documents/ | String | No | Customise the path. Not mandatory, must start with /.

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---|
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─padCode | AC22010020062 | String | Instance Number
├─taskId | 1 | Integer | TaskId
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padCodes": [
		"AC22030023061"
	],
	"autoInstall": 1,
	"fileUniqueId": "1e5d3bf00576ee8f3d094908c0456722",
	"customizeFilePath": "/Documents/"
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717571059834.
	"data": [
		{
			"taskId": 39.
			"padCode": "AC22030010001",
			"vmStatus": 1
		}
	]
}
```

**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
140006 | Incorrect file storage path | Custom paths must begin with /.
140005 | File not available | File path is empty
110006 | Download Failed | File Path Cannot Be Downloaded

#### **upgraded mirror**
Batch Instance Image Upgrade

**Interface address**

> /openapi/open/pad/upgradeImage

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22030010182 | String | Yes | Instance Number
imageId | mg-24061124017 | String | Yes | ImageId
wipeData | false | Boolean | Yes | whether to wipe the instance data (data partition), true clears, false does not clear

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─padCode | AC22030010182 | String | Instance Number
├─taskId | 1 | Integer | TaskId
├─errorMsg | "" | String | Error Message

**Example request**

```javascript
{
    "padCodes": [
        "AC22030010182"
    ],
    "wipeData": false,
    "imageId": "mg-24061124017"
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1718594881432.
	"data": [
		{
			"taskId": 63.
			"padCode": "AC22030010182".
			"errorMsg": null
		}
	]
}
```

**Error code**

Error Code | Error Description | Recommendations|
--- | --- | ---|
110041 | Mirror does not exist | Mirror id passed incorrectly|
110037 | Failed to execute upgrade image command | Instance state is wrong, contact administrator|
110038 | Failed to execute upgrade image command | Incorrect instance status, contact administrator|

#### **Batch access to instance model information**
Get the model information of the corresponding instance in bulk based on the instance number.

**Interface address**

> /openapi/open/pad/modelInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description|
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22030010182 | String | Yes | Instance Number|

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─padCode | AC22030010182 | String | Instance Number
├─imei | 524803173613682 | String | IMEI
├─serialno | 01NM5ON34M4O | String | Serial Number
├─wifimac | 04:3a:6c:e5:e9:8d:62:d6:4a | String | Wi-Fi's mac address
├─androidid | aa6bcedf1426546c | String | Android instance unique identifier
├─model | Mi 10 Pro | String | Models
├─Brand | Xiaomi | String | Brands
├─manufacturer | Xiaomi | String | Manufacturer
├─isRoot | 1 | String | Whether or not it is ROOT privilege
├─width | 720 | Integer | The width of the cloud phone Maximum 1080
├─height | 1280 | Integer | Height of cloud phone Maximum 1920
├─memoryLimit | 1024 | Integer | Memory Limit

**Example request**

```javascript
{
    "padCodes": [
        "AC22030010182"
    ]
}
```
**Example of response**

```javascript
{
  "code": 200.
  "msg": "success",
  "ts": *************.
  "data": [
    {
      "padCode": "AC22030010182".
      "imei": "524803173613682",
      "serialno": "01NM5ON34M4O",
      "wifimac": "04:3a:6c:e5:e9:8d:62:d6:4a",
      "androidid": "aa6bcedf1426546c",
      "model": "Mi 10 Pro".
      "brand": "Xiaomi",
      "manufacturer": "Xiaomi",
      "isRoot": "1",
      "width": 720,
      "height": 1280.
      "memoryLimit": 1024
    }
  ]
}
```


#### **Add application blacklist list**
Add application blacklists based on instance specifications.

**Interface address**

> /openapi/open/appBlack/setUpBlackList

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padGrade | q2-1 | String | Yes | Instance Specification
blackApps | | Object[] | Yes | BlackListList
├─appPkg | cn.v8box.app | String | Yes | App Package Name
├─appName | x8 Sandbox | String | Yes | Application Name

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | 1721647657112 | Long | Timestamps
data | "" | String |

**Example request**

```javascript
{
	"padGrade": "q2-1".
	"blackApps": [
		{
			"appPkg": "cn.v8box.app",
			"appName": "x8 Sandbox"
		}
	]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1721647657112.
	"data": null
}
```


#### **Set instance blacklist**
Set up instance blacklists based on instance specifications.

**Interface address**

> /openapi/open/pad/triggeringBlacklist

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padGrade | q2-1 | String | Yes | Instance Specification
padCodes | | String[] | No |
├─ | AC22030010124 | String | No | Instance No.

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | 1721739857317 | Long | Timestamps
data | | Object[] |
├─taskId | 12818 | Integer | TaskId
├─padCode | AC22030010124 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padGrade": "q2-4".
	"padCodes": [
		"AC22030010124"
	]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1721739857317.
	"data": [
		{
			"taskId": 12818,
			"padCode": "AC22030010124".
			"vmStatus": 1
		}
	]
}
```

**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110051 | No application blacklist configuration exists for this specification | need to add specification application blacklist list
110028 | Instance doesn't exist | Passed parameter incorrectly
110052 | Failed to execute Set Application Blacklist command | Please try again.

#### **Set instance bandwidth**
Set the instance bandwidth according to the instance number.

**Interface address**

> /openapi/open/pad/setSpeed

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22030010124 | String | Yes | Instance Number
upBandwidth | 10.00 | float | yes | upBandwidth Mbps (0: no restriction; -1: restriction of internet access)
downBandwidth | 10.00 | float | yes | downBandwidth Mbps (0: no restriction; -1: restriction of Internet access)

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | 1721739857317 | Long | Timestamps
data | | Object[] |
├─taskId | 679 | Integer | TaskId
├─padCode | AC32010140011 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padCodes": [
		"AC32010140011"
	],
	"upBandwidth": 10.00,
	"downBandwidth": 10.00
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1721640654237.
	"data": [
		{
			"taskId": 679.
			"padCode": "AC32010140011",
			"vmStatus": 1
		}
	]
}
```

#### **Enable/Disable ADB**
Enable or disable ADB for an instance based on its instance ID.

**API Endpoint**

> /openapi/open/pad/adb

**Request Method**

> POST

**Request Content Type**

> application/json

**Request Body Parameters**

| Parameter | Example | Type    | Required | Description              |
| --------- | ------- | ------- | -------- | ------------------------ |
| padCodes  | AC22030010124 | String  | Yes      | Instance ID              |
| enable    | true    | boolean | Yes      | true - enable, false - disable |

**Response Parameters**

| Parameter | Example | Type      | Description               |
| --------- | ------- | --------- | ------------------------- |
| code      | 200     | Integer   | Status code               |
| msg       | success | String    | Response message          |
| ts        | 1721739857317 | Long   | Timestamp                |
| data      |         | Object[]  |                           |
| ├─padCode | AC32010140011 | String | Instance ID              |
| ├─command | adb connect ip:port | String | ADB connection info    |
| ├─expireTime | 2024-10-24 10:42:00 | String | Connection expiration  |
| ├─enable  | true    | boolean   | ADB status                |


**Request Example**

```javascript
{
  "padCode": "AC22030010001",
  "enable": true
}
```

**Response Example**

```javascript
{
  "code": 200,
  "msg": "success",
  "ts": 1729651701083,
  "data": {
    "padCode": "AC32010161274",
    "command": "adb connect ip:port",
    "expireTime": "2024-10-24 10:42:00",
    "enable": true
  }
}
```


### Resource management-related interfaces
#### **Information on the list of instances**
Paging to get instance list information based on query criteria.

**Interface address**

> /openapi/open/pad/infos

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value                       | Parameter Type | Is Required | Parameter Description                   |
|----------------|-------------------------------------|----------------|-------------|----------------------------------------|
| page           | 1                                   | Integer        | Yes         | Page number                            |
| rows           | 10                                  | Integer        | Yes         | Number of records                     |
| padCodes       |                                     | String[]       | No          |                                        |
| ├─             | AC22010020062                       | String         | Yes         | Instance number                        |
| groupIds       |                                     | Integer[]      | No          |                                        |
| ├─             | 1                                   | Integer        | No          | Instance group ID                      |

**Response Parameters**

| Parameter Name | Example Value                       | Parameter Type | Parameter Description                     |
|----------------|-------------------------------------|----------------|------------------------------------------|
| code           | 200                                 | Integer        |                                          |
| msg            | success                             | String         |                                          |
| ts             | *************                       | Long           |                                          |
| data           |                                     | Object         |                                          |
| ├─page        | 1                                   | Integer        | Current page                             |
| ├─rows        | 10                                  | Integer        | Number of records per page               |
| ├─size        | 1                                   | Integer        | Number of records on the current page    |
| ├─total       | 1                                   | Integer        | Total number of records                  |
| ├─totalPage   | 1                                   | Integer        | Total number of pages                    |
| ├─pageData    |                                     | Object[]       | List of records                          |
| ├─├─padCode   | VP21020010391                       | String         | Instance number                          |
| ├─├─padGrade  | q1-2                                | String         | Instance opening (q1-6 for six openings, q1-2 for two openings) |
| ├─├─padStatus | 10                                  | String         | Instance status (10-running, 11-restarting, 12-resetting, 13-upgrading, 14-abnormal, 15-not ready) |
| ├─├─groupId   | 0                                   | Integer        | Group ID                                  |
| ├─├─idcCode   | d3c1f580c41525e514330a85dfdecda8  | String         | Data center code                         |
| ├─├─deviceIp   | ***********                        | String         | Cloud machine IP                         |
| ├─├─padIp     | ***********                        | String         | Instance IP                              |
| ├─├─apps      |                                     | String[]       | List of installed applications            |
| ├─├─├─       | armcloud001                        | String         | Installed application                     |



**Example request**

```javascript
{
	"page": 1,
	"rows": 10,
	"padCodes": [
		"AC21020010391"
	],
	"groupIds":[1]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"page": 1,
		"rows": 1,
		"size": 1,
		"total": 1.
		"totalPage": 1,
		"pageData": [
			{
				{
				"padCode": "AC21020010391".
				"padGrade": "q2-4".
				"padStatus": 10,
				"groupId": 0,
				"idcCode": "8e61ad284bc105b877611e6fef7bdd17",
				"deviceIp": "***********",
				"padIp": "***********",
				"apps": [
					"armcloud001"
				]
			}
		]
	}
}
```
#### **List of Example Groupings**
Get a list of all instances grouped by the user (including: group ID, group name, number of instances under the group).
**Interface address**

> /openapi/open/group/infos

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCode | AC21020010391 | String | No | Instance No.
groupIds | | Integer[] | No |
├── | 1 | Integer | No | Grouping ID

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description|
--- | --- | --- | ---|
code | 200 | Integer | Status Code|
msg | success | String | Response Message|
ts | ************* | Long | Timestamps|
data | | Object[] | |
├─groupId | 1 | Integer | GroupId|
├─groupName | Group1 | String | Group Name|
├─padCount | 1 | Integer | Number of instances under the grouping|

**Example request**

```javascript
{
	"padCode": "AC21020010391".
	"groupIds": [1]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": [
			{
				"groupId": 1,
				"groupName": "Grouping One",
				"padCount": 1
			}
	]
}
```

### Application management

#### **Application upload**
Upload application installation files to the Application Management Centre of the specified business (asynchronous task).

**Interface address**

> /file-center/open/app/cache

**Mode of request**

> POST

**Request data type**

> application/json


**Request Body parameters**

| Parameter Name | Example Value                         | Parameter Type | Is Required | Parameter Description                               |
|----------------|---------------------------------------|----------------|-------------|----------------------------------------------------|
| parse          | true                                  | Boolean        | Yes         | Whether to cache and parse (parse and cache). If parsed, package information is not required. |
| apps           |                                       | Object[]       | Yes         | List of applications                               |
| ├─ appId       | 1243                                  | Integer        | No          | Custom application ID                              |
| ├─ url         | https://xxx.armcloud.apk             | String         | Yes         | Source file download address                       |
| ├─ appName     | kuaishou                             | String         | No          | Application name                                   |
| ├─ pkgName     | com.smile.gifmaker                   | String         | No          | Package name                                       |
| ├─ signMd5     | 0F938C4F0995A83C9BF31F0C64322589    | String         | No          | Application signature MD5                          |
| ├─ versionNo   | 36000                                 | Integer        | No          | Version number                                     |
| ├─ versionName | 12.3.20.36000                        | String         | No          | Version name                                       |
| ├─ description  | kuai                                 | String         | No          | Description                                        |
| ├─ md5sum      | e673a204b8f18a0f6482da9998           | String         | No          | Unique identifier for the application              |

**Response Parameters**

| Parameter Name | Example Value                         | Parameter Type | Parameter Description                          |
|----------------|---------------------------------------|----------------|-----------------------------------------------|
| code           | 200                                   | Integer        | Status code                                   |
| msg            | success                               | String         | Response message                              |
| ts             | *************                         | Long           | Timestamp                                     |
| data           |                                       | Object[]       |                                               |
| ├─ taskId      | 12                                    | Integer        | Task ID                                       |
| ├─ appId       | 1243                                  | Integer        | Application ID                                |


**Example request**

```javascript
{
	"parse": true,
	"apps": [
		{ 
			"appId": 1243.
			"appName": "kuaishou",
			"url": "https://xxx.armcloud.apk",
			"pkgName": "com.smile.gifmaker",
			"signMd5": "0F938C4F0995A83C9BF31F0C64322589",
			"versionNo": 36000.
			"versionName": "12.3.20.36000",
			"description": "kuai".
			"md5sum": "e673a204b8f18a0f6482da9998"
		}
	]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": [
			{
				"taskId": 12,
				"appId": 1243
			}
	]
}
```

**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
100001 | No access | No server room ordered

#### **Application List**
The list of application information can be queried according to the application ID.

**Interface address**

> /file-center/open/app/list

**Mode of request**

> POST

**Request data type**
> application/json


**Request for Query parameters**

| Parameter Name | Example Value                        | Parameter Type | Is Required | Parameter Description                           |
|----------------|--------------------------------------|----------------|-------------|------------------------------------------------|
| page           | 1                                    | Integer        | Yes         | Starting page, default is 1                    |
| rows           | 10                                   | Integer        | Yes         | Number of records to query, default is 10      |
| appIds         |                                      | Integer[]      | No          | Application IDs                                 |

**Response Parameters**

| Parameter Name | Example Value                        | Parameter Type | Parameter Description                          |
|----------------|--------------------------------------|----------------|-----------------------------------------------|
| code           | 200                                  | Integer        | Status code                                   |
| msg            | success                              | String         | Response message                              |
| ts             | *************                        | Long           | Timestamp                                     |
| pageData       |                                      | Object         |                                               |
| ├─ page        | 1                                    | Integer        | Current page                                  |
| ├─ rows        | 10                                   | Integer        | Number of items displayed per page            |
| ├─ size        | 10                                   | Integer        | Number of items in the current page           |
| ├─ total       | 2                                    | Integer        | Total number of items                         |
| ├─ totalPage   | 1                                    | Integer        | Total number of pages                         |
| ├─ pageData    |                                      | Object[]       |                                               |
| ├─ ├─ originUrl| downloadUrl_tr0bi                   | String         | Original download URL                         |
| ├─ ├─ appId    | dwadawdf                            | Integer        | Custom application ID                          |
| ├─ ├─ description | description_1cq3m                 | String         | Description                                    |
| ├─ ├─ packageName | packageName_e6lw8                 | String         | Package name                                   |
| ├─ ├─ appName  | appName_o4mhn                       | String         | Application name                               |
| ├─ ├─ versionName | versionName_s4o2i                 | String         | Version name                                   |
| ├─ ├─ versionNo | 1                                  | Integer        | Version number                                 |
| ├─ ├─ signMd5  | 0F938C4F0995A83C9BF31F0C64322589   | String         | MD5 checksum                                   |
| ├─ ├─ available | true                                | Boolean        | Indicates if the current file is available     |
| ├─ ├─ createTime | 1709895492000                     | Integer        | Creation time                                  |


**Example request**

```javascript

{
    "rows": 10,
    "page": 1,
    "appIds": [
        1, 2
    ]
}

```


**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"page": 1,
		"rows": 10,
		"size": 10,
		"total": 30.
		"totalPage": 3,
		"pageData": [
			{
				"originUrl": "downloadUrl_tr0bi",
				"appId": 1,
				"description": "description_1cq3m".
				"packageName": "packageName_e6lw8",
				"appName": "appName_o4mhn".
				"versionName": "versionName_s4o2i".
				"versionNo": 1.
				"signMd5": "0F938C4F0995A83C9BF31F0C64322589",
				"available": true,
				"createTime": 1709895492000
			},
			{
				"originUrl": "downloadUrl_xh9ig",
				"appId": 2,
				"description": "description_9j663".
				"packageName": "packageName_o7t9j",
				"appName": "appName_szxpe",
				"versionName": "versionName_hwio7",
				"versionNo": 1.
				"signMd5": "0F938C4F0995A83C9BF31F0C64322587",
				"available": true,
				"createTime": 1710479133000
			}
		]
	}
}
```


#### **Application details**
Querying Application Installations on a Specified Instance

**Interface address**

> /file-center/open/app/detail

**Mode of request**

> POST

**Request data type**
> application/json

**Request for Query parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
appId | 1 | Integer | Yes | appid

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object |
├─appId | 1 | Integer | application id
├─originUrl | http://www.xx.com/test.apk | String | Original file download address
├─description | test | String | Custom Description
├─packageName | xxx.xxx.com | String | Package Name
├─appName | test | String | Application Name
├─versionName | 1.0.1 | String | Version Name
├─versionNo | 124511 | Integer | versionNo
├─signMd5 | 0F938C4F0995A83C9BF31F0C64322589 | String | signature md5
├─createTime | 1711595044000 | Integer | File Creation Time

**Example request**

```javascript
{
	"appId":1
}
```


**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"appId": 1,
		"originUrl": "http://www.xx.com/test.apk",
		"customizeFileId": "customiseId_v5o26",
		"description": "test",
		"packageName": "xxx.xxx.com",
		"appName": "test",
		"versionName": "1.0.1",
		"versionNo": 124511.
		"signMd5": "0F938C4F0995A83C9BF31F0C64322589",
		"createTime": 1711595044000
	}
}
```


#### **Query Instance application list**
Querying the list of uploaded files

**Interface address**

> /openapi/open/pad/listApp

**Mode of request**

> POST

**Request data type**

> application/json


**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | No |
├─ | AC22010020062 | String | Yes | Instance Number

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22010020062 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
    "padCodes": [
        "AC22010020062"
    ]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717570838398.
	"data": [
		{
			"taskId": 31,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 32,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
100010 | Processing Failed | Please reinstall.



#### **Application Installation**
Installs a single or multiple apps simultaneously for a single or multiple instances. this interface operates asynchronously.

**Interface address**

> /openapi/open/pad/installApp

**Mode of request**

> POST

**Request data type**

> application/json


**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
apps | Object[] | Yes | Apps List
├─appId | 124 | Integer | Yes | appId
├─appName | Hulu Man | String | Yes | App Name
├─pkgName | com.huluxia.gametools | String | Yes | Application Package Name
├─PadCodes | | String[] | Yes |
├─── | AC22010020062 | String | Yes | Instance No.


**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22010020062 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"apps":[
		{
			"appId":124,
			"appName": "hulu".
			"pkgName": "com.huluxia.gametools",
			"padCodes":["AC22010020062"]
		}
	]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717570991004.
	"data": [
		{
			"taskId": 37.
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 38.
			"padCode": "AC22030010002".
			"vmStatus": 1
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
140005 | File unavailable | Check if file exists

#### **Application uninstallation**
For single or multiple instances to uninstall single or multiple apps at the same time. this interface operates asynchronously.

**Interface address**

> /openapi/open/pad/uninstallApp

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Value                       | Parameter Type | Is Required | Parameter Description                       |
|----------------|-------------------------------------|----------------|-------------|---------------------------------------------|
| apps           |                                     | Object[]       | Yes         | Application list                           |
| ├─ appId       | 124                                 | Integer        | Yes         | Application ID                             |
| ├─ appName     | 葫芦侠 (HuluXia)                   | String         | Yes         | Application name                           |
| ├─ pkgName     | com.huluxia.gametools               | String         | Yes         | Application package name                   |
| ├─ padCodes    |                                     | String[]       | Yes         |                                             |
| ├─ ├─         | AC22010020062                       | String         | Yes         | Instance number                            |

**Response Parameters**

| Parameter Name | Example Value                       | Parameter Type | Parameter Description                       |
|----------------|-------------------------------------|----------------|---------------------------------------------|
| code           | 200                                 | Integer        | Status code                                 |
| msg            | success                             | String         | Response message                            |
| ts             | *************                       | Long           | Timestamp                                   |
| data           |                                     | Object[]       |                                             |
| ├─ taskId      | 2                                   | Integer        | Task ID                                    |
| ├─ padCode     | AC22010020062                       | String         | Instance number                             |
| ├─ vmStatus    | 1                                   | Integer        | Instance online status (0: offline; 1: online) |


**Example request**

```javascript
{
	"apps":[
		{
			"appId":124,
			"appName": "demo",
			"pkgName": "com.demo",
			"padCodes":["AC22010020062"]
		}
	]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 22,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 23,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110007 | Failed to uninstall application | Try again later

#### **Application Launch**
Performs application startup operations on the instance based on the instance number and application package name.

**Interface address**

> /openapi/open/pad/startApp

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name | Example Values | Parameter Type | Required or Not | Parameter Description |
| ----------- | ------------ | -------- | -------- | -------- | 
|padCodes | String[] | yes | |
| ├─ | AC22010020062 | String | Yes | Instance Number|
| pkgName | xxx.test.com | String | Yes | package name |

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22020020793 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padCodes": [
		"AC22030022693"
	],
	"pkgName": xxx.test.com
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": [
		{
			"taskId": 24,
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 25,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110008 | Failed to launch application | Reboot the cloud and then launch the application again

#### **Application Stop**
Performs an application stop operation on an instance based on the instance number and application package name.

**Interface address**

> /openapi/open/pad/stopApp

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22010020062 | String | Yes | Instance Number
pkgName | xxx.test.com | String | Yes | Package Name

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22010020062 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padCodes": [
		"AC22010020062"
	],
	"pkgName": xxx.test.com
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717570700415.
	"data": [
		{
			"taskId": 26.
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 27,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110010 | Failed to stop the application | Restarting the cloud shutdown application


#### **Application restart**
Performs an application restart operation on an instance based on the instance number and application package name.

**Interface address**

> /openapi/open/pad/restartApp

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
padCodes | | String[] | yes |
├─ | AC22010020062 | String | Yes | Instance Number
pkgName | xxx.test.com | String | Yes | Package Name

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22010020062 | String | Instance Number
├─vmStatus | 1 | Integer | instance online status (0: offline; 1: online)

**Example request**

```javascript
{
	"padCodes": [
		"AC22030022693"
	],
	"pkgName": xxx.test.com
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1717570855874.
	"data": [
		{
			"taskId": 33.
			"padCode": "AC22030010001",
			"vmStatus": 1
		},
		{
			"taskId": 34,
			"padCode": "AC22030010002".
			"vmStatus": 0
		}
	]
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
110009 | Failed to restart the application | Restart the cloud machine and then start the application again

### Mirror management

#### **Create mirror upload task**
Create a mirror upload task. (Asynchronous task)

**Interface address**

> /openapi/open/image/uploadImage

**Mode of request**

> POST

**Request data type**

> application/json

**Request for Query parameters**

| Parameter Name | Example Value                                               | Parameter Type | Is Required | Parameter Description|
|----------------|------------------------------------------------------------|----------------|-------------|-----------------------|
| imageFiles     |                                                            | Object[]       | Yes         |                                                    |
| ├─ imageFileUrl | https://xxxx.xx.xx/image/armcloud_v13.2.1-alpha05.tar   | String         | Yes         | File download address                              |
| ├─ imageTag    | v13.2.1-alpha05                                            | String         | Yes         | Image Tag                                         |
| serverType     | Cruise10                                                  | String         | Yes         | SOC type (currently supports: Cruise10)           |
| romVersion     | android10                                                 | String         | Yes         | AOSP version (currently supports: android10, android13) |

**Response Parameters**

| Parameter Name | Example Value                     | Parameter Type | Parameter Description                    |
|----------------|------------------------------------|----------------|------------------------------------------|
| code           | 200                                | Integer        | Status code                              |
| msg            | success                            | String         | Response message                         |
| ts             | 1717643679112                     | Long           | Timestamp                                |
| data           |                                    | Object         |                                          |
| ├─ imageTaskIds |                                  | Object[]       | Task list                                |
| ├─ ├─         | 14                                 | Integer        | Task ID                                  |
| ├─ errorList   |                                    | Object[]       | Failed upload image task information     |
| ├─ ├─ imageTag | v13.2.1-alpha05                   | String         | Image Tag                                |
| ├─ ├─ errorMessage | Do not submit images repeatedly | String         | Error reason                             |



**Example request**

```javascript
{
    "imageFiles": [
        {
            "imageFileUrl": "https://app.armcloud.net/image/armcloud_10.2.1-alpha45.tar",
            "imageTag": "v13.2.1-alpha05"
        }
    ],
    "serverType": "Cruise10".
    "romVersion": "android10.0"
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": {
		"imageTaskIds": [
			14
		],
		"errorList": [
			{
				"imageTag": "v13.2.1-alpha05",
				"errorMessage": "Do not submit images repeatedly"
			}
		]
	}
}
```
**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
500 | Incorrect request parameter | Please output the passed parameter normally.
110040 | Account does not exist | Is account incorrect
110039 | Task add failed | Please try again

### Task management

#### **Documentation Mission Details**
Queries detailed information about the execution results of the specified file task.

**Interface address**

> /task-center/open/task/fileTaskDetail

**Mode of request**

> POST

**Request data type**

> application/json

**Request for Query parameters**

| Parameter Name | Example Value                                   | Parameter Type | Is Required | Parameter Description                          |
|----------------|-------------------------------------------------|----------------|-------------|------------------------------------------------|
| taskIds        |                                                 | Integer[]      | Yes         |                                                |
| ├─ taskId      | 1                                               | Integer        | Yes         | Task ID                                       |

**Response Parameters**

| Parameter Name | Example Value                                    | Parameter Type | Parameter Description                          |
|----------------|--------------------------------------------------|----------------|------------------------------------------------|
| code           | 200                                              | Integer        | Status code                                   |
| msg            | success                                          | String         | Response message                              |
| ts             | *************                                   | Long           | Timestamp                                     |
| data           |                                                  | Object[]       | Task list details                             |
| ├─ taskId      | 1                                                | Integer        | Subtask ID                                    |
| ├─ appId       | 134                                              | Long           | Application ID                                 |
| ├─ fileUniqueId| e2c07491309858c5cade4bfc44c03724               | String         | Unique file identifier                        |
| ├─ fileName    | xx.apk                                          | String         | File name                                     |
| ├─ taskStatus  | 2                                                | Integer        | Task status (-1: failed; -2: partially failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed) |
| ├─ endTime     | 1713429401000                                   | Long           | Subtask end timestamp                         |



**Example request**

```javascript
{
	"taskIds":[
		1,2
	]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1716283460673.
	"data": [
		{
			"taskId": 1,
			"appId": 134.
			"fileUniqueId": "e2c07491309858c5cade4bfc44c03724",
			"fileName": "xx.apk",
			"taskStatus": 2,
			"endTime": 1713429401000
		},
		{
			"taskId": 2,
			"appId": 135,
			"fileUniqueId": "e2c07491309858c5cade4bfc43c03725",
			"fileName": "xx.apk",
			"taskStatus": 2,
			"endTime": 1713429401001
		}
	]
}
```
i

#### **Details of instance operation tasks**
Queries detailed information about the execution results of the specified instance operation task.

**Interface address**

> /task-center/open/task/padTaskDetail

**Mode of request**

> POST

**Request data type**

> application/json

**Request for Query parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
taskIds | | Integer[] | yes |
├─taskId | 1 | Integer | Yes | TaskId


**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object [] | Subtask List Details
├─ taskId | 1 | Integer | SubtaskId
├─ padCode | VP22020020793 | String | Instance Identifier
├─ taskStatus | 2 | String TODO Type Usage Error | TaskStatus (-1: total failure; -2: partial failure; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: completed)
├─ endTime | 1713429401000 | Long | Subtask End Timestamps
├─ taskContent | "" | String | taskContent
├─ taskResult | "" | String | Task Result
├─ errorMsg | "" | String | Error Message

**Example request**

```javascript
{
	"taskIds":[1,2]
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": 1716283460673.
	"data": [
		{
			"taskId": 1,
			"padCode": "AC22030022441".
			"taskStatus": 2,
			"endTime": 1713429401000.
			"taskContent": null,
			"taskResult": null
		},
		{
			"taskId": 2,
			"padCode": "AC22030022442".
			"taskStatus": 2,
			"endTime": 1713429401001.
			"taskContent": null,
			"taskResult": null
		}
	]
}
```

#### **List of mandates**
Queries list information about instances, applications, and other related tasks.

**Interface address**

> /task-center/open/task/list

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Is Required | Parameter Description  |
|--------------------|-------------------------------------------------|----------------|-------------|------------------------|
| type               | 1002                                            | Integer        | Yes         | Task type (1000: instance restart; 1001: instance reset; 1002: pad execute command; 1003: download application; 1004: application uninstall; 1005: application stop; 1006: application restart; 1007: application start; 1008: local screenshot; 1009: download file; 1010: modify instance properties; 1011: query installed applications; 1012: upgrade image; 1013: application installation; 1014: application cleanup; 2000: upload file; 2001: delete file) |
| page               | 1                                               | Integer        | Yes         | Starting page, default is 1                   |
| rows               | 10                                              | Integer        | Yes         | Query quantity, default is 10                  |

**Response Parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|------------------------------------------------|
| code               | 200                                            | Integer        | Status code                                   |
| msg                | success                                        | String         | Response message                              |
| ts                 | *************                                   | Long           | Timestamp                                     |
| data               |                                                  | Object         |                                                |
| ├─ page            | 1                                               | Integer        | Current page                                  |
| ├─ rows            | 10                                              | Integer        | Number of items per page                      |
| ├─ size            | 10                                              | Integer        | Number of items in current page               |
| ├─ total           | 30                                              | Integer        | Total quantity                                |
| ├─ totalPage       | 3                                               | Integer        | Total pages                                   |
| ├─ pageData        |                                                  | Object[]       |                                                |
| ├─ ├─ taskBatchId  | 9d4b45447172732670c10808d630e454               | String         | Batch number                                  |
| ├─ ├─ taskBatchStatus | 2                                           | Integer        | Task status (-1: all failed; -2: partially failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed) |
| ├─ ├─ type        | 1002                                            | Integer        | Task type (same description as above)         |
| ├─ ├─ createTimestamp | 1713429395000                             | Long           | Task creation timestamp                       |
| ├─ ├─ subTasks     |                                                  | Object[]       |                                                |
| ├─ ├─ ├─ taskId   | 1                                               | Integer        | Subtask ID                                    |
| ├─ ├─ ├─ padCode   | AC22030022441                                  | String         | Instance number                               |
| ├─ ├─ ├─ fileUniqueId | e2c07491309858c5cade4bfc44c03724          | String         | Unique file identifier                        |
| ├─ ├─ ├─ taskStatus | 2                                             | Integer        | Task status (same description as above)      |
| ├─ ├─ ├─ endTime    | 1713429401000                                 | Long           | Subtask end timestamp                         |


**Example request**

```javascript
{
    "page": 1,
	"rows": 10,
	"type":1000
}
```

**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data": {
		"page": 1,
		"rows": 10,
		"size": 10,
		"total": 310.
		"totalPage": 31.
		"pageData": [
			{
				"taskBatchId": "9d4b45447172732670c10808d630e454",
				"taskBatchStatus": 2,
				"type": 1002,
				"createTimestamp": 1713429395000,
				"subTasks": [
					{
						"taskId": 1,
						"padCode": "AC22030022441".
						"fileUniqueId": "e2c07491309858c5cade4bfc44c03724",
						"taskStatus": 2,
						"endTime": 1713429401000
					}
				]
			}
		]
	}
}
```

#### **Get the result of the instance execution script**
Get the result of the instance's script execution by executing the script task ID.

**Interface address**

> /task-center/open/task/executeScriptInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- |
taskIds | | Integer [] | yes |
├── 1 | Integer | No | Task ID

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─taskId | 1 | Integer | TaskId
├─padCode | AC22020020793 | String | Instance Number
├─taskStatus | 3| Integer | Task Status (-1: total failure; -2: partial failure; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete)
├─endTime | 1756021166163 | Long | Task Execution End Time
├─taskContent | Success | String | Task Content
├─taskResult | Success | String | Task Result

**Example request**

```javascript
{
	"taskIds": [1]
}
```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data":[
				{
				"taskId": 1,
				"padCode": "AC22020020793".
				"taskStatus":3,
				"taskResult": "Success",
				"endTime": "1756021166163",
				"taskContent": "Success"
				}
			]
}
```


#### **Get file upload task results**
Get the file upload result of the instance by the file upload task ID.

**Interface address**

> /task-center/open/task/cacheFileResult

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Is Required | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|-------------|------------------------------------------------|
| taskIds            |                                                  | Integer[]      | Yes         |                                                |
| ├─                 | 1                                               | Integer        | Yes         | Task ID                                       |

**Response Parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|------------------------------------------------|
| code               | 200                                            | Integer        | Status code                                   |
| msg                | success                                        | String         | Response message                              |
| ts                 | *************                                   | Long           | Timestamp                                     |
| data               |                                                  | Object[]       |                                                |
| ├─ fileList       |                                                  | Object[]       |                                                |
| ├─ ├─ taskId      | 1                                               | Integer        | Task ID                                       |
| ├─ ├─ taskStatus   | 3                                             | Integer        | Task status (-1: all failed; -2: partially failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed) |
| ├─ ├─ fileName     | xxx.apk                                       | String         | File name                                     |
| ├─ ├─ fileUniqueId  | 6865b417b7257d782afd5ac8bee4d311           | String         | Unique file identifier                        |
| ├─ ├─ appId        | ""                                             | Integer        | Application ID                                |
| ├─ ├─ endTime      | 1756021166163                                 | Long           | Task execution end timestamp                  |

**Example request**

```javascript

{
	"taskIds": [1]
}

```
**Example of response**

```javascript
{
	"code": 200.
	"msg": "success",
	"ts": *************.
	"data": {
		"fileList":[
			"taskId":1,
			"taskStatus":3,
			"fileName": "xxx.apk",
			"fileUniqueId": "6865b417b7257d782afd5ac8bee4d311",
			"appId": null,
			"endTime": 1756021166163
		]
	}
}
```

#### **Get instance screenshot results**
Get the screenshot result of the instance by screenshot task ID.

**Interface URL**

> /task-center/open/task/screenshotInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Is Required | Parameter Description |
|--------------------|-------------------------------------------------|----------------|-------------|-----------------------|
| taskIds            |                                                  | Integer[]      | Yes         |                                                |
| ├─                 | 1                                               | Integer        | No          | Task ID                                       |

**Response Parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|------------------------------------------------|
| code               | 200                                            | Integer        | Status code                                   |
| msg                | success                                        | String         | Response message                              |
| ts                 | *************                                   | Long           | Timestamp                                     |
| data               | -                                               | Object[]       | Task list details                             |
| ├─ taskId         | 1                                               | Integer        | Task ID                                       |
| ├─ taskStatus      | 3                                             | Integer        | Task status (-1: all failed; -2: partially failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed) |
| ├─ padCode         | AC22020020793                                  | String         | Instance number                               |
| ├─ taskContent      | Success                                       | String         | Task content                                  |
| ├─ taskResult       | Success                                       | String         | Task result                                   |
| ├─ endTime         | 1756121167163                                 | String         | Task execution end time                       |


**Example request**

```json
{
	"taskIds": [1]
}
```
**Example of response**

```json
{
	"code": 200.
	"msg": "success",
	"ts":*************.
	"data":[
				{
				"taskId": 1,
				"taskStatus": 3,
				"padCode": "AC22020020793".
				"taskContent": "Success",
				"taskResult": "Success",
				"endTime": 1756121167163
				}
			]
}
```


#### **Application start/stop implementation results**
Get the results of an instance's application start/stop by its application start/stop task ID

**Interface address**

> /task-center/open/task/appOperateInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

Parameter Name | Example Value | Parameter Type | Required or Not | Parameter Description
--- | --- | --- | --- | --- | ---
taskIds | | Integer [] | yes |
├── 1 | Integer | Yes | Task ID

**Response parameters**

Parameter Name | Example Value | Parameter Type | Parameter Description
--- | --- | --- | ---
code | 200 | Integer | Status Code
msg | success | String | Response Message
ts | ************* | Long | Timestamps
data | | Object[] |
├─ taskId | 1 | Integer | TaskId
├─ padCode | AC22020020793 | String | Example Number
├─ taskStatus | 3| Integer | TaskStatus (-1: total failure; -2: partial failure; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete)
├─ endTime | 1756021166163 | Long | Task execution end time
├─ taskContent | Success | String | TaskContent
├─ taskResult | Success | String | Task Result


**Example request**

```javascript
{
    "taskIds": [1]
}
```
**Example of response**

```javascript
{
    "code": 200.
    "msg": "success",
        "ts":*************.
    "data":[
        {
            "taskId": 1,
            "taskStatus": 3,
            "padCode": "AC22020020793".
            "taskContent": "Success",
            "taskResult": "Success",
            "endTime": 1756121167163
        }
    ]
}
```

#### **Instance restart reset execution results**
Get the instance restart reset execution result by task ID.

**Interface address**

> /task-center/open/task/padExecuteTaskInfo

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Is Required | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|-------------|------------------------------------------------|
| taskIds            |                                                  | Integer[]      | Yes         |                                                |
| ├─                 | 1                                               | Integer        | Yes         | Task ID                                       |

**Response Parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|------------------------------------------------|
| code               | 200                                            | Integer        | Status code                                   |
| msg                | success                                        | String         | Response message                              |
| ts                 | *************                                   | Long           | Timestamp                                     |
| data               | -                                               | Object[]       |                                                |
| ├─ taskId         | 1                                               | Integer        | Task ID                                       |
| ├─ padCode        | AC21020010001                                  | String         | Instance number                               |
| ├─ taskStatus      | 3                                             | Integer        | Task status (-1: all failed; -2: partially failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed) |
| ├─ endTime         | *************                                 | Long           | Task execution end time                       |
| ├─ taskContent      | ""                                          | String         | Task content                                  |
| ├─ taskResult       | Success                                       | String         | Task result                                   |



**Example request**

```javascript
{
    "taskIds": [1]
}
```
**Example of response**

```javascript
{
    "code": 200.
    "msg": "success",
        "ts": *************
    "data":[
        {
            "taskId": 1,
            "padCode": "AC22030022911".
            "taskStatus": 3,
            "endTime": 1756021166163.
            "taskContent": null,
            "taskResult": "Success"
        }
    ]
}
```

#### **Mirror upload task result query**
Get mirror upload task results by task ID.

**Interface address**

> /task-center/open/task/imageUploadResult

**Mode of request**

> POST

**Request data type**

> application/json

**Request Body parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Is Required | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|-------------|------------------------------------------------|
| taskIds            |                                                  | Integer[]      | Yes         |                                                |
| ├─                 | 36                                             | Integer        | Yes         | Task ID                                       |

**Response Parameters**

| Parameter Name     | Example Value                                   | Parameter Type | Parameter Description                          |
|--------------------|-------------------------------------------------|----------------|------------------------------------------------|
| code               | 200                                            | Integer        | Status code                                   |
| msg                | success                                        | String         | Response message                              |
| ts                 | 1718262521168                                   | Long           | Timestamp                                     |
| data               | -                                               | Object[]       |                                                |
| ├─ taskId         | 36                                             | Integer        | Task ID                                       |
| ├─ taskStatus      | 2                                             | Integer        | Task status (-1: creation failed; 1: in progress; 2: creation successful) |
| ├─ errorMsg        | ""                                            | String         | Error message                                  |



**Example request**

```javascript
{
    "taskIds": [1]
}
```
**Example of response**

```javascript
{
    "code": 200.
    "msg": "success",
        "ts": 1718262521168.
    "data": [
        {
            "taskId": 36,
            "taskStatus": 2,
            "errorMsg": ""
        }
    ]
}
```

### SDK Token Issuance
Issue a temporary STS Token for authentication of the user accessing the cloud phone service.

#### **Get SDK temporary token**
**Interface address**

> /openapi/open/token/stsToken

**Mode of request**

> GET

**Request data type**

> application/json

**Request example**

```java
package com.xiaosuan.api.utils;

import com.alibaba.fastjson.TypeReference;
import com.xiaosuan.armcloud.sdk.configure.ArmCloudConfig;
import com.xiaosuan.armcloud.sdk.constant.ArmCloudApiEnum;
import com.xiaosuan.armcloud.sdk.http.DefaultHttpExecutor;
import com.xiaosuan.armcloud.sdk.model.Result;
import com.xiaosuan.armcloud.sdk.service.ArmCloudApiService;
import com.xiaosuan.armcloud.sdk.service.impl.ArmCloudApiServiceImpl;

import java.util.HashMap;

public class test {
    /**
     * 获取sts_token
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {

        ArmCloudConfig armCloudConfig = new ArmCloudConfig();
        armCloudConfig.setOpenUrl("https://xxx");
        armCloudConfig.setService("armcloud-paas");
        armCloudConfig.setHost("xxx");
        armCloudConfig.setAk("xxxxxx");
        armCloudConfig.setSk("xxxxxx");
        ArmCloudApiService armcloudApiService = new ArmCloudApiServiceImpl(armCloudConfig, new DefaultHttpExecutor());
        Result<Object> result = armcloudApiService.execute(ArmCloudApiEnum.STS_TOKEN,new HashMap<>(), new TypeReference<Result<Object>>() {});
        System.out.println(result);

    }
}

```

**Response parameters**

| Parameter Name | Example Value                                  | Parameter Type | Parameter Description                    |
|----------------|------------------------------------------------|----------------|------------------------------------------|
| code           | 200                                            | Integer        | Status code                             |
| msg            | success                                        | String         | Response message                        |
| ts             | *************                                  | Long           | Timestamp                               |
| data           | -                                              | Object         |                                          |
| ├─ token       | xxxx-xxxx-xxxx-xxxx         | String         | SDK communication token                 |


**Example of response**

```javascript
{
    "code": 200.
    "msg": "success",
        "ts":*************.
    "data": {
        "token": "xxxx-xxxx-xxxx-xxxx"
    }
}
```

**Error code**

Error Code | Error Description | Recommendations
--- | --- | ---
100004 | Invalid key | Refer to the interface description to check the request parameters and passed values

### SDK Token Issuance(Based on padCode)
Issue a temporary STS Token to be used to authenticate the user accessing the cloud phone service (the token can only be used for the requested padCode).

#### **Get SDK temporary token (based on padCode)**
**interface address**

> /openapi/open/token/stsTokenByPadCode

**request method**

> POST

**Request data type**

> application/json

**Request Body Parameters**

|parameter name | example value | Parameter type   | Required or not | Parameter Description   |
|--- |-----|--------| -- |--------|
|padCode | AC32010230001 | String | yes | Instance code |

**Example of a request**

```javascript
{"padCode":"AC32010230001"}


```
**Example of a request**

|parameter name | example value | Parameter type | Parameter Description|
|--- | --- | --- | ---|
|code | 200 | Integer | status code|
|msg | success | String | response message|
|ts | ************* | Long | timestamp|
|data |  | Object |  |
|├─token | xxxx-xxxx-xxxx-xxxx | String | sdk通信token|


**Response Example**

```javascript
{"code":200,"msg":"success","ts":1735209109185,"data":{"token":"xxxx-xxxxx-xxxx-xxxxx"}}
```


## Callback-related

### Configuration instructions
The customer needs to configure the callback address in the customer's open platform, and if the address is successfully configured, receiving callback information will be enabled by default.

### **Asynchronous execution of ADB command callbacks**
**Scenarios of use**

Client calls to asynchronously execute ADB commands are notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId| Integer| 1 | taskid |
| padCode | String | AC22030022001| Instance Identifier |
| taskStatus| Integer| 3| taskStatus (-1: total failure; -2: partial failure; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete) |
| endTime| Long| 1756021166163| Task execution end time |
| taskResult| String | Success| taskResult |
| taskContent| String | | TaskContent |
| cmd | String | cd /root;ls | Executed Commands |
| cmdResult| String | /ws | Executed Command Returns |


### **Instance file upload callbacks**
**Scenarios of use**

Clients calling the instance file upload api are notified via this callback interface.

| Field     | Type    | Example                                 | Description                          |
|-----------|---------|-----------------------------------------|--------------------------------------|
| taskId    | Integer | 1                                       | Task ID                              |
| result     | Boolean | true                                    | Execution result: true for success, false for failure |
| errorCode  | String  |                                         | Error code                           |
| padCode    | String  | AC22030022001                           | Instance number                      |
| fileId     | String  | cf08f7b685ab3a7b6a793b30de1b33ae34    | File ID                              |


### **Application installation callbacks**
**Scenarios of use**

The client invokes application installation and the installation of the application is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| apps | Object[] | | Application Information |
| ├─ appId | Integer | 10001 | Application ID|
| ├─ appName | String | demo | appName |
| ├─ pkgName | String | com.xxx.demo | package name |
| ├─ padCode | String | AC22030022001 | Instance number |
| ├─ result | boolean | true | Identifier of the installation result. true: success, false: failure |

### **Application uninstallation callbacks**
**Scenarios of use**

The client invokes application uninstallation and the application uninstallation is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| apps | Object | | Application Information |
| ├─ appId | Integer | 10001 | Application ID|
| ├─ appName | String | demo | appName |
| ├─ pkgName | String | com.xxx.demo | package name |
| ├─ padCode | String | AC22030022001 | Instance number |
| result | boolean | true | Identifier of the result of the installation. true: success, false: failure |

### **Application startup callbacks**
**Scenarios of use**

The client invokes application startup and the application startup is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 3 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete) |
| padCode | String | AC22030022001 | Instance Identification |
| pkgName | String | xxx.test.com | Package Name |


### **Application stop callbacks**
**Scenarios of use**

The client calls application stop and the application stop is notified to the client via this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 3 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete) |
| padCode | String | AC22030022001 | Instance Identifier |
| pkgName | String | xxx.test.com | Package Name |

### **Application restart callbacks**
**Scenarios of use**

The client invokes application restart and the application restart is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 3 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete) |
| padCode | String | 212254 | Instance Identifier |
| pkgName | String | xxx.test.com | Package Name |

### **Query instance application list callbacks**
**Scenarios of use**

The client calls the instance installed application list and the instance installed application list is notified to the client via this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 1 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete)|
| padCode | String | AC22030022001 |pod identifier|
| apps | Object[] | | |
| ├─appName | String | test | appName |
| ├─pkgName | String | text.xx.com | package name |


### **File upload callbacks**
**Scenarios of use**

The client calls to upload a file, and the uploaded file is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 3 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete)|
| originFileUrl | String | www.xxx.com/xxx.apk | originalFileDownloadAddress|
| fileUniqueId | String | test001 | fileid |

### **Instance state task callbacks**
**Scenarios of use**

The client calls instance restart/instance reset and the status of the instance restart/instance reset task is notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| taskBusinessType | Integer | 1 | taskBusinessType (1000: instance restart; 1001: instance reset) |
| taskId | Integer | 2 | TaskId |
| taskStatus | Integer | 3 | taskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: executing; 3: complete)|
| padCode | String | 212254 | Instance number |
| taskResult| String | Success| taskResult |
| taskContent| String | | TaskContent |
| endTime| Long | 177777224 | endTime |

### **Instance state callbacks**
**Scenarios of use**

Instance state changes are notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
|padStatus | Integer | 1 | Instance Status: 10-Running 11-Restarting 12-Resetting 13-Upgrading 14-Exception 15-Not Ready|
| padCode| String | 212254 | Instance Identifier |

### **Cloud machine status callbacks**
**Scenarios of use**

Changes in the state of the cloud machine are notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
|deviceStatus | Integer | 1 |PhysicalMachineStatus 0-offline; 1-online|
| deviceCode| String | 212254 | Cloud Machine ID |

### **Instance upgrade mirror callbacks**
**Scenarios of use**

Instance upgrades mirroring task state changes are notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| padCode| String | 212254 | instance number |
| taskBusinessType | Integer | 1 | taskBusinessType(1012 Upgrade Mirror) |
| taskStatus | Integer | 3 | TaskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete)|
| taskId | Integer | 1 | Task ID |
| taskContent | String | | taskContent |
| endTime | Long| 1756021166163| endTime |
| taskResult| String | Success| taskResult |

### **Application blacklisting task callbacks**
**Scenarios of use**

The instance sets up that application blacklisted task status changes are notified to the client through this callback interface.

| Fields | Types | Examples | Descriptions
| ------- | ------- | ----------- | ---------- |
| padCode| String | 212254 | instance number |
| taskId | Integer | 1 | Task ID |
| taskStatus | Integer | 3 | TaskStatus (-1: all failed; -3: cancelled; -4: timeout; 1: pending; 2: execution in progress; 3: complete)|

[//]: # (### **Instance Backup Callbacks**)

[//]: # (**Usage Scenarios**)

[//]: # ()
[//]: # (Changes in the status of the backup instance information task are notified to the client through this callback interface)

[//]: # ()
[//]: # (| field    | typology    | examples     | clarification                                        |)

[//]: # (| ------- | ------- |--------|-------------------------------------------|)

[//]: # (| padCode| String  | 212254 | Instance number                                      |)

[//]: # (| taskId | Integer  | 1      | Task ID                                      |)

[//]: # (| taskStatus | Integer | 3      | Task status &#40;-1: all failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed&#41; |)

[//]: # (| taskBusinessType | Integer | 1024   | Enumeration of task types                                    |)

[//]: # (### **Instance Restore Callbacks**)

[//]: # (**Usage Scenarios**)

[//]: # ()
[//]: # (Instance Restore callback task state changes are notified to the client through this callback interface。)

[//]: # ()
[//]: # (| field    | typology    | examples | clarification                                        |)

[//]: # (| ------- | ------- |----------|-------------------------------------------|)

[//]: # (| padCode| String  | 212254   | Instance number                                      |)

[//]: # (| taskId | Integer  | 1        | Task ID                                      |)

[//]: # (| taskStatus | Integer | 3        | Task status &#40;-1: all failed; -3: canceled; -4: timeout; 1: pending; 2: in progress; 3: completed&#41; |)

[//]: # (| taskBusinessType | Integer | 1025     | Enumeration of task types                                    |)