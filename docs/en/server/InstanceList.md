---
title: List of Instance Properties
order: 1
---

## Modem Properties Property List
> Configurable Properties in Android：
> - Non-persistent save: can be set via the modemPropertiesList parameter of the “updatePadProperties” interface (non-persistent save, takes effect immediately, expires after restarting the instance)
> - Persistence: can be set via the modemPersistPropertiesList parameter of the “updatePadProperties” interface (persistent, takes effect after restarting the instance)

### Persistent Calls
Example of a request
```json
"modemPersistPropertiesList":[{
  "propertiesName":"IMEI",
  "propertiesValue":"*********"
},
  {
    "propertiesName":"ICCID",
    "propertiesValue":"00998877"
  }]
```
### Non-Persistent Calls
Example of a request
```json
"modemPropertiesList":[{
  "propertiesName":"IMEI",
  "propertiesValue":"*********"
},
  {
    "propertiesName":"ICCID",
    "propertiesValue":"00998877"
  }]
```
### Property List
| causality（key） | attribute value（value） | Attribute Description
| :------------ | :------------ |  :------------ |
| IMEI | ********* | |
| ICCID | 00998877 | |
| IMSI | 4600112345 | |
| MCCMNC | 461,01 | |
| OpName | China Mobile (PRC state telecommunications company) | |
| PhoneNum | 861380013800 | | | 

##  System Properties
> Android Configurable properties in the system：
> - Non-persistent save: can be set via the systemPropertiesList parameter of the “updatePadProperties” interface (non-persistent save, takes effect immediately, expires after restarting the instance)
> - Persistence saving: can be set via the systemPersistPropertiesList parameter of the “updatePadProperties” interface (persistence saving, effective after restarting the instance)

### Persistent Calls
Example of a request
```json
"systemPersistPropertiesList":[{
  "propertiesName":"ro.product.manufacturer",
  "propertiesValue":"XIAOMI"
},
  {
    "propertiesName":"ro.product.brand",
    "propertiesValue":"XIAOMI"
  }]
```
### Non-Persistent Calls
```json
Example of a request
"systemPropertiesList":[{
  "propertiesName":"ro.product.manufacturer",
  "propertiesValue":"XIAOMI"
},
  {
    "propertiesName":"ro.product.brand",
    "propertiesValue":"XIAOMI"
  }]
```
### Property List
#### Common Properties
| Attribute (key) | Attribute Value (value) | Attribute Description |
| :------------ | :------------ | :------------ |
|ro.product.manufacturer | Example: HW | manufacturer |
|ro.product.brand | Example: HW | brand |
|ro.product.model | Example: LYA_AL00 | Model
|ro.build.id | 0 | Build Tags
|ro.build.display.id | 0 | Build version number
|ro.product.name | Example: LYA_AL00 | Product Name
|ro.product.device | Example: HWLYA | Device Information
|ro.product.board | Example: LYA | Motherboard Name
|ro.build.tags | Example: dev-keys/release-keys | development key/release key
|ro.build.fingerprint | 0 | System Fingerprints
|ro.build.date.utc | 0 | firmware build timestamps
|ro.build.user | 0 | Firmware build user
|ro.build.host | 0 | Firmware build host
|ro.build.description | 0 | Firmware build description information
|ro.build.version.incremental | 0 | internal version number
|ro.build.version.codename | 0 | codename

#### Other Properties
##### Open attributes for specified prefixes 
> "ro.build.", 
"ro.product.", 
"ro.odm.", 
"ro.vendor.", 
"ro.system_ext.", 
"ro.system.", 
"ro.com.", 
"ro.config." 

But not all of them are open: some of them are not suitable for opening, and are maintained in the blacklist below
In addition, some genera not prefixed with the above are also open, and are maintained in the whitelist below.

##### Blacklist of unmodifiable attributes
> "ro.build.type", 
"ro.build.vername", 
"ro.build.version.release", 
"ro.build.version.sdk", 
"ro.build.version.name", 
"ro.product.cpu.abi", 
"ro.product.cpu.abilist", 
"ro.product.cpu.abilist32", 
"ro.product.cpu.abilist64", 
"ro.odm.build.type", 
"ro.odm.build.version.release", 
"ro.odm.build.version.sdk", 
"ro.odm.product.cpu.abilist", 
"ro.odm.product.cpu.abilist32", 
"ro.odm.product.cpu.abilist64", 
"ro.vendor.build.type", 
"ro.vendor.build.version.release", 
"ro.vendor.build.version.sdk", 
"ro.vendor.product.cpu.abilist", 
"ro.vendor.product.cpu.abilist32", 
"ro.vendor.product.cpu.abilist64", 
"ro.system.build.type", 
"ro.system.build.version.release", 
"ro.system.build.version.sdk"

##### Whitelist of Modifiable Attributes
> "ro.board.platform", 
"ro.bootimage.build.fingerprint", 
"ro.baseband", 
"ro.boot.wificountrycode", 
"ro.bootimage.build.date", 
"ro.bootimage.build.date.utc", 
"ro.gfx.driver.0", 
"ro.revision", 
"ro.ril.svdo", 
"ro.ril.svlte1x", 
"ro.serialno", 

#### Analog SIM
| Attribute (key) | Attribute Value (value) | Attribute Description |
| :------------ | :------------ | :------------ |
| aic.sim.state | Example: 5 | 0;1: No SIM card;2: SIM_STATE_NETWORK_LOCKED;3: SIM card PIN code locked;4: SIM card PUK code locked;5: SIM card normal | | aic.operator.shortname | aic.operator.shortname | aic.operator.shortname | aic.operator.shortname | aic.operator.shortname | aic.operator.shortname | aic.operator.shortname | aic.sim.shortname
| aic.operator.shortname | Example: CMCC | operator shortname
| aic.operator.numeric | Example: 46001 | network operator id (i.e. MCCMNC)
| aic.spn | Example: China Mobile | SIM card operator name
| aic.iccid | Example: 89860002191807255576 | SIM card number
| aic.imsi | Example: *************** | Prefixed with SIM card operator number: MCC (3 digits) + MNC (2 or 3 digits)
| aic.phonenum | Example: 18629398873 | Phone number
| aic.net.country | Example: CHINA | Network Country
| aic.sim.country | Example: CHINA | SIM Card Country
| aic.signal.strength | 例：{\"cdmaDbm\"=0,\"cdmaEcio\"=1,\"evdoDbm\"=2,\"evdoEcio\"=3,\"evdoSnr\"=4,\"rssi\"=-51,\"asulevel\"=30,\"ber\"=0,\"ta\"=0,\"rscp\"=-51,\"ecNo\"=10,\"rsrp\"=1,\"rsrq\"=43,\"rssnr\"=300,\"cqi\"=15,\"csiRsrp\"=-44,\"csiRsrq\"=-3,\"csiSinr\"=23,\"csiCqiTableIndex\"=0,\"ssRsrp\"=-44,\"ssRsrq\"=-3,\"ssSinr\"=40,\"parametersUseForLevel\"=22} | 信号强度
| aic.deviceid | precedent：370483496 | electronic serial number
| aic.cellinfo | precedent：{\"lac\"=4049,\"cid\"=1463,\"sid\"=149,\"arfcn\"=arfcn,\"bsic\"=133,\"alphal\"=\"\",\"alphas\"=\"CMCC\",\"psc\"=11,\"ci\"=11,\"psc\"=11,\"pci\"=22,\"tac\"=33, \"earfcn\"=44,\"bandwidth\"=144} | 不同模式下基站信息生效字段：GSM：int lac, int cid, int arfcn, int bsic, String mccStr,String mncStr, String alphal, String alphasCDMA：int lac, int cid, int psc, int uarfcn, String mccStr, String mncStr, String alphal, String alphasLTE：int mcc, int mnc, int ci, int pci, int tac,String mccStr, String mncStr, String alphal, String alphasNR:int csiRsrp, int csiRsrq, int csiSinr, int ssRsrp, int ssRsrq, int ssSinr #其中 mccStr,mncStr,mcc,mnc 将从 modem/aic.operator.numeric 获取，此处不用传
| aic.net.type | precedent：13 | Data network type: gsm/lte/cdma (telecom), lte by default  ## 0-20;NETWORK_TYPE_CDMA=4 ;NETWORK_TYPE_LTE=13 ;NETWORK_TYPE_GSM=16 ;NETWORK_TYPE_NR=20 #5G
| aic.radio.type | precedent：13 | Voice network type: gsm/lte/cdma(Telecom), default is lte(VoLTE)  ;NETWORK_TYPE_CDMA=4 ;NETWORK_TYPE_LTE=13 ;NETWORK_TYPE_GSM=16 ;NETWORK_TYPE_NR=20 #5G
| aic.gid1 | precedent：FF | GroupLevel1 The exact meaning depends on the operator's definition and may represent different services or specific features.
| aic.alphatag | precedent：abcdefg | A string of characters stored on the SIM card, usually used to represent the name or brand of the mobile network operator
| aic.nai | precedent：abcdefg | NAI is a string used to identify a device in a mobile network

##### Signal strength parameters in effect for different modes 

CDMA:int cdmaDbm, int cdmaEcio, int evdoDbm, int evdoEcio, int evdoSnr
GSM: int rssi, int ber, int ta
WCDMA:int rssi, int ber, int rscp, int ecno
TDSCDMA：int rssi, int ber, int rscp
LTE:int rssi, int rsrp, int rsrq, int rssnr, int cqi, int timingAdvance
NR: int csiRsrp, int csiRsrq, int csiSinr, int ssRsrp, int ssRsrq, int ssSinr
##### Signal strength value range reference
 //cdma 
public int cdmaDbm; // This value is the RSSI value 
public int cdmaEcio; // This value is the Ec/Io 
public int evdoDbm; // This value is the EVDO RSSI value 
public int evdoEcio; // This value is the EVDO Ec/Io 
public int evdoSnr; // Valid values are 0-8. 8 is the highest signal to noise ratio 
//public int level; 

//gsm 
public int rssi; // in dBm [-113, -51] or UNAVAILABLE 
public int ber; // bitErrorRate; // bit error rate (0-7, 99) TS 27.007 8.5 or UNAVAILABLE 
public int ta; // timingAdvance; // bit error rate (0-7, 99) TS 27.007 8.5 or UNAVAILABLE 


//wcdma 
public int rscp; // in dBm [-120, -24] 
public int ecno; // range -24, 1, CellInfo.UNAVAILABLE if unknown 


//lte 
//public int rssi; // in dBm [-113,-51], UNKNOWN 
public int rsrp; // in dBm [-140,-43], UNKNOWN
public int rsrq; // in dB [-20,-3], UNKNOWN 
public int rssnr; // in 10*dB [-200, +300], UNKNOWN 
public int cqi; // [0, 15], UNKNOWN 
//public int ta; // [0, 1282], UNKNOWN 

//Nr 
public int csiRsrp; // [-140, -44], UNKNOWN 
public int csiRsrq; // [-20, -3], UNKNOWN 
public int csiSinr; // [-23, 23], UNKNOWN 

public int csiCqiTableIndex; 
public List mCsiCqiReport; 
public int ssRsrp; // [-140, -44], UNKNOWN 
public int ssRsrq; // [-20, -3], UNKNOWN 
public int ssSinr; // [-23, 40], UNKNOWN 

public int mParametersUseForLevel;

## Setting Properties
Example of a request
```json
"settingPropertiesList":[{
  "propertiesName":"ssaid/com.demo1",
  "propertiesValue":"2345243531"
},
  {
    "propertiesName":"ssaid/com.demo2",
    "propertiesValue":"123456789"
  },
  {
    "propertiesName":"language",
    "propertiesValue":"zh-CN"
  }]
```
### Property List
| Attribute (key) | Attribute Value | Attribute Description
| :------------ | :------------ | :------------ |
| ssaid/com.cheersucloud.cimi.sample | ********* | Android id |
| bt/mac | 1A:75:FF:88:2A:06 | bluetooth mac |
| language | zh-CN | system language |
| timezone | Asia/Shanghai | systemvolume | 10
| systemvolume | 10 | Fixed media volume, desirable range 0-15 |

## OAID Properties
Example of a request
```json
"oaidPropertiesList":[{
  "propertiesName":"UDID",
  "propertiesValue":"111111111"
},
  {
    "propertiesName":"OAID",
    "propertiesValue":"123456789"
  },
  {
    "propertiesName":"language",
    "propertiesValue":"zh-CN"
  }]
```
### Property List
| Attribute (key) | Attribute Value (value) | Attribute Description |
| :------------ | :------------ | :------------ |
| UDID | 11111111 | UDID is a unique identifier for iOS devices, each iOS device has a unique UDID, but as of 2018, Apple has disabled developer access to UDIDs in favor of Vendor IDs (Vender Identifier) | OAID | 221111 | OAID | 221111 | OAID | 221111 | OAID | 221111
| OAID | 22222222 | is an anonymous identifier for Android devices, developed and promoted by the China Mobile Internet Industry Alliance (CCIA) to replace the device ID (IMEI) and Android ID (the unique identifier of the Android system), and to solve the problems of cross-application cross-platform accurate placement of mobile advertisements and the protection of user privacy. | VAID | 3
| VAID | 3333333333 | VAID is a manufacturer's advertising identifier for Android devices, which is provided by the device manufacturer. VAID can be used for cross-application and cross-platform advertisement delivery, and unlike OAID, VAID is not an anonymous identifier. VAID | AAID | 44444
| AAID | 4444444444 | is a unique identifier provided by the Google Play Services Framework for precise cross-platform delivery of ads across apps, and is also designed to protect user privacy. Users can reset their AAID at any time to disable apps from accessing their AAID. | | AAID | 4444444444 | AAID
