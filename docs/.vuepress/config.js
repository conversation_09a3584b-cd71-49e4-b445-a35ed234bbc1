import { viteBundler } from "@vuepress/bundler-vite";
import { defaultTheme } from "@vuepress/theme-default";
import { defineUserConfig } from "vuepress";
import { watermarkPlugin } from "@vuepress/plugin-watermark";
import vuepressPluginStarReplace from "./vuepress-plugin-star-replace";
export default defineUserConfig({
  lang: "zh-CN",
  title: "ARMCLOUD开发者文档中心",

  markdown: {
    headers: {
      level: [2, 3, 4],
    },
  },

  head: [["link", { rel: "icon", href: "/favicon.ico" }]],
  plugins: [
    watermarkPlugin({
      watermarkOptions: {
        content: "ARMCLOUD",
        letterSpacing: "2px",
        monitorProtection: true,
        width: 200,
        height: 200,
      },
    }),
    vuepressPluginStarReplace,
  ],
  locales: {
    "/": {
      lang: "zh-CN",
      title: "ARMCLOUD 文档中心",
    },
    "/en/": {
      lang: "en-US",
      title: "ARMCLOUD Documentation Center",
    },
  },
  theme: defaultTheme({
    sidebarDepth: 4,
    contributors: false,
    lastUpdated: false,
    locales: {
      "/": {
        selectLanguageText: "简体中文",
        selectLanguageName: "简体中文",
        sidebar: [
          {
            text: "产品介绍",
            collapsible: true,
            link: "/cn/basic/",
          },
          {
            text: "产品计费",
            collapsible: true,
            link: "/cn/billing/",
          },
          // {
          //   text: "快速入门",
          //   collapsible: true,
          //   link: "/cn/quickstart/",
          // },
          {
            text: "服务端 OpenAPI",
            collapsible: true,
            link: "/cn/server/ChangeLog.md",
            children: [
              "/cn/server/ChangeLog.md",
              "/cn/server/UsageGuide.md",
              "/cn/server/OpenAPI_Sign.md",
              "/cn/server/OpenAPI.md",
              "/cn/server/ErrorMsgCode.md",
              "/cn/server/CallbackTaskBusinessTypeCode.md",
              "/cn/server/InstanceList.md",
              "/cn/server/InstanceAndroidPropList.md",
            ],
          },
          {
            text: "Android端 SDK",
            link: "/cn/client/android/example.md",
            collapsible: true,
            children: [
              "/cn/client/android/example.md",
              "/cn/client/android/android-sdk.md",
              "/cn/client/android/callback.md",
              "/cn/client/android/errorCode.md",
              "/cn/client/android/log.md",
            ],
          },
          {
            text: "Web H5端 SDK",
            link: "/cn/client/h5/example.md",
            collapsible: true,
            children: [
              "/cn/client/h5/example.md",
              "/cn/client/h5/h5-sdk.md",
              "/cn/client/h5/callback.md",
              "/cn/client/h5/errorCode.md",
              "/cn/client/h5/log.md",
            ],
          },
          {
            text: "Windows PC端 SDK",
            link: "/cn/client/pc/example.md",
            collapsible: true,
            children: [
              "/cn/client/pc/example.md",
              "/cn/client/pc/pc-sdk.md",
              "/cn/client/pc/callback.md",
              "/cn/client/pc/log.md",
            ],
          },
          {
            text: "端侧与云机通信开发",
            collapsible: true,
            link: "/cn/system/SystemServe.md",
            children: ["/cn/system/SystemServe.md", "/cn/system/SystemApi.md"],
          },
          {
            text: "类XP、LSP Hook框架",
            collapsible: true,
            link: "/cn/frame/XPose.md",
          },
          {
            text: "相关协议",
            collapsible: true,
            link: "/cn/agreement/PrivacyAgreement.md",
            children: ["/cn/agreement/PrivacyAgreement.md"],
          },
        ],
      },
      "/en/": {
        selectLanguageText: "English",
        selectLanguageName: "English",
        sidebar: [
          {
            text: "Product introduction",
            collapsible: true,
            link: "/en/basic/",
          },
          {
            text: "Product billing",
            collapsible: true,
            link: "/en/billing/",
          },
          // {
          //   text: "Quick start",
          //   collapsible: true,
          //   link: "/en/quickstart/",
          // },
          {
            text: "Server OpenAPI",
            collapsible: true,
            link: "/en/server/OpenAPI.md",
            children: ["/en/server/OpenAPI.md", "/en/server/InstanceList.md"],
          },
          {
            text: "Android SDK",
            link: "/en/client/android/example.md",
            collapsible: true,
            children: [
              "/en/client/android/example.md",
              "/en/client/android/android-sdk.md",
              "/en/client/android/callback.md",
              "/en/client/android/errorCode.md",
              "/en/client/android/log.md",
            ],
          },
          {
            text: "Web H5 SDK",
            link: "/en/client/h5/example.md",
            collapsible: true,
            children: [
              "/en/client/h5/example.md",
              "/en/client/h5/h5-sdk.md",
              "/en/client/h5/callback.md",
              "/en/client/h5/errorCode.md",
              "/en/client/h5/log.md",
            ],
          },
          {
            text: "Windows PC SDK",
            link: "/en/client/pc/example.md",
            collapsible: true,
            children: [
              "/en/client/pc/example.md",
              "/en/client/pc/pc-sdk.md",
              "/en/client/pc/callback.md",
              "/en/client/pc/log.md",
            ],
          },
          {
            text: "Edge-Cloud Communication Development",
            collapsible: true,
            link: "/en/system/SystemServe.md",
            children: ["/en/system/SystemServe.md"],
          },
          {
            text: "Similar to XP, LSP Hook framework",
            collapsible: true,
            link: "/en/frame/XPose.md",
          },
          {
            text: "Related agreements",
            collapsible: true,
            link: "/en/agreement/PrivacyAgreement.md",
            children: ["/en/agreement/PrivacyAgreement.md"],
          },
        ],
      },
    },
  }),

  bundler: viteBundler(),
});
